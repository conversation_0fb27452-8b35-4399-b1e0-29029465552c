<mf-gestions-receipt
  *axLazyElement
  [setEmailValueAsEmpty]="!isSelfPerson"
  [alertTitle]="alertTitle | translate"
  [alertSubtitle]="alertSubtitle | translate"
  [emailLabel]="emailLabel | translate"
  [idAddress]="idAddress"
  [csvAddress]="csvAddress"
  [idContactData]="idContactData"
  [csvContactData]="csvContactData"
  [displayCsvAddressLabelAsText]="true"
  [displayCsvContactDataAsText]="true"
  [resourceProcessReceiptName]="resourceProcessReceiptName"
  [resourceProcessReceiptId]="resourceProcessReceiptId"
>
  <p
    [innerHTML]="'SE_PADRO_CO2.PRESENTATION_RECEIPT.ASTERISK.MSG_1' | translate"
  ></p>
  <p
    *ngIf="showMsg2Asterisk"
    [innerHTML]="'SE_PADRO_CO2.PRESENTATION_RECEIPT.ASTERISK.MSG_2' | translate"
  ></p>
</mf-gestions-receipt>

<div class="mt-4" *ngIf="showPanel">
  <se-panel [title]="panelTitle">
    <div class="d-flex align-items-center mb-4" *ngIf="rebutsPendents">
      <p class="mb-0 me-3">
        <strong>
          {{
            'SE_PADRO_CO2.PRESENTATION_RECEIPT.PENDING_PAYMENT_PANEL.REMINDER'
              | translate
          }}
        </strong>
      </p>
      <se-tag [tagTheme]="'warning'" [closable]="false">
        {{
          'SE_PADRO_CO2.PRESENTATION_RECEIPT.PENDING_PAYMENT_PANEL.TAG'
            | translate
        }}
      </se-tag>
    </div>
    <p [innerHTML]="panelSubtitle | translate"></p>
  </se-panel>
</div>

<div class="presentation-receipt__survey-container"></div>

<div class="d-flex flex-column flex-sm-row justify-content-sm-end mt-4">
  <se-button type="submit" btnTheme="primary" (onClick)="goToList()">
    {{ 'SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate }}
  </se-button>
</div>
