import { Component, Input, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  DownloadExcelEndpointService,
  DownloadExcelFields,
  DownloadExcelRequest,
  type DownloadExcelRequestBody,
} from '@app/shared/services/download-excel-service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  Nullable,
  SeModal,
  SeModalOutputEvents,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-download-excel-modal',
  templateUrl: './download-excel-modal.component.html',
})
export class DownloadExcelModalComponent implements OnInit {
  @Input() data: SeModal | undefined;
  @Input() request: Nullable<DownloadExcelRequestBody>;
  @Input() selectedPlates: string[] = [];

  readonly CHECKBOX_NAMES = {
    selectedElements: 'selectedElements',
    allElements: 'allElements',
  };

  downloadExcelForm: FormGroup = new FormGroup({});

  constructor(
    private activatedModalService: NgbActiveModal,
    private downloadExcelEndpointService: DownloadExcelEndpointService,
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    this.downloadExcelForm = new FormGroup({
      selectedElements: new FormControl({
        value: false,
        disabled: this.selectedPlates.length === 0,
      }),
      allElements: new FormControl({
        value: true,
        disabled: this.selectedPlates.length === 0,
      }),
      plate: new FormControl({
        value: true,
        disabled: false,
      }),
      car: new FormControl({
        value: true,
        disabled: false,
      }),
      domiciled: new FormControl({
        value: true,
        disabled: false,
      }),
      situation: new FormControl({
        value: true,
        disabled: false,
      }),
      new: new FormControl({
        value: true,
        disabled: false,
      }),
      shares: new FormControl({
        value: true,
        disabled: false,
      }),
      taxYear: new FormControl({
        value: true,
        disabled: false,
      }),
    });
  }

  closeModal(event?: string): void {
    if (event === SeModalOutputEvents.MAIN_ACTION) this.downloadExcel();
    this.activatedModalService.close();
  }

  onCheckboxClick(event: boolean, checkboxName: string): void {
    if (checkboxName === this.CHECKBOX_NAMES.selectedElements && event) {
      this.downloadExcelForm.patchValue({ allElements: false });
      this.downloadExcelForm
        .get(this.CHECKBOX_NAMES.allElements)
        ?.addValidators(Validators.required);
    }
    if (checkboxName === this.CHECKBOX_NAMES.allElements && event) {
      this.downloadExcelForm.patchValue({ selectedElements: false });
      this.downloadExcelForm
        .get(this.CHECKBOX_NAMES.selectedElements)
        ?.addValidators(Validators.required);
    }
  }

  downloadExcel(): void {
    const downloadRequest: DownloadExcelRequest = {
      headers: { fields: this.fields },
      body: {
        ...this.request,
        filter: {
          ...this.request?.filter,
          listMatriculas: this.listMatriculas,
        },
      },
    };

    this.downloadExcelEndpointService.downloadExcel(downloadRequest);
  }

  private get fields(): string[] {
    return Object.keys(this.downloadExcelForm.controls)
      .filter(
        (key) =>
          this.downloadExcelForm.get(key)?.value &&
          key !== this.CHECKBOX_NAMES.selectedElements &&
          key !== this.CHECKBOX_NAMES.allElements,
      )
      .map(
        (key) => DownloadExcelFields[key as keyof typeof DownloadExcelFields],
      );
  }

  get listMatriculas(): string[] {
    return this.isDownloadSelectedElementsCheckboxChecked
      ? this.selectedPlates
      : [];
  }

  get isDownloadSelectedElementsCheckboxChecked(): boolean {
    return !!this.downloadExcelForm.get(this.CHECKBOX_NAMES.selectedElements)
      ?.value;
  }
}
