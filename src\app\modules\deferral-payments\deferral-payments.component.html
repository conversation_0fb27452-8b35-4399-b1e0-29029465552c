<section class="header-content mb-4">
  <h1 class="header-content__title">
    {{ 'SE_PADRO_CO2.DEFERRAL_PAYMENTS_PROCESS.TITLE' | translate }}
  </h1>
</section>
<div class="deferral-payments-resume mb-2">
  <se-panel
    [title]="'SE_PADRO_CO2.DEFERRAL_PAYMENTS_PROCESS.RESUM_TITLE' | translate"
    [colapsible]="false"
    [collapsed]="false"
    [panelTheme]="'primary'"
    [tooltip]="false"
    class="panel-0-padding"
  >
    <se-table
      *ngIf="vehiclesRows.length; else noDataTable"
      [columns]="vehiclesColumns"
      [data]="vehiclesRows"
      [styleClass]="'border-table'"
      [resizable]="false"
      [cellTemplatePriorityOrder]="'row-column-cell'"
    ></se-table>
    <div class="d-flex justify-content-end resume-payment">
      <span>{{
        'SE_PADRO_CO2.PAYMENTS_PROCESS.TABLE.TOTAL_INGRESSAR' | translate
      }}</span>
      <span class="resume-payment__total-amount">{{
        totalAmount | currency: 'EUR'
      }}</span>
    </div>
  </se-panel>
  <div class="mt-4">
    <app-civil-servant-required-doc-block
      [idEntity]="idEntity"
      [idFunctionalModule]="idFunctionalModule"
      [plates]="plates"
      [authAjornament]="true"
      (disableContinueButton)="onDisableContinueButton($event)"
    >
    </app-civil-servant-required-doc-block>
  </div>
  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button (onClick)="goBack()" [btnTheme]="'secondary'">{{
      'SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate
    }}</se-button>

    <se-button [disabled]="disableContinueButton" (onClick)="onContinue()">{{
      'SE_PADRO_CO2.BUTTONS.CONTINUE' | translate
    }}</se-button>
  </div>
</div>

<ng-template #noDataTable>
  <se-empty-state [icon]="'info'" [backgroundTheme]="'primary'" />
</ng-template>
