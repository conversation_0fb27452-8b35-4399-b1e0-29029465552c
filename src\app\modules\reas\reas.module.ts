import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonModule,
  SeLinkModule,
  SePanelModule,
  SeTableModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';

import { ReasRequestComponent } from './rea-request/reas-request.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { SummaryReasComponent } from './summary-reas/summary-reas.component';
import { SharedModule } from 'primeng/api';
import { RequestSummaryModule } from '@shared/components';
import { LazyElementsModule } from '@angular-extensions/elements';
import { environment } from '@environments/environment';
import { CivilServantRequiredDocBlockModule } from '@app/shared/components/civil-servant-required-doc-block';
import { NgIconsModule } from '@ng-icons/core';
import { matLaunchOutline } from '@ng-icons/material-icons/outline';
import { AllegationsFreeTextModule } from '@app/shared/components/allegations-free-text/allegations-free-text.module';
import { AccountNumberSummaryModule } from '@app/shared/components/account-number-summary';
import { NotificationsSummaryModule } from '@app/shared/components/notifications-summary';

const routes: Routes = [
  {
    path: '',
    component: ReasRequestComponent,
    data: {
      title: 'SE_PADRO_CO2.ALLEGATIONS.TITLE',
      stepId: 'REAS_STEP1',
      stepperId: 'REAS_STEPS',
    },
  },
  {
    path: 'notificacions',
    component: NotificationsComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      stepId: 'REAS_STEP2',
      stepperId: 'REAS_STEPS',
    },
  },
  {
    path: 'resum',
    component: SummaryReasComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      stepId: 'REAS_STEP3',
      stepperId: 'REAS_STEPS',
    },
  },
];

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    NgIconsModule.withIcons({ matLaunchOutline }),
    RouterModule.forChild(routes),
    TranslateModule,
    SeButtonModule,
    SePanelModule,
    SeTableModule,
    SeAlertModule,
    SeLinkModule,
    SeAlertModule,
    RequestSummaryModule,
    AllegationsFreeTextModule,
    AccountNumberSummaryModule,
    NotificationsSummaryModule,
    CivilServantRequiredDocBlockModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-pagaments-dades-bancaries',
          url: environment.wcUrlPagamentsJs,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
        {
          tag: 'mf-contribuent-notificacions',
          url: environment.wcUrlContribuentJs,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
  ],
  declarations: [
    ReasRequestComponent,
    NotificationsComponent,
    SummaryReasComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ReasModule {}
