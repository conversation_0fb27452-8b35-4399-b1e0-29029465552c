<se-modal
  [data]="data"
  [closableLabel]="'UI_COMPONENT.BUTTONS.CLOSE'"
  (modalOutputEvent)="closeModal()"
  (modalSecondaryButtonEvent)="closeModal()"
>
  <ng-container *ngIf="isFormula; else noFormula">
    <p>
      {{ 'SE_PADRO_CO2.MODAL.DETAIL.NO_OFFICIAL_EMISSIONS' | translate }}
    </p>
    <p
      [innerHTML]="
        'SE_PADRO_CO2.MODAL.DETAIL.FORMULA_APPLIED'
          | translate: { fuel: formulaFuel, category: carDetail?.categoria }
      "
    ></p>
    <!-- TODO preguntar por estos numeros y letras -->
    <div class="grey-back mt-4">
      <div class="d-flex justify-content-center gap-1">
        <ng-container *ngFor="let concept of concepts">
          <div
            *ngIf="concept.operation"
            class="d-flex flex-column justify-content-end"
          >
            <p>{{ concept.operation }}</p>
          </div>
          <div class="d-flex flex-column justify-content-end">
            <strong *ngIf="concept.title" class="text-center">{{
              concept.title
            }}</strong>
            <p *ngIf="concept.constant">
              [{{ concept.constant }} x
              {{ concept.value | number: '1.0-3' : 'ca-Es' }}]
            </p>
            <p *ngIf="!concept.constant">
              {{ concept.value | number: '1.0-3' : 'ca-Es' }}
            </p>
          </div>
        </ng-container>
      </div>
    </div>
    <p class="mt-4 text-end">
      <strong
        [innerHTML]="
          'SE_PADRO_CO2.MODAL.DETAIL.CO2_EMISSIONS'
            | translate
              : { co2Emissions: co2Emissions | number: '1.2-2' : 'ca-Es' }
        "
      ></strong>
    </p>
    <p class="mt-2">
      {{ 'SE_PADRO_CO2.MODAL.DETAIL.FORMULA_CONCEPTS' | translate }}
    </p>
    <div class="border">
      <se-table
        [selectable]="false"
        [columns]="tableColumns"
        [data]="tableData"
        [resizable]="true"
        [cellTemplatePriorityOrder]="'row-column-cell'"
      ></se-table>
    </div>
    <p class="mt-4">
      {{ 'SE_PADRO_CO2.MODAL.DETAIL.L_ESTIMATION' | translate }}
    </p>
    <p>
      {{ 'SE_PADRO_CO2.MODAL.DETAIL.ESTIMATION_RANGE' | translate }}
    </p>
  </ng-container>

  <ng-template #noFormula>
    <p
      [innerHTML]="'SE_PADRO_CO2.MODAL.DETAIL.CIRCUMSTANCES_DGT' | translate"
    ></p>
  </ng-template>
</se-modal>
