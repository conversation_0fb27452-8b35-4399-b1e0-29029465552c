import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeCheckboxModule,
  SeLinkModule,
  SePanelModule,
} from 'se-ui-components-mf-lib';

import { AccountNumberSummaryComponent } from './account-number-summary.component';
import { IbanFormatterPipe } from '@app/shared/pipes/iban-formatter';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [AccountNumberSummaryComponent],
  exports: [AccountNumberSummaryComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SePanelModule,
    SeLinkModule,
    SeCheckboxModule,
    IbanFormatterPipe,
    FormsModule,
    ReactiveFormsModule,
  ],
})
export class AccountNumberSummaryModule {}
