import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SeModalModule, SeTableModule } from 'se-ui-components-mf-lib';
import { TaxPeriodModalComponent } from './tax-period-modal.component';

@NgModule({
  declarations: [TaxPeriodModalComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SeModalModule,
    SeTableModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class TaxPeriodModalModule {}
