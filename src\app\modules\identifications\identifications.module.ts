import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule, DatePipe } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { environment } from '@environments/environment';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonModule,
  SeCheckboxModule,
  SeConfirmationMessageModule,
  SeHighlightRadioContainerModule,
  SeInputModule,
  SePanelModule,
  SeRadioModule,
  SeUserInfoBarModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { ConveniatIdentificationComponent } from './conveniat-identification/conveniat-identification.component';
import { CivilServantIdentificationComponent } from './civil-servant-identification/civil-servant-identification.component';
import { CoordinatorIdentificationComponent } from './coordinator-identification/coordinator-identification.component';
import { IdentificationsComponent } from './identifications.component';
import { PersonalIdentificationComponent } from './personal-identification/personal-identification.component';

const routes: Routes = [
  {
    path: '',
    component: IdentificationsComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      isElementVisible: false,
    },
  },
  {
    path: 'representant',
    component: IdentificationsComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      isElementVisible: false,
    },
  },
  {
    path: '**',
    redirectTo: '',
  },
];

@NgModule({
  declarations: [
    IdentificationsComponent,
    CoordinatorIdentificationComponent,
    CivilServantIdentificationComponent,
    ConveniatIdentificationComponent,
    PersonalIdentificationComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    SeButtonModule,
    SeRadioModule,
    SeConfirmationMessageModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-seguretat-declaracio-responsable',
          url: environment.mfSeguretatURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
        {
          tag: 'mf-documents-upload-files',
          url: environment.wcUrlDocumentsJs,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
        {
          tag: 'mf-seguretat-scoring',
          url: environment.mfSeguretatURL,
          loadingComponent: SpinnerComponent,
        },
      ],
    }),
    SeUserInfoBarModule,
    SeInputModule,
    SeCheckboxModule,
    SeHighlightRadioContainerModule,
    SeAlertModule,
    SePanelModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [DatePipe],
})
export class IdentificationModule {}
