import { Nullable, SeTagTheme } from 'se-ui-components-mf-lib';

export interface DetailElement {
  title?: string;
  description?: string;
  tooltipText?: string;
  linkText?: string;
  tagLabel?: string;
  tagTheme?: SeTagTheme;
  bold?: boolean;
  linkCommand?: Nullable<() => unknown>;
}

export enum DetailAlertsEnum {
  PROVISIONAL_DOMICILED = 'PROVISIONAL_DOMICILED',
  PROVISIONAL_NO_DOMICILED_IN_TIME_TO_BE_DOMICILED = 'PROVISIONAL_NO_DOMICILED_IN_TIME_TO_BE_DOMICILED',
  PROVISIONAL_NO_DOMICILED_OUT_OF_TIME_TO_DOMICILED = 'PROVISIONAL_NO_DOMICILED_OUT_OF_TIME_TO_DOMICILED',
  DOMICILED = 'DOMICILED',
  PENDING_PAYMENT = 'PENDING_PAYMENT',
  EXECUTIVE = 'EXECUTIVE',
  CANCELED = 'CANCELED',
}

export interface TagDetailPadro {
  text: string;
  color: SeTagTheme;
}
