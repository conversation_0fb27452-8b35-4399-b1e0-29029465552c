<form [formGroup]="allegationsForm">
  <!-- Allegations -->
  <se-panel
    class="allegations-panel"
    [id]="'allegations-free-textarea-panel'"
    [title]="allegationsPanelData.title | translate"
    [colapsible]="allegationsPanelData.colapsible!"
  >
    <span [innerHTML]="panelSubtitle | translate"></span>
    <div class="text-area">
      <se-textarea
        [id]="'allegations-free-textarea'"
        [formControlName]="FREE_TEXT_BLOCK_CONTROL_NAME"
        [maxlength]="1000"
        [label]="freeTextAreaLabel | translate"
        [readonly]="false"
      >
      </se-textarea>
    </div>
    <mf-documents-upload-files
      *axLazyElement
      [id]="'noReason'"
      [hasActions]="true"
      [statusesToExclude]="statusesToExclude"
      [sigedaDescriptions]="documentsSigedaDescriptions"
      [key]="DocSubtypeCodes.RECLAMACIO_JUSTIFICANT"
      [required]="isDocumentRequired"
      [idFunctionalModule]="functionalModule"
      [accept]="documentsAcceptedTypes"
      [sizeLimitPerFile]="documentsSizeLimit"
      [idEntity]="procedureId"
      [tableColumns]="getDocumentsTableColumns()"
      [modalTableColumns]="getModalTableColumns()"
      [hasModal]="true"
      [multiple]="false"
      [maxFiles]="1"
      [subtitleText]="docSectionSubtitle | translate: { format, fileSize }"
      [dropAreaTitlePreLinkText]="
        'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.DOC_SECTION.PRELINK_TEXT'
          | translate
      "
      [dropAreaTitleLinkText]="
        'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.DOC_SECTION.LINK_TEXT' | translate
      "
      [title]="docSectionTitle | translate"
      (addedFiles)="onAllegationsAddedFiles($event)"
    ></mf-documents-upload-files>
  </se-panel>
</form>
