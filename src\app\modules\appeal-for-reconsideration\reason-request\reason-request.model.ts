import { DocumentsUpload, Motius, type Vehicle } from '@app/shared/models';

export interface RequestUpdateResourcesTramit {
  motius: Motius[];
  ibanNumeroCompte?: string;
  declaracioNumeroCompte?: boolean;
  documentNumeroCompte?: DocumentsUpload;
}

export interface RecursResumResponse {
  titular?: string;
  quota?: number;
  ibanNumeroCompte?: string;
  motius?: Motius[];
  vehicles?: Vehicle[];
  declaracioNumeroCompte?: boolean;
}
