import { LazyElementsModule } from '@angular-extensions/elements';
import { DOCUMENT, registerLocaleData } from '@angular/common';
import {
  HTTP_INTERCEPTORS,
  HttpBackend,
  HttpClientModule,
} from '@angular/common/http';
import localeCa from '@angular/common/locales/ca';
import {
  APP_INITIALIZER,
  ApplicationRef,
  DEFAULT_CURRENCY_CODE,
  DoBootstrap,
  Injector,
  LOCALE_ID,
  NgModule,
  isDevMode,
} from '@angular/core';
import { createCustomElement } from '@angular/elements';
import { BrowserModule } from '@angular/platform-browser';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { environment } from '@environments//environment';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import {
  SeExceptionViewerModule,
  SeHeaderInfoModule,
  SeHttpInterceptorService,
  SeStepperdModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';
import { HelpModalModule } from '@shared/components';
import { PrimeNGConfig } from 'primeng/api';
import { lastValueFrom, take } from 'rxjs';

export function HttpLoaderFactory(
  httpBackend: HttpBackend,
): MultiTranslateHttpLoader {
  return new MultiTranslateHttpLoader(httpBackend, [
    { prefix: `${environment.baseUrlMf}/assets/i18n/`, suffix: '.json' },
    { prefix: `${environment.baseUrlCommons}/i18n/`, suffix: '.json' },
  ]);
}

export function appInitializerFactory(
  translate: TranslateService,
  document: Document,
  primeNGConfig: PrimeNGConfig,
): () => Promise<unknown> {
  return () => {
    translate.addLangs(['ca', 'es']);
    translate.setDefaultLang('ca');

    const currentLang = window.location.href.includes('/es/') ? 'es' : 'ca';
    document.documentElement.lang = currentLang;

    // Set primeNG specific translations
    translate
      .get('SE_COMPONENTS.PRIMENG')
      .pipe(take(1))
      .subscribe((res) => primeNGConfig.setTranslation(res));

    return lastValueFrom(translate.use(currentLang));
  };
}

registerLocaleData(localeCa, 'ca');

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    SeHeaderInfoModule,
    SeExceptionViewerModule,
    HttpClientModule,
    AppRoutingModule,
    HelpModalModule,
    BrowserAnimationsModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpBackend],
      },
    }),
    SeStepperdModule,
    LazyElementsModule.forRoot({
      rootOptions: {
        loadingComponent: SpinnerComponent,
      },
    }),
  ],
  providers: [
    { provide: LOCALE_ID, useValue: 'ca-ES' },
    {
      provide: DEFAULT_CURRENCY_CODE,
      useValue: 'EUR',
    },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: SeHttpInterceptorService,
      multi: true,
    },
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService, DOCUMENT, PrimeNGConfig],
      multi: true,
    },
  ],
  bootstrap: [],
})
export class AppModule implements DoBootstrap {
  constructor(private injector: Injector) {}

  ngDoBootstrap(appRef: ApplicationRef): void {
    const el = createCustomElement(AppComponent, { injector: this.injector });
    // Same name inside concat elements-build.js
    if (!customElements.get('se-padro-co2')) {
      customElements.define('se-padro-co2', el);
    }
    if (isDevMode()) {
      appRef.bootstrap(AppComponent);
    }
  }
}
