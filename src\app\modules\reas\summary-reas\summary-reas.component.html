<div class="pt-3">
  <app-request-summary
    [title]="'SE_PADRO_CO2.REAS_PROCESS.RESUM_TITLE' | translate"
    [documentColumns]="documentColumns"
    [documentRows]="documentRows"
    [descriptionLabel]="'SE_PADRO_CO2.LABELS.MOTIU' | translate"
    [data]="data"
    [modalTitle]="'SE_PADRO_CO2.MODAL.VEHICLES.TITLE_RECURS' | translate"
  >
  </app-request-summary>

  <div class="mt-4" *ngIf="accountNumberData?.iban">
    <app-account-number-summary
      [title]="'SE_PADRO_CO2.REAS_PROCESS.IBAN.TITLE' | translate"
      [data]="accountNumberData"
    >
    </app-account-number-summary>
  </div>

  <div class="mt-4" *ngIf="notificationsData?.postalAddress">
    <app-notifications-summary
      [title]="'SE_PADRO_CO2.REAS_PROCESS.DADES_NOTIFICACIO.TITLE' | translate"
      [notificationsData]="notificationsData"
      [showOnlyPostalAddress]="true"
    >
    </app-notifications-summary>
  </div>

  <div class="mt-4" *ngIf="idTramit">
    <app-civil-servant-required-doc-block
      [idEntity]="idTramit"
      [idFunctionalModule]="idFunctionalModule"
      [authSuport]="true"
      [plates]="plates"
      (disableContinueButton)="onDisableContinueButton($event)"
    >
    </app-civil-servant-required-doc-block>
  </div>

  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button
      type="button"
      btnTheme="secondary"
      (onClick)="onGoBackButtonClick()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.BACK' | translate }}
    </se-button>

    <se-button
      type="submit"
      btnTheme="primary"
      [disabled]="disableContinueButton"
      (onClick)="onContinueButtonClick()"
    >
      {{ 'SE_PADRO_CO2.ALLEGATIONS.SUBMIT' | translate }}
    </se-button>
  </div>
</div>
