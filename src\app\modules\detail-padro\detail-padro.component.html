<div class="detail-padro mb-4" *ngIf="carDetail">
  <!-- el titulo tags y boton de volver atras -->
  <div class="detail-padro__header">
    <div class="d-flex justify-content-between">
      <div class="detail-padro__header__left">
        <h3>
          {{ carDetail.marca }} {{ carDetail.model }} {{ carDetail.matricula }}
        </h3>
        <div
          class="d-flex align-content-center justify-content-start gap-2 mb-3"
        >
          <se-tag
            *ngFor="let tag of tags"
            [tagTheme]="tag.color"
            [closable]="false"
          >
            {{ tag.text }}
          </se-tag>
        </div>
      </div>
      <div *ngIf="isSelfPerson" class="detail-padro__header__right mt-1">
        <se-button
          id="detail-padro__header__help-button"
          class="d-lg-block"
          [btnTheme]="'secondary'"
          (onClick)="showHelpModal()"
        >
          {{ 'UI_COMPONENTS.BUTTONS.HELP' | translate }}
        </se-button>
      </div>
    </div>

    <!-- BUTTON BACK -->
    <div class="button-container">
      <se-button
        [type]="'button'"
        [btnTheme]="'onlyText'"
        [icon]="'matArrowBackOutline'"
        [iconPosition]="'left'"
        (onClick)="goBack()"
      >
        {{ 'UI_COMPONENTS.BUTTONS.BACK' | translate }}
      </se-button>
    </div>
  </div>

  <!-- card de domiciliacion -->
  <ng-container
    *ngIf="
      showRegisterDomiciliation &&
      specificConfigurationService.inTimeToBeDomiciled &&
      !carDetail?.domicilat &&
      !carDetail?.domicilatSeguents
    "
  >
    <div class="mt-3 mb-3 blue-border" appHideOnCoordinator>
      <div class="row">
        <div class="col-12 col-xl-9">
          <p class="m-0">
            <strong
              [innerHTML]="
                'SE_PADRO_CO2.MODAL.DETAIL.DOMICILIATE_CO2_RECEIPTS_EXERCISE'
                  | translate
              "
            ></strong>
          </p>
          <span
            >{{ 'SE_PADRO_CO2.MODAL.DETAIL.DOMICILIATION_BONUS' | translate }}
            <strong>
              {{
                'SE_PADRO_CO2.MODAL.DETAIL.BONUS_PERCENT'
                  | translate
                    : { percent: specificConfigurationService.percentDiscount }
              }}
            </strong>
          </span>
        </div>
        <div class="col-12 col-xl-3 align-content-center justify-content-end">
          <se-button
            (onClick)="navigateToCreateDomiciliation()"
            [btnTheme]="'primary'"
          >
            {{ 'SE_PADRO_CO2.BUTTONS.DOMICILIAR_PAYMENTS' | translate }}
          </se-button>
        </div>
      </div>
    </div>
  </ng-container>

  <!-- todo el contenedor del detalle -->
  <div class="info-container row mt-4">
    <div class="col-12 col-lg-7">
      <!-- panel contenedor de detalle -->
      <se-panel
        [title]="'SE_PADRO_CO2.LABELS.DETAIL' | translate"
        [colapsible]="false"
        [collapsed]="false"
        [actionButton]="panelDetallActionButton"
        (actionButtonClick)="navigateToAllegations()"
        [tooltip]="false"
      >
        <!-- TODO revisar condiciones que aún no estan para "Pagado" -->
        <!-- seccion de datos de liquidacion -->
        <div *ngIf="false">
          <p>{{ 'SE_PADRO_CO2.LABELS.SETTLEMENT_DATA' | translate }}</p>
          <!--  TODO agregar descripciones a los detales -->
          <app-detail-element
            [title]="'SE_PADRO_CO2.DETAIL.LABELS.SETTLEMENT_NAME' | translate"
            [description]="''"
            [tooltipText]="''"
          >
          </app-detail-element>
          <hr />
        </div>
        <!-- seccion de datos de liquidacion -->

        <!-- seccion datos de vehiculo -->
        <p>{{ 'SE_PADRO_CO2.LABELS.VEHICLE_DATA' | translate }}</p>

        <app-detail-element
          *ngFor="let vehicle of vehicleInfo"
          [description]="vehicle.description"
          [title]="vehicle.title"
          [tooltipText]="vehicle.tooltipText"
          [linkText]="vehicle.linkText"
          [tagLabel]="vehicle.tagLabel"
          [tagTheme]="vehicle.tagTheme"
          [linkCommand]="vehicle.linkCommand"
        >
        </app-detail-element>

        <!-- seccion datos de vehiculo -->
        <ng-container>
          <hr />
          <!-- seccion datos de titular -->
          <p>{{ 'SE_PADRO_CO2.LABELS.OWNER_DATA' | translate }}</p>
          <app-detail-element
            *ngFor="let owner of ownerInfo"
            [description]="owner.description"
            [title]="owner.title"
            [tooltipText]="owner.tooltipText"
            [linkText]="owner.linkText"
            [tagLabel]="owner.tagLabel"
            [linkCommand]="owner.linkCommand"
          >
          </app-detail-element>
        </ng-container>
        <!-- seccion datos de titular -->
      </se-panel>

      <!-- panel de domiciliacion -->
      <div class="mt-3">
        <se-panel
          [title]="'SE_PADRO_CO2.DETAIL.OPTIONS.DOMICILED' | translate"
          [colapsible]="false"
          [collapsed]="false"
          [actionButton]="panelDomiciledActionButton"
          [tooltip]="false"
          (actionButtonClick)="navigateToUpdateDomiciled()"
        >
          <app-detail-element
            *ngFor="let domiciled of domiciledInfo"
            [description]="domiciled.description"
            [title]="domiciled.title"
            [tooltipText]="domiciled.tooltipText"
            [linkText]="domiciled.linkText"
            [tagLabel]="domiciled.tagLabel"
            [linkCommand]="domiciled.linkCommand"
          >
          </app-detail-element>
          <div class="mt-3">
            <se-alert
              *ngIf="!carDetail?.domicilat && carDetail?.domicilatSeguents"
              [title]="'SE_PADRO_CO2.DETAIL.LABELS.DOMICILIATED_NOTICE'"
              [type]="'info'"
              [closeButton]="false"
            >
            </se-alert>
          </div>
        </se-panel>
      </div>

      <!-- 5.3.5. Bloc recursos i reclamacions -->
      <app-recursos-i-reclamacions [vehicleDetail]="carDetail" />

      <ng-container *ngIf="isLoginSimple">
        <div class="my-5 row info-messages" appHideOnCoordinator>
          <div class="col-12">
            <app-info-messages
              [title]="'SE_PADRO_CO2.LOGIN_SIMPLE.ID_CAT_TITLE' | translate"
              [text]="'SE_PADRO_CO2.LOGIN_SIMPLE.ID_CAT_SUBTITLE' | translate"
              [image]="'id_cat_logo'"
            >
              <div class="d-flex flex-column flex-md-row mt-1">
                <se-button
                  [btnTheme]="'secondary'"
                  (onClick)="navigateToIdCat()"
                  >{{
                    'SE_PADRO_CO2.LOGIN_SIMPLE.ID_CAT_BUTTON' | translate
                  }}</se-button
                >
              </div>
            </app-info-messages>
          </div>
        </div>
      </ng-container>
    </div>

    <!-- contenedor de calculo y de lo de abajo -->
    <div class="right col-12 col-lg-5 mt-3 mt-md-0">
      <!-- panel de calculo -->
      <se-panel
        [title]="
          (specificConfigurationService.isProvisional
            ? 'SE_PADRO_CO2.DETAIL.LABELS.ESTIMATE_CALCULATION'
            : 'SE_PADRO_CO2.DETAIL.LABELS.ESTIMATE'
          ) | translate
        "
        [colapsible]="false"
        [collapsed]="false"
        [tooltip]="false"
      >
        <p class="currency text-center">
          {{ carDetail.calcul | currency }}
        </p>
        <hr />
        <p class="bold">
          {{
            (isProvisional
              ? 'SE_PADRO_CO2.DETAIL.PAYMENT_BREAKDOWN_PROVISIONAL'
              : 'SE_PADRO_CO2.DETAIL.PAYMENT_BREAKDOWN_DEFINITIVE'
            ) | translate
          }}
        </p>
        <app-detail-element-dashed
          *ngFor="let payment of paymentsInfo"
          [description]="payment.description"
          [title]="payment.title"
          [tooltipText]="payment.tooltipText"
          [linkText]="payment.linkText"
          [tagLabel]="payment.tagLabel"
          [bold]="!!payment.bold"
          [linkCommand]="payment.linkCommand"
        >
        </app-detail-element-dashed>

        <ng-container
          *ngIf="
            getCondition() ===
              detailAlertsEnum.PROVISIONAL_NO_DOMICILED_IN_TIME_TO_BE_DOMICILED ||
            getCondition() === detailAlertsEnum.PENDING_PAYMENT
          "
        >
          <div appHideOnCoordinator class="calculus">
            <hr />
            <se-button
              [type]="'button'"
              [size]="'large'"
              [btnTheme]="'primary'"
              (onClick)="onButtonClick()"
            >
              {{ getButtonLabel() | translate }}
            </se-button>
          </div>
        </ng-container>

        <div class="mt-4">
          <se-alert
            *ngIf="getAlertList().length"
            [type]="'info'"
            [closeButton]="false"
            [list]="getAlertList()"
          >
          </se-alert>
          <!-- TODO  cambiar condicion cuando se tenga clara-->
          <div
            *ngIf="getCondition() === detailAlertsEnum.EXECUTIVE"
            class="d-flex align-items-center flex-column"
          >
            <p class="text-center">
              {{ executiveLinkText | translate }}
            </p>
            <se-link
              *ngIf="showExecutiveLink"
              [href]="executiveLink"
              class="align-content-center"
              [disabled]="false"
              [size]="'regular'"
              [linkTheme]="'secondary'"
            >
              {{ 'SE_PADRO_CO2.DETAIL.ACCESS_DEBTS_SECTION' | translate }}
            </se-link>
          </div>
        </div>
      </se-panel>

      <!-- a que se destina - imagenes -->
      <div class="info mt-4 mb-4">
        <p class="bold mb-2">
          {{ 'SE_PADRO_CO2.DETAIL.DESTINATION' | translate }}
        </p>
        <div class="row">
          <div class="col-12 d-flex align-items-center">
            <img [src]="getSrc(IMAGES.CAR)" alt="''" class="img" />
            <p class="ms-3">
              {{ 'SE_PADRO_CO2.DETAIL.RENEWABLE_ENERGY_PROMOTION' | translate }}
            </p>
          </div>
          <div class="col-12 d-flex align-items-center">
            <img [src]="getSrc(IMAGES.BIKE)" alt="''" class="img" />
            <p class="ms-3">
              {{ 'SE_PADRO_CO2.DETAIL.PUBLIC_TRANSPORT_AID' | translate }}
            </p>
          </div>
          <div class="col-12 d-flex align-items-center">
            <img [src]="getSrc(IMAGES.LIGTH)" alt="''" class="img" />
            <p class="ms-3">
              {{ 'SE_PADRO_CO2.DETAIL.RESEARCH_INNOVATION' | translate }}
            </p>
          </div>
          <div class="col-12 d-flex align-items-center">
            <img [src]="getSrc(IMAGES.HAND)" alt="''" class="img" />
            <p class="ms-3">
              {{
                'SE_PADRO_CO2.DETAIL.ENVIRONMENTAL_MANAGEMENT_POLICIES'
                  | translate
              }}
            </p>
          </div>
          <div class="col-12 d-flex align-items-center">
            <img [src]="getSrc(IMAGES.FARM)" alt="''" class="img" />
            <p class="ms-3">
              {{ 'SE_PADRO_CO2.DETAIL.MILLORA_PARCS_NATURALS' | translate }}
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
