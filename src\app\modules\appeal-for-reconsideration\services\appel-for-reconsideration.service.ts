import { Injectable } from '@angular/core';
import { VehiclesSelectedInfo } from '@app/shared/models';
import {
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { TranslateService } from '@ngx-translate/core';
import { merge, Observable, take, tap } from 'rxjs';
import { SeModalService } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class AppelForReconsiderationService {
  constructor(
    private storageData: StorageService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private loginSrv: LoginResponseService,
    private modalService: SeModalService,
    private translateService: TranslateService,
  ) {}

  getVehiclesSelectedInfo(nifTitular: string): VehiclesSelectedInfo | null {
    const recursResponse = this.storageData.getRecursVehicles();

    if (
      !recursResponse?.validation?.vehiclesRecurribles ||
      recursResponse.validation.vehiclesRecurribles.length === 0
    ) {
      return null;
    }

    return {
      provisional: this.specificConfigurationSrv.isProvisional,
      matriculas: recursResponse.validation.vehiclesRecurribles.map(
        (vehicle) => vehicle.matricula,
      ),
      exercici: this.specificConfigurationSrv.currentExercise,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
      nifTitular: nifTitular,
    };
  }

  openAlertModal(): Observable<string> {
    const modalRef = this.modalService.openModal({
      severity: 'warning',
      title: this.translateService.instant(
        'SE_PADRO_CO2.RECURS_PROCESS.MODAL_IBAN_WARNING.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_PADRO_CO2.RECURS_PROCESS.MODAL_IBAN_WARNING.SUBTITLE',
      ),
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'UI_COMPONENTS.BUTTONS.CANCEL',
    });

    const second: Observable<string> =
      modalRef.componentInstance.modalSecondaryButtonEvent;
    const close: Observable<string> =
      modalRef.componentInstance.modalOutputEvent;

    return merge(close, second).pipe(
      take(1),
      tap(() => modalRef.close()),
    );
  }
}
