import type { Nullable } from 'se-ui-components-mf-lib';
import type { SortMeta } from './sort-meta.model';
import type { SortOrder } from './sort-order.model';

export interface SearchOptions<SortFieldType = string> {
  first?: Nullable<number>; // First Page
  rows?: Nullable<number>; // Rows per page
  sortField?: Nullable<SortFieldType>;
  sortOrder?: Nullable<SortOrder>;
  multiSortMeta?: Nullable<SortMeta<SortFieldType>[]>;
}
