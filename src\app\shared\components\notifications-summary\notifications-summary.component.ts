import { Component, Input } from '@angular/core';
import { Nullable } from 'se-ui-components-mf-lib';
import { ContribuentOutputData } from '@app/shared/models';

@Component({
  selector: 'app-notifications-summary',
  templateUrl: './notifications-summary.component.html',
  styles: [
    `
      .overflow-ellipsis {
        text-overflow: ellipsis;
        overflow: hidden;
      }
    `,
  ],
})
export class NotificationsSummaryComponent {
  @Input() notificationsData: Nullable<ContribuentOutputData>;
  @Input() showOnlyPostalAddress: boolean = false;
  @Input() title: string = '';
}
