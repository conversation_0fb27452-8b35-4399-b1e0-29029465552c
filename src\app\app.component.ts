import {
  Component,
  EventEmitter,
  isDevMode,
  OnInit,
  Output,
  type OnD<PERSON>roy,
} from '@angular/core';
import { NavigationEnd, Router } from '@angular/router';
import {
  combineLatest,
  filter,
  map,
  Observable,
  Subject,
  takeUntil,
} from 'rxjs';

import {
  ConfigService,
  HeaderInfoService,
  StepperService,
} from '@core/services';
import {
  Nullable,
  SeAuthService,
  SeDeviceService,
  SeLoginResponse,
  SeLoginService,
  SePageLayoutService,
  SeStep,
  SeUser,
  SeUserService,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss'],
})
export class AppComponent implements OnInit, OnDestroy {
  presenterName: Nullable<string>;
  presenterNif: Nullable<string>;
  steps$!: Observable<SeStep[]>;
  configLoaded: boolean = false;
  title$: Observable<Nullable<string>>;
  private readonly destroyed$ = new Subject<void>();

  get inElMeuEspai(): boolean {
    return window.location.href.includes('/el-meu-espai-atc/') && !isDevMode();
  }

  @Output() fullScreenMode: EventEmitter<boolean> = new EventEmitter<boolean>();

  constructor(
    protected readonly headerInfoService: HeaderInfoService,
    protected readonly pageLayoutService: SePageLayoutService,
    private readonly loginService: SeLoginService,
    private readonly authService: SeAuthService,
    private readonly stepperService: StepperService,
    private readonly router: Router,
    private readonly configurationService: ConfigService,
    private readonly userService: SeUserService,
    private readonly deviceService: SeDeviceService,
  ) {
    console.log('Webcomponent: CO2 > constructor');

    this.router.initialNavigation();
    // Listen to the route changes (updates page layout)
    this.pageLayoutService.listenNavigation();
    // Listen steps changes
    this.stepperService.initializeAvailableRoutes();
    this.steps$ = this.pageLayoutService.getCurrentSteps();

    this.router.events
      .pipe(filter((event) => event instanceof NavigationEnd))
      .subscribe(() => {
        // Scroll to top
        window.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
      });

    this.headerInfoService.fullscreenMode$.subscribe((value) => {
      this.fullScreenMode.emit(value);
    });

    this.title$ = this.getTitle$();
  }

  async ngOnInit(): Promise<void> {
    this.configLoaded = await this.setMfConfiguration();
    // Secured
    if (window.location.href.includes('/secured')) {
      if (isDevMode()) {
        // Only locally instead of getUserData
        await this.secured();
      } else {
        this.setUserData(this.authService.getSessionStorageUser());
      }
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.unsubscribe();
  }

  getTitle$(): Observable<Nullable<string>> {
    return combineLatest({
      title: this.headerInfoService.titleHeader$,
      titleMobile: this.headerInfoService.titleHeaderMobile$,
    }).pipe(
      takeUntil(this.destroyed$),
      map(({ title, titleMobile }) =>
        this.deviceService.isMobile() ? titleMobile : title,
      ),
    );
  }

  private setUserData(user: SeUser): void {
    if (user) {
      this.presenterName = this.userService.getName();
      this.presenterNif = this.userService.getNIF();
    }
  }

  private secured(): Promise<void> {
    return this.loginService.login(true).then((response: SeLoginResponse) => {
      if (response?.content?.tokenJwt) {
        this.setUserData(response.content?.usuario as SeUser);
      }
    });
  }

  private async setMfConfiguration(): Promise<boolean> {
    return this.configurationService.setCo2Configuration();
  }
}
