import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { map, Observable, of, switchMap } from 'rxjs';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import {
  ATESA_AGREEMENT_ID,
  COORDINADOR_AGREEMENT_ID,
  GetRespresentativeMapResponse,
  O12_AGREEMENT_ID,
  RepresentativesData,
  UserHasAgreementResponse,
} from './identifications.model';

@Injectable({
  providedIn: 'root',
})
export class IdentificationsEndpointsService {
  constructor(private httpService: SeHttpService) {}

  userHasAgreementFor(
    procedureCode: string,
    isAocAtesaCoordinador: boolean = false,
  ): Observable<boolean> {
    const urlVersion = isAocAtesaCoordinador ? `/v2` : ``;
    return this.httpService
      .get({
        baseUrl: environment.baseUrlSeguretat,
        url: `${urlVersion}/usuaris/convenis/${procedureCode}`,
        method: 'get',
      })
      .pipe(
        map((response: UserHasAgreementResponse) => {
          const errorMessages = (response.messages || []) as unknown[];
          const hasAgreement = !!response.content;
          return hasAgreement && errorMessages.length === 0;
        }),
      );
  }

  userHasAgreementDifferentFromAtesarOrCoordinator(
    procedureCode: string,
    isAocAtesaCoordinador: boolean = false,
  ): Observable<boolean> {
    const atesaCoordinator012ProcedureCodes = `${ATESA_AGREEMENT_ID},${COORDINADOR_AGREEMENT_ID},${O12_AGREEMENT_ID}`;
    return this.userHasAgreementFor(procedureCode, isAocAtesaCoordinador).pipe(
      switchMap((hasAgreement: boolean) => {
        if (hasAgreement) {
          return this.httpService
            .get({
              baseUrl: environment.baseUrlSeguretat,
              url: `/usuaris/in-convenis/${atesaCoordinator012ProcedureCodes}`,
              method: 'get',
            })
            .pipe(
              map((response: UserHasAgreementResponse) => {
                const errorMessages = (response.messages || []) as unknown[];
                const hasAocAtesaCoordinador012Agreement = !!response.content;
                // Si tiene alguno de los convenios de Atesa, Coordinador o O12, entonces no es un convenio valido
                return !(
                  hasAocAtesaCoordinador012Agreement &&
                  errorMessages.length === 0
                );
              }),
            );
        }

        return of(false);
      }),
    );
  }

  // se consulta despues de scoring para verificar si es el representante del NIF ingresado
  verifyIfIsRepresentativeWithNIF(
    representativeNIF: string,
    isAocAtesaCoordinador: boolean = false,
  ): Observable<boolean> {
    const urlVersion = isAocAtesaCoordinador ? `/v2` : ``;
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlSeguretat,
      url: `${urlVersion}/usuaris/representats/${representativeNIF}`,
      method: 'get',
    };

    return this.httpService.get<boolean>(httpRequest).pipe(
      map((response: SeHttpResponse<boolean>) => {
        const errorMessages = (response.messages || []) as unknown[];
        const isRepresentative = !!response.content;
        return isRepresentative && errorMessages.length === 0;
      }),
    );
  }

  verifyIfIsRepresentativeWithNIFByProcedure(
    representativeNIF: string,
    procedure: string,
    isAocAtesaCoordinador: boolean = false,
  ): Observable<boolean> {
    const urlVersion = isAocAtesaCoordinador ? `/v2` : ``;
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlSeguretat,
      url: `${urlVersion}/usuaris/representats/${representativeNIF}/impost/${procedure}`,
      method: 'get',
    };

    return this.httpService.get<boolean>(httpRequest).pipe(
      map((response: SeHttpResponse<boolean>) => {
        const errorMessages = (response.messages || []) as unknown[];
        const isRepresentative = !!response.content;
        return isRepresentative && errorMessages.length === 0;
      }),
    );
  }

  // para saber si es representante el usuario en sesion
  verifyIsRepresentative(
    isAocAtesaCoordinador: boolean = false,
  ): Observable<GetRespresentativeMapResponse> {
    const urlVersion = isAocAtesaCoordinador ? `/v2` : ``;
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlSeguretat,
      url: `${urlVersion}/usuaris/representats`,
      method: 'get',
    };

    return this.httpService.get<boolean>(httpRequest).pipe(
      map((response: SeHttpResponse<RepresentativesData[]>) => {
        const errorMessages = (response.messages || []) as unknown[];
        const isRepresentative = !!response?.content?.length;
        return {
          isRepresentative: isRepresentative && errorMessages.length === 0,
          representativesData: response?.content || [],
        };
      }),
    );
  }

  verifyIsRepresentativeByPorcedure(
    procedure: string,
    isAocAtesaCoordinador: boolean = false,
  ): Observable<GetRespresentativeMapResponse> {
    const urlVersion = isAocAtesaCoordinador ? `/v2` : ``;
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlSeguretat,
      url: `${urlVersion}/usuaris/representats/impost/${procedure}`,
      method: 'get',
    };

    return this.httpService.get<boolean>(httpRequest).pipe(
      map((response: SeHttpResponse<RepresentativesData[]>) => {
        const errorMessages = (response.messages || []) as unknown[];
        const isRepresentative = !!response?.content?.length;
        return {
          isRepresentative: isRepresentative && errorMessages.length === 0,
          representativesData: response?.content || [],
        };
      }),
    );
  }
}
