/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
  ViewChild,
  AfterViewInit,
  ChangeDetectionStrategy,
  ViewEncapsulation,
  ElementRef,
  NgZone,
  ChangeDetectorRef,
  OnInit,
} from '@angular/core';
import { ComponentEventOutput } from './captcha.model';

@Component({
  selector: 'app-captcha',
  templateUrl: './captcha.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  encapsulation: ViewEncapsulation.None,
})
export class CaptchaComponent implements OnInit, AfterViewInit, OnDestroy {
  private _size: 'normal' | 'compact' = 'normal';

  private _firstLoadComponent = true;

  private _instance: any = null;

  private _language: string = 'en';

  private _captchaMethod: any;

  @ViewChild('captchaComponent') captchaComponent: ElementRef | undefined;

  /* Component inputs */

  // Public sitekey.
  @Input() _captchaPublicApiKey: string | undefined;

  @Input() set size(value: 'normal' | 'compact') {
    if (value) {
      this._size = value;
    }
  }

  @Input() set _reset(value: boolean) {
    if (value) {
      this.reset();
    }
    // call setCaptchaAriaLabel() each time _reset changes
    // setTimeout(0) sends the callback at the end of the callstack
    if (!this._firstLoadComponent)
      setTimeout(() => this._setCaptchaAriaLabel(), 0);
  }

  @Input() theme = 'light';

  @Input() containerClass: string = '';

  @Input() type = 'image';

  @Input() tabindex = 0;

  @Input() enterprise: boolean = false;

  @Input() initCallback = 'initRecaptcha';

  @Input() get language(): string {
    return this._language;
  }

  set language(language: string) {
    if (language) {
      this._language = language;
    }
    this.init();
  }

  /* Component outputs */
  // The callback function to be executed when the user submits a successful CAPTCHA response.
  @Output() _responseEvent: EventEmitter<ComponentEventOutput> =
    new EventEmitter<ComponentEventOutput>();

  // The callback function to be executed when the recaptcha response expires and the user needs to solve a new CAPTCHA.
  @Output() _expireEvent: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    public el: ElementRef,
    public _zone: NgZone,
    public cd: ChangeDetectorRef,
  ) {}

  ngOnInit(): void {
    this._captchaMethod = this.enterprise
      ? (<any>window).grecaptcha.enterprise
      : (<any>window).grecaptcha;
  }

  ngAfterViewInit(): void {
    if ((<any>window).grecaptcha) {
      if (!this._captchaMethod?.render) {
        setTimeout(() => {
          this.init();
        }, 100);
      } else {
        this.init();
      }
    } else {
      (<any>window)[this.initCallback] = (): void => {
        this.init();
      };
    }
    setTimeout(() => this._setCaptchaAriaLabel(), 100);
  }

  ngOnDestroy(): void {
    if (this._instance !== null) {
      this._captchaMethod?.reset(this._instance);
    }
  }

  init(): void {
    try {
      this._instance = this._captchaMethod?.render(
        this.el.nativeElement.children[0],
        {
          sitekey: this._captchaPublicApiKey,
          theme: this.theme,
          type: this.type,
          size: this._size,
          tabindex: this.tabindex,
          hl: this.language,
          callback: (response: string) => {
            this._zone.run(() => this.recaptchaCallback(response));
          },
          'expired-callback': () => {
            this._zone.run(() => this.recaptchaExpiredCallback());
          },
        },
      );
    } catch (error) {
      console.error(error);
    }
  }

  reset(): void {
    if (this._instance === null) return;

    this._captchaMethod?.reset(this._instance);
    this.cd.markForCheck();
  }

  getResponse(): string | null {
    if (this._instance === null) return null;

    return this._captchaMethod?.getResponse(this._instance);
  }

  recaptchaCallback(response: string): void {
    this._responseEvent.emit({
      componentId: 'captcha',
      browserEvent: null,
      componentValue: response,
    });
  }

  recaptchaExpiredCallback(): void {
    this._expireEvent.emit();
  }

  private _setCaptchaAriaLabel(): void {
    if (this._firstLoadComponent) this._firstLoadComponent = false;

    const captchaInput = this.captchaComponent?.nativeElement.querySelector(
      '.captcha-container #g-recaptcha-response',
    );
    if (captchaInput) captchaInput.setAttribute('aria-label', 'Captcha');
  }
}
