import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { AllegationResumResponse } from '@modules/allegations/resum-allegation/models';
import { RecursPresentationResponse } from '@modules/appeal-for-reconsideration/summary-reconsideration/models/summary-reconsideration.model';

@Injectable({
  providedIn: 'root',
})
export class SummaryReconsiderationEndpointService {
  constructor(private httpService: SeHttpService) {}

  getRecursResumInfo(idTramit: string): Observable<AllegationResumResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `recurs/${idTramit}/resum`,
      method: 'get',
    };

    return this.httpService.get<AllegationResumResponse>(httpRequest);
  }

  setRecursPresentation(
    idTramit: string,
  ): Observable<RecursPresentationResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `recurs/${idTramit}/presentacio`,
      method: 'post',
    };

    return this.httpService.post<RecursPresentationResponse>(httpRequest);
  }
}
