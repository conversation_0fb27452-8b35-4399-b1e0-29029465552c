import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AppRoutes,
  FunctionalModuleEnum,
  IdentificationType,
  ResourceProcessDocument,
} from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  StorageService,
} from '@core/services';
import { map, Observable, of, Subject, switchMap, takeUntil } from 'rxjs';
import {
  Column,
  iDocumentPadoct,
  Nullable,
  Row,
  SeAuthService,
  SeUser,
} from 'se-ui-components-mf-lib';
import { RequestSummary } from '@shared/components';
import { SummaryReconsiderationEndpointService } from '@modules/appeal-for-reconsideration/summary-reconsideration/summary-reconsideration-endpoint.service';
import { ResumAllegationService } from '@modules/allegations/resum-allegation/services';
import { AppelForReconsiderationService } from '@modules/appeal-for-reconsideration/services/appel-for-reconsideration.service';
import {
  ProcedureSavedRecurs,
  RecursPresentationResponse,
  RecursResumResponse,
} from '@modules/appeal-for-reconsideration/summary-reconsideration/models/summary-reconsideration.model';
import { ProcessNameToDisplayInPresentationReceipt } from '@modules/presentation-receipt';
import { NotificacionsEndpointService } from '@modules/appeal-for-reconsideration/notifications/notificacions-endpoint.service';
import {
  AddressTramitRequest,
  Motius,
  VehiclesSelectedInfo,
} from '@app/shared/models';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';
import { MaskIbanService } from '@app/core/services/mask-iban';
import { ContribuentOutputData } from '@app/shared/models';
import { AccountNumberSummary } from '@app/shared/components/account-number-summary';

@Component({
  selector: 'app-summary-reconsideration',
  templateUrl: './summary-reconsideration.component.html',
  styleUrls: ['./summary-reconsideration.component.scss'],
})
export class SummaryReconsiderationComponent implements OnInit, OnDestroy {
  private unsubscribe: Subject<void> = new Subject();
  data: RequestSummary | undefined;
  recurs: Nullable<Motius[]>;

  recursColumns: Column[] = [];
  recursRows: Row[] = [];

  private user: SeUser | undefined;
  accountNumberData: AccountNumberSummary | undefined;
  idTramit: Nullable<string>;
  notificationsData: Nullable<ContribuentOutputData>;
  vehiclesInfo: Nullable<VehiclesSelectedInfo>;
  plates: string[] = [];

  protected idFunctionalModule: FunctionalModuleEnum =
    FunctionalModuleEnum.RECURS;
  protected disableContinueButton: boolean = false;

  get isLoginSimple(): boolean {
    return this.loginSrv.user.tipusAccess === IdentificationType.LOGIN_SIMPLE;
  }

  constructor(
    private summaryReconsidetarionEndpointService: SummaryReconsiderationEndpointService,
    private seAuthService: SeAuthService,
    private storageData: StorageService,
    private customRouter: CustomRouterService,
    private resumAllegationsService: ResumAllegationService,
    private appelForReconsiderationService: AppelForReconsiderationService,
    private notificationsEndpointService: NotificacionsEndpointService,
    private loginSrv: LoginResponseService,
    private procedureHeaderService: ProceduresHeaderService,
    private maskIbanService: MaskIbanService,
  ) {
    // Intencionadamente vacío
  }

  ngOnInit(): void {
    this.user = this.seAuthService.getSessionStorageUser();
    this.vehiclesInfo = this.vehiclesInfo =
      this.appelForReconsiderationService.getVehiclesSelectedInfo(
        this.user.nif,
      );
    this.idTramit = this.storageData.getRecursVehicles().recursId;
    this.notificationsData = this.storageData.getNotificationsData();
    this.plates = this.vehiclesInfo?.matriculas || [];
    this.procedureHeaderService.setupRecursHeader(
      this.vehiclesInfo?.matriculas,
    );

    this.getResumData();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private getResumData(): void {
    if (this.idTramit) {
      this.summaryReconsidetarionEndpointService
        .getRecursResumInfo(this.idTramit)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result: RecursResumResponse) => {
          if (result?.content) {
            this.data =
              this.resumAllegationsService.setRecursDataResponseInDataObject(
                result.content,
                this.vehiclesInfo,
                this.isLoginSimple,
                false, // No GDPR message for this process
              );
            this.accountNumberData = {
              iban: this.maskIbanService.transform(
                result?.content.ibanNumeroCompte,
                '',
              ),
              declaracioNumeroCompte: !!result?.content?.declaracioNumeroCompte,
              documentNumeroCompte: result?.content.documentNumeroCompte,
            };

            this.recurs = result?.content.motius;
            this.getRecursColumns();
          }
        });
    }
  }

  getRecursColumns(): void {
    this.recursColumns = this.resumAllegationsService.getAllegacionsColumns();
    this.getRecursRows();
  }

  getRecursRows(): void {
    this.recursRows = this.resumAllegationsService.getAllegacionsRows(
      this.recurs,
    );
  }

  onGoBackButtonClick(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.RECONSIDERATION_NOTIFICATION);
  }

  onContinueButtonClick(): void {
    if (this.idTramit) {
      if (this.notificationsData?.address) {
        this.proccessPostalAddressAndSubmitProcess(
          this.idTramit,
          this.notificationsData.address,
        );
      } else {
        this.submitProccess(this.idTramit);
      }
    }
  }

  private proccessPostalAddressAndSubmitProcess(
    idTramit: string,
    request: Partial<AddressTramitRequest>,
  ): void {
    this.notificationsEndpointService
      .processAddressTramit(idTramit, new AddressTramitRequest(request))
      .pipe(takeUntil(this.unsubscribe))
      .subscribe({
        complete: () => this.submitProccess(idTramit, request),
      });
  }

  private submitProccess(
    idTramit: string,
    request?: Partial<AddressTramitRequest>,
  ): void {
    this.summaryReconsidetarionEndpointService
      .setRecursPresentation(idTramit)
      .pipe(
        takeUntil(this.unsubscribe),
        switchMap((response) =>
          this.proccessPostalAddressMiro(response, request),
        ),
      )
      .subscribe((result: RecursPresentationResponse) => {
        if (result?.content) {
          this.setDataToShowThemInPresentationReceipt(result.content);
          this.storageData.clearReasonsSelected();
          this.storageData.clearIdTramit();
          this.storageData.deleteNotificationsData();
          this.customRouter.navigateByBaseUrl(AppRoutes.PRESENTATION_RECEIPT);
        }
      });
  }

  private proccessPostalAddressMiro(
    response: RecursPresentationResponse,
    address?: Partial<AddressTramitRequest>,
  ): Observable<RecursPresentationResponse> {
    const isCivilServant: boolean =
      this.storageData.profileUser === IdentificationType.CIVIL_SERVANT;
    const idTramitacioOrigen: string = response?.content?.idTramit || '';
    const idPersCens: string | undefined = isCivilServant
      ? this.loginSrv.user?.idPersTitular
      : undefined;

    const request = new AddressTramitRequest({
      ...address,
      idTramitacioOrigen,
      idPersCens,
    });

    if (response?.content && request?.teExpedient && idTramitacioOrigen) {
      return this.notificationsEndpointService
        .processAddressTramitMiro(request)
        .pipe(
          takeUntil(this.unsubscribe),
          map(() => response),
        );
    }
    return of(response);
  }

  private setDataToShowThemInPresentationReceipt(
    response: ProcedureSavedRecurs,
  ): void {
    const processedAddressResponse =
      this.notificationsData?.processedAddressResponse || null;
    const contactData = this.notificationsData?.contactData || null;

    this.storageData.setResourceProcessDocumentData({
      receipt: {
        idPadoct: response.idJustificant,
        nom: response.fileName,
      } as iDocumentPadoct,
      idFunctionalModule:
        ProcessNameToDisplayInPresentationReceipt.AppealForReconsideration,
      processedAddressResponse: processedAddressResponse,
      contactData: contactData,
    } as ResourceProcessDocument);
    this.storageData.processNameToDisplayPresentationReceipt =
      ProcessNameToDisplayInPresentationReceipt.AppealForReconsideration;
  }

  protected onDisableContinueButton($event: boolean): void {
    this.disableContinueButton = $event;
  }
}
