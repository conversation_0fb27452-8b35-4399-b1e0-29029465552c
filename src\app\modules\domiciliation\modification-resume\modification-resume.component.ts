import { Component, OnDestroy, OnInit } from '@angular/core';
import {
  AppRoutes,
  DomiciledProcedureRequest,
  DomiciledProcedureResponseFinalizar,
  DomiciledProcessTypes,
  DomiciledVehicle,
  FunctionalModuleEnum,
  IdentificationType,
  ProcedureSaved,
  ResourceProcessDocument,
} from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { TranslateService } from '@ngx-translate/core';
import { RequestSummary } from '@shared/components';
import { DomiciliationEndpointService } from '@shared/services';
import { iDocumentPadoct } from 'se-ui-components-mf-lib';
import { ProcessNameToDisplayInPresentationReceipt } from '../../presentation-receipt/presentation-receipt.model';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import { MaskIbanService } from '@app/core/services/mask-iban';
import { ProceduresHeaderService } from '../../../shared/services/procedures-header';
import { ModificationResumeUnsubscribeService } from './modification-resume-unsubscribe.service';

@Component({
  selector: 'app-domiciled-modification-resume',
  templateUrl: './modification-resume.component.html',
})
export class ModificationResumeComponent implements OnInit, OnDestroy {
  protected directDebitSummary: RequestSummary | undefined;
  protected loginSimpleUserForm: FormGroup | undefined;
  protected disableContinueButton: boolean = false;
  protected idEntity: string = '';
  protected plates: string[] = [];
  private destroyed$ = new Subject<void>();

  get titleText(): string {
    if (this.storageService.isUnsubscribe) {
      return this.translateService.instant(
        'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.CANCELLATION_WARNING',
      );
    }

    const vehicleExercice =
      this.storageService.vehiclesToBeUpdated[0]?.exerciciDomiciliacio;
    const isCurrentExerciceEqualToVehicleExercice =
      this.specificConfigurationSrv.currentExercise === vehicleExercice;
    if (
      this.specificConfigurationSrv.outOfTimeToModifyAccount ||
      !isCurrentExerciceEqualToVehicleExercice
    ) {
      return this.translateService.instant(
        'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.EFFECTIVE_FROM',
        {
          currentYear:
            !isCurrentExerciceEqualToVehicleExercice && vehicleExercice
              ? vehicleExercice
              : this.specificConfigurationSrv.exerciseToModifyAccount,
        },
      );
    }

    return '';
  }

  get showPDFInfo(): boolean {
    return !this.storageService.isUnsubscribe;
  }

  get continueButtonText(): string {
    return this.storageService.isUnsubscribe
      ? 'SE_PADRO_CO2.BUTTONS.CONTINUE'
      : 'SE_PADRO_CO2.BUTTONS.SUBMIT_REQUEST';
  }

  get summaryTitle(): string {
    return this.translateService.instant(
      this.storageService.isUnsubscribe
        ? 'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.CANCELLATION_TITLE'
        : 'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.MODIFICATION_TITLE',
      {
        currentYear: new Date(this.specificConfigurationSrv.limitDomiciliacions)
          .getFullYear()
          .toString(),
      },
    );
  }

  get modalTitle(): string {
    return this.translateService.instant(
      this.storageService.isUnsubscribe
        ? 'SE_PADRO_CO2.MODAL.VEHICLES.TITLE_DOMICILIATION_UNSUBSCRIBE'
        : 'SE_PADRO_CO2.MODAL.VEHICLES.TITLE_DOMICILIATION_MODIFICATION',
    );
  }

  get idFunctionalModule(): FunctionalModuleEnum {
    return FunctionalModuleEnum.DOMICILIACIONS;
  }

  constructor(
    private domiciliationEndopointService: DomiciliationEndpointService,
    private storageService: StorageService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private loginSrv: LoginResponseService,
    private translateService: TranslateService,
    private customRouter: CustomRouterService,
    private maskIbanService: MaskIbanService,
    private headerService: ProceduresHeaderService,
    private unsubscribeService: ModificationResumeUnsubscribeService,
  ) {}

  ngOnInit(): void {
    if (this.loginSrv.user.tipusAccess === IdentificationType.LOGIN_SIMPLE) {
      this.buildLoginSimpleUserForm();
    }

    this.setSummaryData();
    this.headerService.setupDomiciliationHeader(
      this.storageService.vehiclesToBeUpdated,
    );
    this.saveDomiciliationUpdate();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  setSummaryData(): void {
    const { vehiclesToBeUpdated, isUnsubscribe, ibanDomiciliation } =
      this.storageService;
    const iban = this.maskIbanService.transform(ibanDomiciliation);

    this.plates = vehiclesToBeUpdated.map(
      (vehicle: DomiciledVehicle) => vehicle.matricula as string,
    );
    this.directDebitSummary = {
      vehicles: {
        provisional: this.specificConfigurationSrv.isProvisional,
        matriculas: this.plates,
        exercici: this.getExercice(vehiclesToBeUpdated),
        idPersTitular: this.loginSrv.user.idPersTitular as string,
      },
      ibanLabel: 'SE_PADRO_CO2.LABELS.NEW_BANK_ACCOUNT',
      showGdprMsg: true,
      ...(vehiclesToBeUpdated.length === 1 && {
        singleVehicleText: `${vehiclesToBeUpdated[0].matricula}, ${vehiclesToBeUpdated[0].marca} ${vehiclesToBeUpdated[0].model}`,
      }),
    };
    if (!isUnsubscribe) {
      this.directDebitSummary = {
        ...this.directDebitSummary,
        iban,
      };
    }
  }
  private saveDomiciliationUpdate(): void {
    const atesaPhone = this.storageService.civilServantVehiclePhone;
    const atesaDate = this.storageService.civilServantVehicleDate;
    const atesaHour = this.storageService.civilServantVehicleHour;
    const body: DomiciledProcedureRequest = {
      accio: this.storageService.isUnsubscribe
        ? DomiciledProcessTypes.UNSUBSCRIBE
        : DomiciledProcessTypes.UPDATE,
      provisional: this.specificConfigurationSrv.isProvisional,
      listMatriculas: this.plates,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
      iban: this.storageService.ibanDomiciliation,
      exercici: this.getExercice(this.storageService.vehiclesToBeUpdated),
      tipusAccess: this.loginSrv.user.tipusAccess,
      ...(atesaPhone && atesaDate && atesaHour
        ? {
            trucadaTelefonica: {
              numeroTelefon: atesaPhone,
              data: atesaDate,
              hora: atesaHour,
            },
          }
        : {}),
      nifRepresentant: this.storageService.civilServantVehicleNifRepresentant,
    };

    this.domiciliationEndopointService
      .postDomiciledProcedureInici(body)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((result) => {
        if (result?.content?.id) {
          this.idEntity = result.content.id;
        }
      });
  }

  private getExercice(vehiclesToBeUpdated: DomiciledVehicle[]): string {
    const vehicleExercice = vehiclesToBeUpdated[0]?.exerciciDomiciliacio;
    const modifyAccountExercice =
      this.specificConfigurationSrv.inTimeToModifyAccount && vehicleExercice
        ? vehicleExercice
        : this.specificConfigurationSrv.exerciseToModifyAccount;
    const isUnsubscribe = this.storageService.isUnsubscribe;
    const exercice = isUnsubscribe
      ? this.specificConfigurationSrv.exerciseToBeDomiciliated
      : modifyAccountExercice;

    return exercice;
  }

  protected goBack(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.MODIFY_DOMICILED);
  }

  protected onContinue(): void {
    if (this.storageService.isUnsubscribe) {
      const nom = this.loginSimpleUserForm?.get('name')?.value;
      const cognoms = this.loginSimpleUserForm?.get('surname')?.value;
      this.unsubscribeService.showConfirmationUnsubscribeModal(
        this.idEntity,
        nom,
        cognoms,
      );
    } else {
      this.submitDomiciliationUpdate();
    }
  }

  private submitDomiciliationUpdate(): void {
    this.domiciliationEndopointService
      .postDomiciledProcedureFinalizar(this.getRequestSaveDomiciliation())
      .pipe(takeUntil(this.destroyed$))
      .subscribe((response: DomiciledProcedureResponseFinalizar) => {
        this.setDataToShowThemInPresentationReceipt(
          response.content as ProcedureSaved,
        );
        this.storageService.processNameToDisplayPresentationReceipt =
          ProcessNameToDisplayInPresentationReceipt.Domiciliation;
        this.storageService.clearModificationData();
        this.customRouter.navigateByBaseUrl(AppRoutes.PRESENTATION_RECEIPT);
      });
  }

  private getRequestSaveDomiciliation(): DomiciledProcedureRequest {
    const userFromLoginSrv = this.loginSrv.user;
    return {
      idPersTitular: userFromLoginSrv.idPersTitular as string,
      listMatriculas: this.plates,
      id: this.idEntity,
      tipusAccess: userFromLoginSrv.tipusAccess,
      nom: this.loginSimpleUserForm?.get('name')?.value || null,
      cognoms: this.loginSimpleUserForm?.get('surname')?.value || null,
    };
  }

  private setDataToShowThemInPresentationReceipt(
    response: ProcedureSaved,
  ): void {
    this.storageService.setResourceProcessDocumentData({
      receipt: {
        idPadoct: response.idJustificant,
        nom: response.fileName,
      } as iDocumentPadoct,
      idFunctionalModule:
        ProcessNameToDisplayInPresentationReceipt.Domiciliations_modified,
    } as ResourceProcessDocument);
  }

  private buildLoginSimpleUserForm(): void {
    this.loginSimpleUserForm = new FormGroup({
      name: new FormControl('', [Validators.required]),
      surname: new FormControl('', [Validators.required]),
    });
  }

  protected onDisableContinueButton($event: boolean): void {
    this.disableContinueButton = $event;
  }
}
