import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import {
  DomiciledProcedureRequest,
  FeatureFlag,
  DomiciledVehiclesRequest,
  DomiciledVehiclesResponse,
  GetUserInfoResponse,
  GetNotificationDataResponse,
  DomiciledProcedureResponseInici,
  DomiciledProcedureResponseFinalizar,
} from '@core/models';
import { environment } from '@environments/environment';

@Injectable({
  providedIn: 'root',
})
export class DomiciliationEndpointService {
  private endPoint = 'domiciliacions';

  constructor(private httpService: SeHttpService) {}

  getVehiclesToBeDomiciliated(
    body: DomiciledVehiclesRequest,
  ): Observable<DomiciledVehiclesResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `${this.endPoint}/vehicles-domiciliables`,
      method: 'post',
      body,
      suppressErrorMessage: true,
    };

    return this.httpService.post<DomiciledVehiclesResponse>(httpRequest);
  }

  getDomiciledVehicles(
    body: DomiciledVehiclesRequest,
  ): Observable<DomiciledVehiclesResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `${this.endPoint}/vehicles-domiciliats`,
      method: 'post',
      body,
      suppressErrorMessage: true,
    };

    return this.httpService.post<DomiciledVehiclesResponse>(httpRequest);
  }

  postDomiciledProcedureInici(
    body: DomiciledProcedureRequest,
  ): Observable<DomiciledProcedureResponseInici> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `${this.endPoint}/inici`,
      method: 'post',
      body,
    };

    return this.httpService.post<DomiciledProcedureResponseInici>(httpRequest);
  }

  postDomiciledProcedureFinalizar(
    body: DomiciledProcedureRequest,
  ): Observable<DomiciledProcedureResponseFinalizar> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `${this.endPoint}/finalitzar`,
      method: 'post',
      body,
    };

    return this.httpService.post<DomiciledProcedureResponseFinalizar>(
      httpRequest,
    );
  }

  getFeatureFlagDomiciliation(
    body: DomiciledVehiclesRequest,
  ): Observable<SeHttpResponse<FeatureFlag>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `${this.endPoint}/vehicles-records`,
      method: 'post',
      body,
    };

    return this.httpService.post(httpRequest);
  }

  getUserInfo = (): Observable<GetUserInfoResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlContribuent,
      url: `/user-info`,
      method: 'get',
    };

    return this.httpService.get(httpRequest);
  };

  getNotificationData = (): Observable<GetNotificationDataResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlContribuent,
      url: `/te-notificacio-electronica`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  };

  getNotificationDataById = (
    idPers: string,
  ): Observable<GetNotificationDataResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlContribuent,
      url: `/v2/te-notificacio-electronica`,
      method: 'post',
      body: {
        idPersCens: idPers,
      },
    };
    return this.httpService.post(httpRequest);
  };
}
