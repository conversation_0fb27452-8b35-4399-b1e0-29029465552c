<div class="vehicle-selection">
  <div class="row">
    <div class="col-12 col-md-6 vehicle-selection__vehicles-panel">
      <se-panel
        [title]="
          'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.DOMICILED_VEHICLES'
            | translate
        "
      >
        <se-table
          *ngIf="data.length; else noDataTable"
          [selectable]="true"
          [showSelectAll]="!isMobile"
          [columns]="tableColumns"
          [data]="data"
          [itemsPerPage]="itemsPerPage"
          [cellTemplatePriorityOrder]="'row-column-cell'"
          (onSelectionChange)="onSelectChange($event)"
        >
        </se-table>
      </se-panel>
    </div>
    <div class="col-12 col-md-6 vehicle-selection__request-direct-debit-panel">
      <se-panel
        [title]="
          'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.MODIFY_OR_CANCEL'
            | translate
        "
      >
        <div class="radio-form">
          <form [formGroup]="form">
            <se-radio
              formControlName="radioValue"
              [id]="radioValues.UPDATE"
              [label]="'SE_PADRO_CO2.LABELS.MODIFY_DOMICILIATION' | translate"
              [name]="radioValues.UPDATE"
              [disabled]="false"
              [value]="radioValues.UPDATE"
              (valueChanged)="setBankDetails()"
            ></se-radio>
            <se-radio
              formControlName="radioValue"
              [id]="radioValues.UNSUBSCRIBE"
              [label]="'SE_PADRO_CO2.LABELS.CANCEL_DOMICILIATION' | translate"
              [name]="radioValues.UNSUBSCRIBE"
              [disabled]="false"
              [value]="radioValues.UNSUBSCRIBE"
            ></se-radio>
          </form>
          <ng-container
            *ngIf="form.get('radioValue')?.value === radioValues.UPDATE"
          >
            <div class="iban-container mt-4">
              <mf-pagaments-dades-bancaries
                *axLazyElement
                [fileFormatSeparation]="fileFormatSeparation"
                [pagamentsData]="bankDetailInput"
                [validateRepresentative]="isValidateRepresentative()"
                [subtitle]="
                  'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.NEW_BANK_ACCOUNT_DETAILS'
                    | translate
                "
                [isPanel]="false"
                (onFormChanged)="onBankDetailChange($event)"
              >
              </mf-pagaments-dades-bancaries>
            </div>
          </ng-container>

          <ng-container
            *ngIf="form.get('radioValue')?.value === radioValues.UNSUBSCRIBE"
          >
            <div class="mt-4">
              <se-alert
                [title]="
                  'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.CANCELLATION_WARNING'
                "
                [type]="'info'"
                [closeButton]="false"
              >
              </se-alert>
            </div>
          </ng-container>
        </div>
      </se-panel>
    </div>
  </div>

  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button (onClick)="goBack()" [btnTheme]="'secondary'">
      {{ 'SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate }}
    </se-button>
    <se-button (onClick)="onContinue()" [disabled]="isNextButtonDisabled">
      {{ 'SE_PADRO_CO2.BUTTONS.CONTINUE' | translate }}
    </se-button>
  </div>
</div>

<ng-template #noDataTable>
  <se-empty-state [icon]="'info'" [backgroundTheme]="'primary'" />
</ng-template>
