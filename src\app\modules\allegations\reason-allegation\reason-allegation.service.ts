import { Injectable } from '@angular/core';
import { ReasonsFormValue } from './reason-allegation.model';
import {
  ContestableActDocument,
  Reason,
  Reasons,
  SubReasons,
} from '@shared/components/allegations-options';
import { iDocumentPadoct } from 'se-ui-components-mf-lib';
import { Motius, DocumentsUpload } from '@app/shared/models';

@Injectable({
  providedIn: 'root',
})
export class ReasonAllegationService {
  getRequestReasonsSelected(
    allegationsFormValue: ReasonsFormValue,
    allegationsList: Reasons[],
    uploadDocuments: iDocumentPadoct[],
  ): <PERSON><PERSON>[] {
    const selection =
      this.getSelectedAllegationsControlName(allegationsFormValue);
    const reasonsSelected: Motius[] = this.filterReasonsSelected(
      allegationsList,
      selection,
    ).map((reason) => {
      return {
        motiu: reason.id.toString(),
        submotiu: reason.submotius
          ? (allegationsFormValue[`subreason_${reason.id}`] as string)
          : '',
        descripcio: this.getDescription(reason, allegationsFormValue),
        documents: this.getDocumentsReasonsSelected(
          reason,
          uploadDocuments,
          Number(allegationsFormValue[`subreason_${reason.id}`]),
        ),
        checkbox: reason.submotius
          ? (allegationsFormValue[`subreason_checkbox_${reason.id}`] as boolean)
          : false,
      };
    });

    return reasonsSelected;
  }

  private getDescription(
    reason: Reasons,
    allegationsFormValue: ReasonsFormValue,
  ): string {
    if (reason.descripcioObligatoria) {
      return allegationsFormValue[`free-text_${reason.id}`] as string;
    }

    if (reason?.submotius?.some((submotiu) => submotiu.descripcioObligatoria)) {
      return allegationsFormValue[`subreason_free-text_${reason.id}`] as string;
    }

    return '';
  }

  private getDocumentsReasonsSelected(
    reason: Reasons,
    uploadDocuments: iDocumentPadoct[],
    subreasonId: number,
  ): DocumentsUpload[] {
    const documentsSelected: DocumentsUpload[] = [];
    const reasonDocuments: ContestableActDocument[] =
      (reason.submotius && reason.submotius.length > 0
        ? this.getSubreasonSelected(reason, subreasonId)?.documents
        : reason.documents) ?? [];

    reasonDocuments.forEach((document) => {
      const documentFound = uploadDocuments.find(
        (doc) => doc.codeDescriptionComplementary === document.subtype,
      );
      if (documentFound) {
        documentsSelected.push({
          id: documentFound.id,
          filename: documentFound.nom,
          documentTypeId: documentFound.codeDescriptionComplementary,
        });
      }
    });

    return documentsSelected;
  }

  private filterReasonsSelected(
    list: Reason[],
    selection: string[],
  ): Reasons[] {
    return list.filter((item) =>
      selection.includes(item.formControlName ?? ''),
    );
  }

  private getSelectedAllegationsControlName(
    allegationsFormValue: ReasonsFormValue,
  ): string[] {
    if (!allegationsFormValue) return [];

    return Object.entries(allegationsFormValue)
      .filter((pairControlNameValue) => pairControlNameValue[1])
      .map((pairControlNameValue) => pairControlNameValue[0]);
  }

  private getSubreasonSelected(
    reason: Reasons,
    subreasonId: number,
  ): SubReasons | undefined {
    const subreasons = reason?.submotius ?? [];

    return subreasons.find((item) => item.id === subreasonId);
  }
}
