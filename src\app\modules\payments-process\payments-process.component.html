<section class="header-content mb-4" *ngIf="isSelfPerson">
  <h1 class="header-content__title">
    {{ 'SE_PADRO_CO2.PAYMENTS_PROCESS.TITLE' | translate }}
  </h1>
</section>
<div class="app-payments-process mb-2" *ngIf="paymentData">
  <se-panel
    *ngIf="showTable"
    [title]="'SE_PADRO_CO2.PAYMENTS_PROCESS.PANEL_TITLE' | translate"
    [colapsible]="false"
    [collapsed]="false"
    [panelTheme]="'primary'"
    [tooltip]="false"
    class="panel-0-padding"
  >
    <se-table
      [styleClass]="'border-table'"
      [resizable]="false"
      [columns]="vehiclesColumns"
      [data]="vehiclesRows"
    ></se-table>
    <div class="d-flex justify-content-end resume-payment">
      <span>{{
        'SE_PADRO_CO2.PAYMENTS_PROCESS.TABLE.TOTAL_INGRESSAR' | translate
      }}</span>
      <span class="resume-payment__total-amount">{{
        totalAmount | currency: 'EUR'
      }}</span>
    </div>
  </se-panel>
  <mf-pagaments-proces-pagament
    *axLazyElement
    [disableContinueButton]="disableContinueButton"
    [paymentData]="paymentData"
    [totalDue]="totalAmount"
    (primaryClicked)="handlePayButton($event)"
    [primaryButtonLabel]="'SE_PADRO_CO2.BUTTONS.PAY' | translate"
    [secondaryButtonLabel]="
      showTable ? ('SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate) : ''
    "
    (secondaryClicked)="goToReceipts()"
    (activeContainerChanged)="onActiveContainerChanged($event)"
    (formChanged)="onFormChanged($event)"
  >
    <div payment-process-footer class="mt-4">
      <app-civil-servant-required-doc-block
        [accountNumber]="ibanValue"
        [accountNumberFormValid]="ibanFormValid"
        [paymentType]="paymentType"
        [checkPaymentData]="true"
        [idEntity]="paymentData.idBBDD || ''"
        [idFunctionalModule]="idFunctionalModule"
        [authPagament]="true"
        [plates]="plates"
        (disableContinueButton)="onDisableContinueButton($event)"
      >
      </app-civil-servant-required-doc-block>
    </div>
  </mf-pagaments-proces-pagament>

  <div class="d-flex flex-column flex-sm-row justify-content-sm-end mt-4">
    <se-button
      *ngIf="!showTable"
      [label]=""
      [btnTheme]="'secondary'"
      (onClick)="goToReceipts()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate }}
    </se-button>
  </div>
</div>
