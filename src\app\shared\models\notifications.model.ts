import { Nullable } from 'se-ui-components-mf-lib';
import { AddressTramit } from './address-tramit-request.model';

export enum DestinationOrgans {
  TR = 'TR',
  JF = 'JF',
}
export type DestinationOrgansT = keyof typeof DestinationOrgans | '';

export enum NotificationsMode {
  POSTAL_ADDRESS_ACTIVATE_NE = 'POSTAL_ADDRESS_ACTIVATE_NE',
  ACTIVATE_NOTICES = 'ACTIVATE_NOTICES',
  UPDATE_CONTACT_DATA = 'UPDATE_CONTACT_DATA',
  THIRD_PERSON_NOT_REPRESENTATIVE = 'THIRD_PERSON_NOT_REPRESENTATIVE',
}

export interface ContribuentInputData {
  isThirdPerson: boolean;
  nifPassiveSubject: string;
  idPersCens?: string;
  esAtesa?: boolean;
}

export enum OrigenEnum {
  LOGIN = 'LOGIN',
  RECURS = 'RECURS',
  MEU_ESPAI = 'MEU_ESPAI',
  REA = 'REA',
}

export type OrigenEnumT = keyof typeof OrigenEnum;

export interface ContribuentOutputData {
  address?: Partial<Address>;
  postalAddress?: string;
  canal?: string;
  thirdPersonNotificationsMsg?: string;
  isExpedient?: boolean | null;
  processedAddressResponse?: AddressTramit;
  contactData?: ContactDataOutput;
}

export interface ProcessingNotificationsData extends ContribuentOutputData {
  receivingAgency?: Nullable<string>;
  receivingAgencyCode?: Nullable<DestinationOrgansT>;
}

export interface Address {
  tipusVia: string;
  codTipusVia: string;
  direccio: string;
  num: string;
  escala: string;
  pis: string;
  porta: string;
  lletra: string;
  provincia: string;
  codiProvincia: string;
  municipi: string;
  codPaisIne: string;
  codProvinciaIne: string;
  codMunicipiIne: string;
  codPostal: string;
  pais: string;
  codPais: string;
  comarca: string;
  codiComarca: string;
  codiMunicipi: string;
  tnum: string;
  codiTipusNumeracio: string;
  comaut: string;
  codComaut: string;
  longitud: string;
  latitud: string;
  utmx: string;
  utmy: string;
  idAdrecCens: string;
  compl: string;
  bloc: string;
  portal: string;
  idAnteriorAdreca: string;
  flagAdressNormalitzada: string;
  isExpedient: boolean;
  telephone: string;
  email: string;
  avis: boolean;
  actiuNE: boolean;
  fax: string;
  viaYPortalYPisoYLetra: string;
  cpYMunicipio: string;
}

export interface ContactDataOutput {
  hasAccess: boolean;
  receiptId?: string;
  receiptCsv?: string;
}
