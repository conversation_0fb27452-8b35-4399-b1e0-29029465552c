import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SeModalModule, SeTableModule } from 'se-ui-components-mf-lib';
import { ModalVehiclesComponent } from './modal-vehicles.component';

@NgModule({
  declarations: [ModalVehiclesComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    SeModalModule,
    SeTableModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class ModalVehiclesModule {}
