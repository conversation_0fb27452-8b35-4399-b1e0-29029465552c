import { Injectable } from '@angular/core';
import { AppRoutes } from '@core/models/app-routes.enum';
import { TranslateService } from '@ngx-translate/core';
import { take } from 'rxjs';

import { SePageLayoutService, SeStep } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class StepperService {
  constructor(
    private translate: TranslateService,
    private pageLayoutService: SePageLayoutService,
  ) {
    /* intentionally empty constructor */
  }

  initializeAvailableRoutes(): void {
    this.translate
      .stream('SE_DECINF_MF.STEPPER')
      .pipe(take(1))
      .subscribe((translate) => {
        const ET_STEPPER: { [x: string]: SeStep[] } = {
          REPOSITION_RESOURCE_ENABLE: [
            {
              id: 'REPOSITION_RESOURCE_DESKTOP_STEP1',
              label: translate['PARTICIPANTS'],
              routerLink: AppRoutes.PARTICIPANTS,
            },
            {
              id: 'REPOSITION_RESOURCE_DESKTOP_STEP2',
              label: translate['YEAR_DECLARATION'],
              routerLink: AppRoutes.YEAR_DECLARATION,
            },
            {
              id: 'REPOSITION_RESOURCE_DESKTOP_STEP3',
              label: translate['SUMMARY'],
              routerLink: AppRoutes.SUMMARY,
            },
          ],
        };

        this.pageLayoutService.setAvailableSteps(ET_STEPPER);
      });
  }
}
