import { Component, Input } from '@angular/core';
import { SpecificConfigurationService } from '@app/core/services';
import { VehiclesSelectedInfo } from '@app/shared/models';
import { ModalVehiclesComponent } from '@shared/components';
import {
  Nullable,
  SeAlertMessage,
  SeModalService,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-domiciliation-request',
  templateUrl: './domiciliation-request.component.html',
  styleUrls: ['./domiciliation-request.component.scss'],
})
export class DomiciliationRequestComponent {
  @Input() domiciliationDate: Nullable<string>;
  @Input() showDetail = false;
  @Input() alert: SeAlertMessage | undefined;
  @Input() bodyVehicles: VehiclesSelectedInfo | undefined;

  get exerciceDom(): number {
    return this.specificConfigurationSrv.exerciceDom;
  }
  constructor(
    private modalSrv: SeModalService,
    private specificConfigurationSrv: SpecificConfigurationService,
  ) {}

  protected onOpenVehiclesModal(): void {
    if (
      this.bodyVehicles?.matriculas &&
      this.bodyVehicles?.matriculas?.length
    ) {
      const component: ModalVehiclesComponent = this.modalSrv.openModal({
        title: 'SE_PADRO_CO2.MODAL.VEHICLES.TITLE_DOMICILIATION',
        severity: 'info',
        closable: true,
        closableLabel: 'SE_PADRO_CO2.BUTTONS.OK',
        hideIcon: true,
        size: 'xl',
        secondaryButton: false,
        component: ModalVehiclesComponent,
      }).componentInstance;

      component.vehiclesPlates = this.bodyVehicles;
      component.hideExerciceColumn = true;
    }
  }
}
