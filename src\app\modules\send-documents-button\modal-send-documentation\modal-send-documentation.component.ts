import { Component, EventEmitter, Input, Output } from '@angular/core';
import {
  AbstractControl,
  FormBuilder,
  FormGroup,
  ValidationErrors,
  ValidatorFn,
  Validators,
} from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  SeModal,
  SeModalOutputEvents,
  SeValidations,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-modal-send-documentation',
  templateUrl: './modal-send-documentation.component.html',
  styleUrls: [],
})
export class ModalSendDocumentationComponent {
  protected componentForm: FormGroup;
  protected showModal: boolean = false;

  _data: SeModal = {
    severity: 'info',
    closable: true,
    closableLabel: 'SE_DOCUMENTS_MF.SEND_DOCUMENTS_BUTTON.MODAL.BUTTON',
    closableDisabled: false,
    title: 'SE_DOCUMENTS_MF.SEND_DOCUMENTS_BUTTON.MODAL.TITLE',
    titleTextWeight: 'semi-bold',
    subtitle: 'SE_DOCUMENTS_MF.SEND_DOCUMENTS_BUTTON.MODAL.DESCRIPTION',
  };

  @Input() set data(data: SeModal) {
    this._data = {
      ...this._data,
      ...data,
    };
  }

  get data(): SeModal {
    return this._data;
  }

  @Output() modalOutput: EventEmitter<string> = new EventEmitter<string>();

  constructor(
    private readonly activatedModalService: NgbActiveModal,
    private readonly fb: FormBuilder,
  ) {
    this.componentForm = this.fb.group({
      email: [
        null,
        [
          Validators.required,
          SeValidations.email,
          Validators.pattern(/^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/),
        ],
      ],
      confirmEmail: [
        null,
        [
          Validators.required,
          SeValidations.email,
          Validators.pattern(/^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/),
        ],
      ],
    });
    this.componentForm.addValidators(
      this.matchingTextValidator('email', 'confirmEmail'),
    );
  }

  private matchingTextValidator(
    controlName: string,
    matchingControlName: string,
  ): ValidatorFn {
    return (formGroup: AbstractControl): ValidationErrors | null => {
      const control = formGroup.get(controlName);
      const matchingControl = formGroup.get(matchingControlName);

      if (
        control &&
        matchingControl &&
        control.value !== matchingControl.value
      ) {
        matchingControl.setErrors({ email: true });
        return { email: true };
      } else {
        matchingControl!.setErrors(null);
        return null;
      }
    };
  }

  protected closeAlert(): void {
    this.showModal = false;
  }

  protected closeModal(event: string): void {
    if (event === SeModalOutputEvents.MAIN_ACTION) {
      if (this.componentForm.valid) {
        this.activatedModalService.close(this.componentForm.getRawValue());
        // Añado este metodo output ya que cuando se utiliza este componente en un webcomponent no hay forma de capturar el close de la modal
        this.modalOutput.emit(this.componentForm.getRawValue().email);
      } else {
        this.showModal = true;
      }
    } else {
      this.modalOutput.emit();
      this.activatedModalService.close();
    }
  }
}
