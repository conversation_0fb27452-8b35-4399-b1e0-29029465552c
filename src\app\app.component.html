<div class="app-padro-co2">
  <!-- HEADER INFO -->
  <se-header-info
    *ngIf="headerInfoService.isVisible"
    [ngClass]="headerInfoService.styleClass"
    [title]="(title$ | async)! | translate"
    [showHelp]="true"
    [customButton]="(headerInfoService.customButton$ | async)!"
    [buttonDropdown]="(headerInfoService.dropdownButton$ | async)!"
    [infoItems]="headerInfoService.infoItems$ | async"
    [tags]="headerInfoService.tags$ | async"
    [expanded]="false"
    (helpButtonClick)="headerInfoService.showHelpModal()"
  ></se-header-info>
  <!-- CONTAINER -->
  <div class="main-container mt-3 mt-md-4">
    <div class="container">
      <!--/* STEPPER */-->
      <div
        class="mb-4"
        *ngIf="pageLayoutService.isElementVisible && (steps$ | async) as steps"
      >
        <se-stepper [steps]="steps"></se-stepper>
      </div>
      <!-- CONTENT -->
      <div class="page-container">
        <!-- EXCEPTIONS -->
        <se-exception-viewer *ngIf="!inElMeuEspai"></se-exception-viewer>
        <!-- MAIN CONTENT -->
        <router-outlet *ngIf="configLoaded"></router-outlet>
      </div>
    </div>
  </div>
</div>
