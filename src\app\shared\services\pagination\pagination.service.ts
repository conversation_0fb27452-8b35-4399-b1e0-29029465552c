import { Injectable } from '@angular/core';
import { DeviceService } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class PaginationService {
  readonly ROWS_PER_PAGE_OPTIONS_DESKTOP = [10, 20, 50];
  readonly ROWS_PER_PAGE_OPTIONS_MOBILE = [
    5,
    ...this.ROWS_PER_PAGE_OPTIONS_DESKTOP,
  ];
  readonly ITEMS_PER_PAGE_MOBILE = 5;
  readonly ITEMS_PER_PAGE_DESKTOP = 10;

  constructor(private deviceSrv: DeviceService) {}

  getItemsPerPage(): number {
    return this.deviceSrv.isMobile()
      ? this.ITEMS_PER_PAGE_MOBILE
      : this.ITEMS_PER_PAGE_DESKTOP;
  }
  getRowsPerPageOptions(): number[] {
    return this.deviceSrv.isMobile()
      ? this.ROWS_PER_PAGE_OPTIONS_MOBILE
      : this.ROWS_PER_PAGE_OPTIONS_DESKTOP;
  }
}
