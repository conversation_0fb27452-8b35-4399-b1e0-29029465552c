import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { map, Observable } from 'rxjs';
import {
  SeDropdownOption,
  SeHttpRequest,
  SeHttpService,
} from 'se-ui-components-mf-lib';

export interface SituationsResponse {
  messages: string[];
  content: SeDropdownOption[];
}

@Injectable({
  providedIn: 'root',
})
export class MastersEndpointService {
  constructor(private httpService: SeHttpService) {}

  public getSituations(): Observable<SeDropdownOption[]> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDadaReferencia,
      url: `/estat-deute-calc`,
      method: 'get',
      clearExceptions: true,
    };

    return this.httpService.get(httpRequest).pipe(
      map((response) => {
        if (response?.content?.length) {
          return response.content.map((res: SeDropdownOption) => ({
            ...res,
            value: res.id,
          }));
        } else {
          return [];
        }
      }),
    );
  }
}
