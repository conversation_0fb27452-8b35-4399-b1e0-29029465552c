import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DetailPadroComponent } from './detail-padro.component';
import { TranslateModule } from '@ngx-translate/core';
import { CurrencyPipe } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import {
  SePanelModule,
  SeLinkModule,
  SeAlertModule,
  SeButtonModule,
  SeTooltipAccessibleModule,
  SeModalModule,
} from 'se-ui-components-mf-lib';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HeaderInfoService } from '@app/core/services';
import { RouterTestingModule } from '@angular/router/testing';

describe('DetailPadroComponent', () => {
  let component: DetailPadroComponent;
  let fixture: ComponentFixture<DetailPadroComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        BrowserAnimationsModule,
        TranslateModule.forRoot(),
        HttpClientTestingModule,
        SePanelModule,
        SeLinkModule,
        SeAlertModule,
        SeButtonModule,
        SeTooltipAccessibleModule,
        SeModalModule,
      ],
      providers: [CurrencyPipe, HeaderInfoService],
      declarations: [DetailPadroComponent],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(DetailPadroComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
