import {
  AfterViewInit,
  ChangeDetectorRef,
  Directive,
  ElementRef,
  Renderer2,
} from '@angular/core';
import { StorageService } from '@core/services';
import { IdentificationType } from '../models';

@Directive({
  selector: '[appHideOnCoordinator]',
  standalone: true,
})
export class HideOnCoordinatorDirective implements AfterViewInit {
  private isCoordinator: boolean = false;

  constructor(
    private el: ElementRef,
    private renderer: Renderer2,
    private cdr: ChangeDetectorRef,
    private storageService: StorageService,
  ) {
    this.isCoordinator =
      this.storageService.profileUser === IdentificationType.COORDINADOR;
  }

  ngAfterViewInit(): void {
    this.updateVisibility();
    this.cdr.detectChanges();
  }

  updateVisibility(): void {
    if (this.isCoordinator) {
      this.renderer.addClass(this.el.nativeElement, 'd-none');
    }
  }
}
