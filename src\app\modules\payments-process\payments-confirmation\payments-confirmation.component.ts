import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import {
  PaymentData,
  PaymentResult,
  PaymentResultStates,
} from '../models/payments.model';
import { AppRoutes, IdentificationType } from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { FeatureFlagService } from '@app/core/services/feature-flag';
import { TranslateService } from '@ngx-translate/core';
import { Nullable, SeAuthService } from 'se-ui-components-mf-lib';
import { Subject, takeUntil } from 'rxjs';
import {
  GetPendingPaymentsRequest,
  PaymentsEndpointsService,
} from '@app/shared/services';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';

@Component({
  selector: 'app-payments-confirmation',
  templateUrl: './payments-confirmation.component.html',
  styleUrls: ['./payments-confirmation.component.scss'],
})
export class PaymentsConfirmationComponent implements OnInit, OnDestroy {
  paymentData: PaymentData | undefined;
  protected domiciliatedInfoMessage: string = `${this.translate.instant('SE_PADRO_CO2.RECEIPTS.DOMICILED')}  <strong>${this.translate.instant('SE_PADRO_CO2.RECEIPTS.BONIFICATION', { percent: this.specificConf.percentDiscount })} </strong>`;
  // ratenow
  private readonly surveyFamily = 'FAM3';
  private readonly procedurePaymentId = 'F3CAS2';

  get showRegisterDomiciliation(): boolean {
    return (
      this.featureFlagSrv.registerDomiciliation && this._isSuccessfulPayment
    );
  }

  get showModifyDomiciliation(): boolean {
    return this.featureFlagSrv.modifyDomiciliation && this._isSuccessfulPayment;
  }

  get showPanel(): boolean {
    return !!this._rebutsPendents && this._isSuccessfulPayment;
  }

  get asteriskLink1(): string {
    return `https://atc.gencat.cat/${this.translate.currentLang}/utilitats/consulta-referencia-document`;
  }

  get asteriskLink1Label(): string {
    return `atc.gencat.cat/${this.translate.currentLang}/utilitats/consulta-referencia-document`;
  }

  get asteriskLink2(): string {
    return `https://atc.gencat.cat/${this.translate.currentLang}/dir/pd-info`;
  }

  get asteriskLink2Label(): string {
    return `atc.gencat.cat/${this.translate.currentLang}/dir/pd-info`;
  }

  private _rebutsPendents: Nullable<boolean>;
  private _isSuccessfulPayment: boolean = false;
  private _unsubscribe: Subject<void> = new Subject();

  constructor(
    private storageService: StorageService,
    private customRouter: CustomRouterService,
    private featureFlagSrv: FeatureFlagService,
    private translate: TranslateService,
    private specificConf: SpecificConfigurationService,
    private seAuthService: SeAuthService,
    private paymentsEndpointsService: PaymentsEndpointsService,
    private loginService: LoginResponseService,
    private proceduresHeaderService: ProceduresHeaderService,
  ) {}

  ngOnInit(): void {
    this.paymentData = this.storageService.paymentData;

    this.setupHeader();

    this.getPendingPayments();
  }

  private setupHeader(): void {
    const { vehicles } = this.storageService.getPaymentVehicles();

    this.proceduresHeaderService.setupPaymentHeader(vehicles);
  }

  private getPendingPayments(): void {
    const request: GetPendingPaymentsRequest = {
      tipusAccess:
        this.loginService.user?.tipusAccess || IdentificationType.NOM_PROPI,
      idPersTitular: this.loginService?.user?.idPersTitular || '',
      provisional: false,
      exerciciActual: !this.specificConf.isProvisional,
      matricula: this.loginService?.user?.matricula || null,
    };

    this.paymentsEndpointsService
      .getPendingPayments(request)
      .pipe(takeUntil(this._unsubscribe))
      .subscribe((value) => (this._rebutsPendents = value));
  }

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }

  navigateToList(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  setTitle(ev: Event): void {
    const event = (ev as CustomEvent<PaymentResult>)?.detail;

    if (event.result === PaymentResultStates.OK) {
      this._isSuccessfulPayment = true;
      this.setupSurvey();
    } else {
      this._isSuccessfulPayment = false;
    }

    this.storageService.setVehiclesSelected(null);
  }

  protected navigateToCreateDomiciliation(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.DOMICILIATION);
  }

  protected navigateToUpdateDomiciled(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.MODIFY_DOMICILED);
  }

  private setupSurvey(): void {
    const eventDetail = {
      familia: this.surveyFamily,
      tramite: this.procedurePaymentId,
      querySelector: '.payments-confirmation__survey-container',
      produccion:
        this.seAuthService.getSessionStorageUser().environment === 'pro',
    };

    document.dispatchEvent(
      new CustomEvent('showSurveyEvent', { detail: eventDetail }),
    );
  }
}
