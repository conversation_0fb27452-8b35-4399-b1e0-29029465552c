import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DeferralPaymentsComponent } from './deferral-payments.component';
import {
  SePanelModule,
  SeButtonModule,
  SeTableModule,
  SeEmptyStateModule,
  SeAlertModule,
} from 'se-ui-components-mf-lib';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { DeferralPaymentsConfirmationComponent } from './deferral-payments-confirmation';
import { DEFERRAL_ROUTES } from './models/deferral-payments.model';
import { CivilServantRequiredDocBlockModule } from '@app/shared/components/civil-servant-required-doc-block';

const routes: Routes = [
  { path: '', redirectTo: DEFERRAL_ROUTES.RESUM, pathMatch: 'full' },
  {
    path: DEFERRAL_ROUTES.RESUM,
    component: DeferralPaymentsComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
    },
  },
  {
    path: DEFERRAL_ROUTES.CONFIRMACIO,
    component: DeferralPaymentsConfirmationComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      isElementVisible: false,
      isHeaderVisible: false,
    },
  },
];

@NgModule({
  declarations: [
    DeferralPaymentsComponent,
    DeferralPaymentsConfirmationComponent,
  ],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    SePanelModule,
    SeButtonModule,
    SeTableModule,
    SeEmptyStateModule,
    SeAlertModule,
    CivilServantRequiredDocBlockModule,
  ],
})
export class DeferralPaymentsModule {}
