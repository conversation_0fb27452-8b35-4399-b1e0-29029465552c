import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';

import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
  type Nullable,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import type {
  RecursResumResponse,
  RequestUpdateResourcesTramit,
} from './reason-request.model';

@Injectable({
  providedIn: 'root',
})
export class ReasonRequestEndpointService {
  constructor(private readonly httpService: SeHttpService) {
    // Intencionadament buit
  }

  updateResourcesTramit(
    idTramit: string,
    request: RequestUpdateResourcesTramit,
  ): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `recurs/${idTramit}/motius`,
      method: 'put',
      body: request,
    };

    return this.httpService.put<SeHttpResponse>(httpRequest);
  }

  getRecursResum(idTramit: string): Observable<Nullable<RecursResumResponse>> {
    return this.httpService
      .get<SeHttpResponse<RecursResumResponse>>({
        baseUrl: environment.baseUrlCo2,
        url: `recurs/${idTramit}/resum`,
        method: 'get',
      })
      .pipe(
        map(
          (response: SeHttpResponse<RecursResumResponse>) => response.content,
        ),
      );
  }
}
