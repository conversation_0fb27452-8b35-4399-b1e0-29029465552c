import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  AppRoutes,
  ConveniatVerificationResponse,
  IdentificationType,
  LoginRequest,
  LoginResponse,
  BASE_URL,
} from '@core/models';
import {
  CustomRouterService,
  IdentificationsService,
  LoginResponseService,
  StorageService,
} from '@core/services';
import {
  Nullable,
  SeMessageService,
  SeValidations,
} from 'se-ui-components-mf-lib';
import { IdentificationData, Taxpayer } from '../models';
import { TranslateService } from '@ngx-translate/core';
import { BaseUrlService } from '@app/core/services/url-atc/url-atc.service';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';

@Component({
  selector: 'app-conveniat-identification',
  templateUrl: './conveniat-identification.component.html',
})
export class ConveniatIdentificationComponent implements OnInit, OnDestroy {
  @Input() set showRepresentative(value: boolean) {
    if (value) {
      this.showThirdPersonForm = value;
      this.agreedForm
        .get('identificationType')
        ?.setValue(IdentificationType.CONVENIAT);
    }
  }

  get isRepresentative(): boolean {
    return this.identificationsSrv.isRepresentative;
  }

  IdentificationType = IdentificationType;
  statementOption: Nullable<Taxpayer>;
  identificationData!: IdentificationData;
  showThirdPersonForm: boolean = false;
  canContinue: boolean = false;

  agreedForm: FormGroup = new FormGroup({
    identificationType: new FormControl(IdentificationType.NOM_PROPI),
    plate: new FormControl(null, Validators.required),
    nif: new FormControl(null, [
      Validators.required,
      !this.isRepresentative
        ? SeValidations.dniNie('ERROR.LOGIN_SIMPLE.ERROR_NOT_PERSONA_FISICA')
        : SeValidations.dniNieCif(),
    ]),
    name: new FormControl(
      null,
      this.isRepresentative ? Validators.required : null,
    ),
    responsibleStatement: new FormControl(
      this.identificationData?.identificationType ===
      IdentificationType.CONVENIAT
        ? this.identificationData?.presenterNif
        : null,
    ),
  });

  get name(): string {
    return this.agreedForm.get('name')?.value as string;
  }

  get nif(): string {
    return (this.agreedForm.get('nif')?.value as string)?.toUpperCase();
  }

  get plate(): string {
    return (this.agreedForm.get('plate')?.value as string)?.toUpperCase();
  }

  scoringValidation: boolean = false;

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private loginResponseSrv: LoginResponseService,
    private customRouter: CustomRouterService,
    private store: StorageService,
    private translate: TranslateService,
    private msgService: SeMessageService,
    private identificationsSrv: IdentificationsService,
    private readonly baseUrlService: BaseUrlService,
  ) {}

  ngOnInit(): void {
    if (this.isRepresentative) {
      this.agreedForm.valueChanges
        .pipe(
          distinctUntilChanged(
            (prev, curr) => JSON.stringify(prev) === JSON.stringify(curr),
          ),
          debounceTime(200),
          takeUntil(this.unsubscribe),
        )
        .subscribe((value) => {
          this.canContinue = false;
          // Si l’usuari informa NIF + Nom: l’accés es realitzarà amb perfil de representant
          // Si l’usuari informa Matrícula + NIF: l’accés es realitzarà amb perfil de conveniat
          // Si excepcionalment l’usuari informa alhora els 3 camps (Matrícula+NIF+Nom):
          // el sistema ignorarà el valor de la matrícula, de tal forma que l’accés es realitzarà amb perfil de representant.
          this.setRequiredControl('name');
          this.setRequiredControl('plate');

          if (value.plate) {
            this.resetControl('name');
          } else if (value.name) {
            this.resetControl('plate');
          }
        });
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  continue(): void {
    if (
      this.agreedForm.get('identificationType')?.value ===
        IdentificationType.CONVENIAT &&
      !this.showThirdPersonForm
    ) {
      this.showThirdPersonForm = true;
    } else {
      this.onNavigateToPadroList();
    }
  }

  goBack(): void {
    this.msgService.resetMessages();
    this.showThirdPersonForm = false;
  }

  onNavigateToPadroList(): void {
    this.msgService.resetMessages();
    if (this.scoringValidation) {
      this.loginRepresentant();
    } else if (
      this.agreedForm.get('identificationType')?.value ===
      IdentificationType.CONVENIAT
    ) {
      this.loginConveniat();
    } else {
      this.loginNomPropi();
    }
  }

  confirmIsValid(): void {
    this.canContinue = false;
    this.scoringValidation = false;
    this.msgService.resetMessages();
    if (!this.nif) return;
    // Representant
    if (this.name) {
      this.onScoringValidation();
    } else if (this.plate) {
      //conveniat
      this.onConveniatValidation();
    }
  }

  private loginRepresentant(): void {
    const body: LoginRequest = {
      nom: this.name,
      nifTitular: this.nif,
    };
    this.identificationsSrv
      .loginCo2(body, IdentificationType.REPRESENTATIVE, AppRoutes.RECEIPTS)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((response) => {
        if (response?.content) {
          this.store.taxpayerNIF = this.nif;
          this.store.taxpayerName = this.name;
          this.store.loginCase = IdentificationType.REPRESENTATIVE;
        }
      });
  }

  private loginConveniat(): void {
    const body: LoginRequest = {
      matricula: this.plate?.toUpperCase(),
      nifTitular: this.nif,
      nom: this.isRepresentative ? this.name : undefined,
    };
    this.loginResponseSrv.login(body).subscribe((response: LoginResponse) => {
      if (response.content.validAccess) {
        this.store.taxpayerNIF = this.nif;
        this.store.taxpayerName = response.content.nombreTitular;
        this.store.licensePlate = this.plate;
        this.store.loginCase = IdentificationType.CONVENIAT;
        this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
      }
    });
  }

  private loginNomPropi(): void {
    this.identificationsSrv
      .loginCo2({}, IdentificationType.NOM_PROPI)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.store.loginCase = IdentificationType.NOM_PROPI;
        this.baseUrlService.goToElMeuEspai(`${BASE_URL}/${AppRoutes.RECEIPTS}`);
      });
  }

  private onScoringValidation(): void {
    this.loginResponseSrv
      .validateScoring(this.nif, this.name)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(({ match: valid, fullName }) => {
        if (valid) {
          this.agreedForm.get('name')?.setValue(fullName, { emitEvent: false });
          this.agreedForm.get('plate')?.setValue(null, { emitEvent: false });
          this.agreedForm.updateValueAndValidity();
          this.verifyIfIsRepresentative(this.nif);
        }
      });
  }

  private onConveniatValidation(): void {
    this.loginResponseSrv
      .confirmRelationNifPlate(this.nif, this.plate)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((response: ConveniatVerificationResponse) => {
        if (response.content) {
          const {
            resultat,
            hoursBloqueig,
            minsBloqueig,
            bloquejat,
            tipusBloqueig,
          } = response.content;

          this.canContinue = !bloquejat ? resultat : false;

          if (bloquejat || !resultat) {
            this.setMessageErrorWithHoursAndMinutes(
              tipusBloqueig,
              hoursBloqueig,
              minsBloqueig,
            );
          }
        }
      });
  }

  private setMessageErrorWithHoursAndMinutes = (
    lockType: string,
    hours?: string,
    minutes?: string,
  ): void => {
    let title = 'SE_PADRO_CO2.CONVENIAT.ERROR_VALIDATION.TITLE';
    let subtitle = 'SE_PADRO_CO2.CONVENIAT.ERROR_VALIDATION.SUBTITLE';

    if (lockType) {
      title = this.translate.instant(lockType, {
        ...(hours && { hours }),
        ...(minutes && { minutes }),
      });
      subtitle = '';
    }

    this.msgService.addMessages([{ severity: 'error', title, subtitle }]);
  };

  private verifyIfIsRepresentative(representativeNIF: string): void {
    this.identificationsSrv
      .verifyIfIsRepresentative(representativeNIF)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((isRepresentative) => {
        this.canContinue = isRepresentative;
        this.scoringValidation = isRepresentative;
        if (!isRepresentative) {
          this.msgService.addMessages([
            {
              severity: 'error',
              title:
                'SE_PADRO_CO2.MODULE_IDENTIFICATION.ALERT.IS_NOT_REPRESENTATIVE',
            },
          ]);
        }
      });
  }

  private resetControl(controlName: string): void {
    this.agreedForm.get(controlName)?.clearValidators();
    this.agreedForm
      .get(controlName)
      ?.updateValueAndValidity({ emitEvent: false });
  }

  private setRequiredControl(controlName: string): void {
    this.agreedForm.get(controlName)?.setValidators(Validators.required);
    this.agreedForm
      .get(controlName)
      ?.updateValueAndValidity({ emitEvent: false });
  }
}
