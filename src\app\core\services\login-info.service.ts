import { Injectable } from '@angular/core';
import { SeLoginService } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class LoginInfoService {
  constructor(private seLoginService: SeLoginService) {}

  async getSeuUrl(): Promise<string> {
    const response = await this.seLoginService.getSeuUrlByEnv();
    if (response?.content) {
      return response.content;
    }

    return '';
  }
}
