import { Injectable } from '@angular/core';
import {
  AddressTramitRequest,
  AddressTramitResponse,
  OrigenEnum,
} from '@app/shared/models';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class NotificacionsEndpointService {
  constructor(private httpService: SeHttpService) {}

  processAddressTramit = (
    idTramit: string,
    request: AddressTramitRequest,
  ): Observable<AddressTramitResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `recurs/${idTramit}/adreca`,
      method: 'put',
      body: request,
      clearExceptions: true,
    };

    return this.httpService.put(httpRequest);
  };

  processAddressTramitMiro(
    request: AddressTramitRequest,
  ): Observable<AddressTramitResponse> {
    const httpRequest: SeHttpRequest = {
      headers: { origen: OrigenEnum.RECURS },
      baseUrl: environment.baseUrlContribuent,
      url: `/adreca/tramitacio`,
      method: 'post',
      body: request,
      clearExceptions: true,
    };

    return this.httpService.post(httpRequest);
  }
}
