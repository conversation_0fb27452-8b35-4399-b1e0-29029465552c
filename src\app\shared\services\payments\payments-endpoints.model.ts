import { AtesaPhoneData, IdentificationType } from '@app/core/models';
import { VehiclePayment, VehicleSelected } from '@shared/models';
import { Nullable } from 'se-ui-components-mf-lib';

export interface CalculatePaymentRequest {
  idPersTitular: string;
  provisional: boolean;
  matricules?: string[];
  vehicles?: VehicleSelected[];
  csv?: string; //pendentNotificacio
  continuarAmbValids?: boolean; //pagamentExecutivaError && pagamentAltresError
  tipusAccess: string;
  trucadaTelefonica?: AtesaPhoneData;
}

export interface CalculatePaymentResponse {
  allPendentNotificacio: boolean;
  pendentNotificacio: boolean;
  pagamentExecutivaError: boolean;
  pagamentAltresError: boolean;
  vehicles: VehiclePayment[];
  idTramitacio: string;
  total: number;
  idBBDD?: string;
  refPago?: string;
  idPagament: string;
}

export interface GetPendingPaymentsRequest {
  tipusAccess: IdentificationType;
  idPersTitular: string;
  provisional: boolean;
  exerciciActual: boolean;
  matricula: Nullable<string>;
}
