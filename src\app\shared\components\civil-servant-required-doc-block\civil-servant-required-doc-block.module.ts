import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeInputModule,
  SePanelModule,
  SpinnerComponent,
  SeButtonModule,
} from 'se-ui-components-mf-lib';
import { CivilServantRequiredDocBlockComponent } from './civil-servant-required-doc-block.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { LazyElementsModule } from '@angular-extensions/elements';
import { environment } from '@environments/environment';

@NgModule({
  declarations: [CivilServantRequiredDocBlockComponent],
  exports: [CivilServantRequiredDocBlockComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SeInputModule,
    ReactiveFormsModule,
    FormsModule,
    SePanelModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-documents-upload-files',
          url: environment.wcUrlDocumentsJs,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
    SeButtonModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class CivilServantRequiredDocBlockModule {}
