import { SeHttpResponse } from 'se-ui-components-mf-lib';

export interface GetUserDataByIdPers {
  idPersCens: string;
}

export interface GetUserInfoResponse extends SeHttpResponse {
  content: UserInfo;
}

export interface UserInfo {
  nif: string;
  nombreCompleto: string;
  nombre: string;
  primerApellido: string;
  segundoApellido: string;
  direccionFiscal: Direccion;
  direccionNotificacion: Direccion;
  notificacionElectronica: DatoContacto;
  notificacionCO2: DatoContacto;
  notificacionNEGeneral: DatoContacto;
  showDireccionNotificacion: boolean;
  showNE: boolean;
  canalElectronico: boolean;
  isAvis: boolean;
  isObligado: boolean;
}

export interface Direccion {
  idAdress: string;
  viaYPortalYPisoYLetra: string;
  cpYMunicipio: string;
  provincia: string;
  pais: string;
  direNoConsolidat: boolean;
  concatDireccion: string;
}

export interface DatoContacto {
  telefono: string;
  email: string;
  esMovil: boolean;
}

export interface TitularPanelData {
  panelTitle: string;
  fields: TitularPanelFields[];
}

export interface TitularPanelFields {
  label: string;
  value: string;
}
