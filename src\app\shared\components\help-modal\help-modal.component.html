<se-modal
  [data]="data"
  (modalOutputEvent)="closeModal()"
  (modalSecondaryButtonEvent)="closeModal()"
>
  <div class="d-flex flex-column">
    <form [formGroup]="form" class="align-items-center">
      <se-dropdown
        formControlName="value"
        [id]="'dropdown'"
        [placeholder]="'UI_COMPONENTS.SELECT.PLACEHOLDER' | translate"
        [label]="'UI_COMPONENTS.SELECT.PLACEHOLDER' | translate"
        [options]="options"
        [optionLabel]="'label'"
        [optionValue]="'id'"
        [showClear]="false"
      ></se-dropdown>
    </form>
    <h3 *ngIf="form.get('value')?.value">
      {{ form.get('value')?.value | translate }}
    </h3>
  </div>

  <!--  -->
  <ng-container [ngSwitch]="form.get('value')?.value">
    <ng-template ngSwitchDefault>
      <div class="d-flex flex-column">
        <p class="mt-4">
          <strong>
            {{
              'SE_PADRO_CO2.MODAL.HELP_MODAL.DOMICILED.REQUEST_DOMICILIATION'
                | translate
            }}
          </strong>
        </p>
        <span class="mt-2">{{
          'SE_PADRO_CO2.MODAL.HELP_MODAL.DOMICILED.DOMICILIATION_INFO'
            | translate
        }}</span>
        <p class="mt-3">
          <strong>
            {{
              'SE_PADRO_CO2.MODAL.HELP_MODAL.DOMICILED.MODIFY_DOMICILIATION'
                | translate
            }}
          </strong>
        </p>
        <span class="mt-2">
          {{
            'SE_PADRO_CO2.MODAL.HELP_MODAL.DOMICILED.MODIFY_DOMICILIATION_INFO'
              | translate
          }}
        </span>
        <p class="mt-3">
          <strong>
            {{
              'SE_PADRO_CO2.MODAL.HELP_MODAL.DOMICILED.REQUEST_CANCELLATION'
                | translate
            }}
          </strong>
        </p>
        <span class="mt-2"
          >{{
            'SE_PADRO_CO2.MODAL.HELP_MODAL.DOMICILED.CANCELLATION_INFO'
              | translate
          }}
        </span>
      </div>
    </ng-template>
    <ng-template [ngSwitchCase]="OPTIONS.PAYMENTS">
      <p class="mt-4">
        <strong>
          {{
            'SE_PADRO_CO2.MODAL.HELP_MODAL.PAYMENTS.TELEMATIC_PAYMENT_TITLE'
              | translate
          }}
        </strong>
      </p>
      <span class="mt-2 d-block">
        {{
          'SE_PADRO_CO2.MODAL.HELP_MODAL.PAYMENTS.TELEMATIC_PAYMENT_INFO'
            | translate
        }}
      </span>
      <span class="mt-2 d-block">
        {{
          'SE_PADRO_CO2.MODAL.HELP_MODAL.PAYMENTS.PAYMENT_DEADLINE'
            | translate
              : {
                  year: specificConfigurationService.currentExercise,
                  endDay: fiPeriodeVoluntariPagament.getDate(),
                  endMonth: fiPeriodeVoluntariPagament.toLocaleString(
                    currentLang,
                    {
                      month: 'long',
                    }
                  ),
                  endYear: fiPeriodeVoluntariPagament.getFullYear(),
                }
        }}
      </span>
      <p class="mt-3">
        <strong>
          {{
            'SE_PADRO_CO2.MODAL.HELP_MODAL.PAYMENTS.DEFERMENT_INSTALLMENT_TITLE'
              | translate
          }}
        </strong>
      </p>
      <span class="mt-2 d-block">
        {{
          'SE_PADRO_CO2.MODAL.HELP_MODAL.PAYMENTS.DEFERMENT_INSTALLMENT_INFO'
            | translate
        }}
      </span>
      <span class="mt-2 d-block">
        {{
          'SE_PADRO_CO2.MODAL.HELP_MODAL.PAYMENTS.DEFERMENT_INSTALLMENT_NOTICE'
            | translate
        }}
      </span>
    </ng-template>
    <ng-template [ngSwitchCase]="OPTIONS.RECURS_Y_REA">
      <span class="mt-2">
        {{
          'SE_PADRO_CO2.MODAL.HELP_MODAL.RECURS_Y_REA.REA_RECURS_INFO'
            | translate
        }}
      </span>
      <p class="mt-4">
        <strong>
          {{
            'SE_PADRO_CO2.MODAL.HELP_MODAL.RECURS_Y_REA.APPEAL_TITLE'
              | translate
          }}
        </strong>
      </p>
      <span
        class="mt-2"
        [innerHTML]="
          'SE_PADRO_CO2.MODAL.HELP_MODAL.RECURS_Y_REA.APPEAL_INFO'
            | translate
              : {
                  year: specificConfigurationService.currentExercise,
                  endDay: fiTerminiRecurs.getDate(),
                  endMonth: fiTerminiRecurs.toLocaleString(currentLang, {
                    month: 'long',
                  }),
                  endYear: fiTerminiRecurs.getFullYear(),
                }
        "
      ></span>
      <p class="mt-3">
        <strong>
          {{
            'SE_PADRO_CO2.MODAL.HELP_MODAL.RECURS_Y_REA.REA_TITLE' | translate
          }}
        </strong>
      </p>
      <span class="mt-2 d-block">
        {{ 'SE_PADRO_CO2.MODAL.HELP_MODAL.RECURS_Y_REA.REA_INFO' | translate }}
      </span>
    </ng-template>
  </ng-container>

  <!--  -->
</se-modal>
