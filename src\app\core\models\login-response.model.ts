import { SeHttpResponse, SeUser } from 'se-ui-components-mf-lib';
import { IdentificationType } from '.';

export interface LoginResponse extends SeHttpResponse {
  content: LoginUser;
}

export interface SimpleLoginResponse extends SeHttpResponse {
  content?: ContentSimpleLogin;
}

interface Blocked {
  hoursBloqueig?: string;
  minsBloqueig?: string;
}

interface AgreementVerification extends Blocked {
  resultat: boolean;
  bloquejat: boolean;
  tipusBloqueig: string;
}

export interface ConveniatVerificationResponse extends SeHttpResponse {
  content?: AgreementVerification;
}

export interface LoginUser extends Blocked {
  tipusAccess: IdentificationType;
  idPersTitular?: string;
  nombreTitular: string;
  esTitularJuridic: boolean;
  validAccess: boolean;
  tipusBloqueig: string;
}

export interface ContentSimpleLogin extends Blocked {
  loginOk: boolean;
  tipusError: string;
  intent: number;
  usuario: SeUser;
}

export interface GetScoringHttpResponse extends SeHttpResponse {
  content?: {
    match: boolean;
    nif: string;
    fullName: string;
    percentageSim: number;
    indSotaLlindar: string;
  };
}
