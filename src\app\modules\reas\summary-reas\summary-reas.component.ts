import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AppRoutes,
  FunctionalModuleEnum,
  IdentificationType,
  ResourceProcessDocument,
} from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  StorageService,
} from '@core/services';
import { map, Observable, of, Subject, switchMap, takeUntil } from 'rxjs';
import {
  Column,
  iDocumentPadoct,
  Nullable,
  Row,
  SeAuthService,
  SeUser,
} from 'se-ui-components-mf-lib';
import { RequestSummary } from '@shared/components';

import { ProcessNameToDisplayInPresentationReceipt } from '@modules/presentation-receipt';
import {
  AddressTramitRequest,
  Motius,
  VehiclesSelectedInfo,
} from '@app/shared/models';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';

import { SummaryReasEndpointService } from './services/summary-reas-endpoint.service';
import { ReasService } from '../services/reas.service';
import {
  ProcedureSavedReas,
  ReasPresentationResponse,
  ReasResumResponse,
} from './models/summary-reas.model';
import { SummaryReasService } from './services';
import { MaskIbanService } from '@app/core/services/mask-iban';
import { NotificationsEndpointsService } from '../notifications/services/notifications-endpoints.service';
import { ContribuentOutputData } from '@app/shared/models/notifications.model';
import { AccountNumberSummary } from '@app/shared/components/account-number-summary';

@Component({
  selector: 'app-summary-reconsideration',
  templateUrl: './summary-reas.component.html',
  styleUrls: ['./summary-reas.component.scss'],
})
export class SummaryReasComponent implements OnInit, OnDestroy {
  private unsubscribe: Subject<void> = new Subject();
  data: RequestSummary | undefined;
  reas: Nullable<Motius>;

  documentColumns: Column[] = [];
  documentRows: Row[] = [];

  private user: SeUser | undefined;
  accountNumberData: AccountNumberSummary | undefined;
  idTramit: Nullable<string>;
  notificationsData: Nullable<ContribuentOutputData>;
  vehiclesInfo: Nullable<VehiclesSelectedInfo>;
  plates: string[] = [];

  protected idFunctionalModule: FunctionalModuleEnum =
    FunctionalModuleEnum.RECURS;
  protected disableContinueButton: boolean = false;

  get isLoginSimple(): boolean {
    return this.loginSrv.user.tipusAccess === IdentificationType.LOGIN_SIMPLE;
  }

  constructor(
    private summaryReconsidetarionEndpointService: SummaryReasEndpointService,
    private seAuthService: SeAuthService,
    private storageData: StorageService,
    private customRouter: CustomRouterService,
    private summaryReasService: SummaryReasService,
    private reasService: ReasService,
    private notificationsEndpointService: NotificationsEndpointsService,
    private loginSrv: LoginResponseService,
    private procedureHeaderService: ProceduresHeaderService,
    private maskIbanService: MaskIbanService,
  ) {
    // Intencionadamente vacío
  }

  ngOnInit(): void {
    this.user = this.seAuthService.getSessionStorageUser();
    this.vehiclesInfo = this.vehiclesInfo =
      this.reasService.getVehiclesSelectedInfo(this.user.nif);
    this.idTramit = this.storageData.getReasVehicles().id;
    this.notificationsData = this.storageData.getNotificationsData();
    this.plates = this.vehiclesInfo?.matriculas || [];
    this.procedureHeaderService.setupReasHeader(this.vehiclesInfo?.matriculas);

    this.getResumData();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private getResumData(): void {
    if (this.idTramit) {
      this.summaryReconsidetarionEndpointService
        .getReasResumInfo(this.idTramit)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result: ReasResumResponse) => {
          if (result?.content) {
            this.data = this.summaryReasService.setDataResponseInDataObject(
              result.content,
              this.vehiclesInfo,
              this.isLoginSimple,
              false,
            );
            this.accountNumberData = {
              iban: this.maskIbanService.transform(
                result?.content.ibanNumeroCompte,
                '',
              ),
              declaracioNumeroCompte: !!result?.content?.declaracioNumeroCompte,
              documentNumeroCompte: result?.content.documentNumeroCompte,
            };

            this.reas = result?.content.motius?.[0];
            this.getDocumentColumns();
          }
        });
    }
  }

  getDocumentColumns(): void {
    this.documentColumns = this.summaryReasService.getDocumentColumns();
    this.getDocumentRows();
  }

  getDocumentRows(): void {
    this.documentRows = this.summaryReasService.getDocumentRows(this.reas);
  }

  onGoBackButtonClick(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.REAS_NOTIFICATION);
  }

  onContinueButtonClick(): void {
    if (this.idTramit) {
      if (this.notificationsData?.address) {
        this.proccessPostalAddressAndSubmitProcess(
          this.idTramit,
          this.notificationsData.address,
        );
      } else {
        this.submitProccess(this.idTramit);
      }
    }
  }

  private proccessPostalAddressAndSubmitProcess(
    idTramit: string,
    request: Partial<AddressTramitRequest>,
  ): void {
    this.notificationsEndpointService
      .processAddressTramit(idTramit, new AddressTramitRequest(request))
      .pipe(takeUntil(this.unsubscribe))
      .subscribe({
        complete: () => this.submitProccess(idTramit, request),
      });
  }

  private submitProccess(
    idTramit: string,
    request?: Partial<AddressTramitRequest>,
  ): void {
    this.summaryReconsidetarionEndpointService
      .setReasPresentation(idTramit)
      .pipe(
        takeUntil(this.unsubscribe),
        switchMap((response) =>
          this.proccessPostalAddressMiro(response, request),
        ),
      )
      .subscribe((result: ReasPresentationResponse) => {
        if (result?.content) {
          this.storageData.clearReasonsSelected();
          this.storageData.clearIdTramit();
          this.storageData.deleteNotificationsData();
          this.setDataToShowThemInPresentationReceipt(result.content);
          this.customRouter.navigateByBaseUrl(AppRoutes.PRESENTATION_RECEIPT);
        }
      });
  }

  private proccessPostalAddressMiro(
    response: ReasPresentationResponse,
    address?: Partial<AddressTramitRequest>,
  ): Observable<ReasPresentationResponse> {
    const isCivilServant: boolean =
      this.storageData.profileUser === IdentificationType.CIVIL_SERVANT;
    const idTramitacioOrigen: string = response?.content?.idTramit || '';
    const idPersCens: string | undefined = isCivilServant
      ? this.loginSrv.user?.idPersTitular
      : undefined;
    const request = new AddressTramitRequest({
      ...address,
      idTramitacioOrigen,
      idPersCens,
    });

    if (response?.content && request?.teExpedient && idTramitacioOrigen) {
      return this.notificationsEndpointService
        .processAddressTramitMiro(request)
        .pipe(
          takeUntil(this.unsubscribe),
          map(() => response),
        );
    }
    return of(response);
  }

  private setDataToShowThemInPresentationReceipt(
    response: ProcedureSavedReas,
  ): void {
    const processedAddressResponse =
      this.notificationsData?.processedAddressResponse || null;
    const contactData = this.notificationsData?.contactData || null;

    this.storageData.setResourceProcessDocumentData({
      receipt: {
        idPadoct: response.idJustificant,
        nom: response.fileName,
      } as iDocumentPadoct,
      idFunctionalModule: ProcessNameToDisplayInPresentationReceipt.Rea,
      processedAddressResponse: processedAddressResponse,
      contactData: contactData,
    } as ResourceProcessDocument);
    this.storageData.processNameToDisplayPresentationReceipt =
      ProcessNameToDisplayInPresentationReceipt.Rea;
  }

  protected onDisableContinueButton($event: boolean): void {
    this.disableContinueButton = $event;
  }
}
