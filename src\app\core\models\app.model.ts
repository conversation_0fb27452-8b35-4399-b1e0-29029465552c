import { MfConfiguration } from 'se-ui-components-mf-lib';

export enum ValidateButtonEnum {
  VALIDATE = 'VALIDATE',
  VALID = 'VALID',
  NOT_VALID = 'NOT_VALID',
  VALIDATING = 'VALIDATING',
}

export const MF_CO2_CONFIGURATION_STORAGE = 'mfConfigurationPersonal_PADRO_CO2';
export const MF_CONFIGURATION_STORAGE = 'mfConfigurationPersonal';

export interface MfSpecificConfiguration extends MfConfiguration {
  confEspecifica: SpecificConfiguration;
}

export interface SpecificConfiguration {
  dates: ConfigurationDates;
  llistaExercicis: string[];
  percentatgeDescompte: number;
  importMinAjornamentFraccionament: number;
}

export interface ConfigurationDates {
  fiTerminiRecurs: string;
  publicacioPadroProvisional: string;
  limitAlegacions: string;
  limitDomiciliacions: string;
  publicacioPadroDefinitiu: string;
  limitModificacionsCompte: string;
  fiPeriodeVoluntariPagament: string;
  iniciFraccionamentAjornament: string;
}

export enum FunctionalModuleEnum {
  RECURS = 'RECURS',
  REAS = 'REAS',
  ALLEGATIONS = 'ALLEGATIONS',
  DOMICILIACIONS = 'DOMICILIACIONS',
  PAGAMENTS = 'PAGAMENTS',
  PAGAMENT_LIQUIDACIONS = 'PAGAMENT_LIQUIDACIONS',
  DEFERRAL_PAYMENTS = 'DEFERRAL_PAYMENTS',
}
