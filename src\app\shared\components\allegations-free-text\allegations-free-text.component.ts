import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import {
  Column,
  iDocumentPadoct,
  SeDocumentsService,
  SePanel,
  UploadAcceptFormats,
} from 'se-ui-components-mf-lib';

import { Form<PERSON>uilder, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import {
  AllegationsDocumentsService,
  ContestableActDocument,
} from '../allegations-options';
import {
  AllegationsFormData,
  DocSigedaCodes,
  DocSubtypeCodes,
  FREE_TEXT_BLOCK_CONTROL_NAME,
} from './models/allegations-free-text.model';
import {
  MODAL_TABLE_COLUMNS,
  TABLE_COLUMNS_DOC_TYPE_FIRST_ELEMENT,
} from '@app/shared/models/upload-documents.model';
import { DOCUMENTS_STATUS_TO_EXCLUDE } from '@app/core/models';

const MIN_CHARACTER_LENGTH = 25;

@Component({
  selector: 'app-allegations-free-text',
  templateUrl: './allegations-free-text.component.html',
})
export class AllegationsFreeTextComponent implements OnInit, OnDestroy {
  @Input({ required: true }) procedureId!: string;
  @Input({ required: true }) functionalModule!: string;
  @Input() allegationValue?: string;
  @Input() panelTitle: string =
    'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.PANEL_TITLE';
  @Input() panelSubtitle: string =
    'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.PANEL_SUBTITLE';
  @Input() freeTextAreaLabel: string =
    'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.FREE_TEXT_LABEL';
  @Input() docSectionTitle: string =
    'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.DOC_SECTION.LABEL';
  @Input() docSectionSubtitle: string =
    'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.DOC_SECTION.SUBTITLE';

  @Output() freeTextForm: EventEmitter<AllegationsFormData> =
    new EventEmitter();

  allegationsPanelData!: SePanel;
  allegationsForm!: FormGroup;
  allegationsDocuments: iDocumentPadoct[] = [];

  documentsSigedaDescriptions: ContestableActDocument[] = [];
  documentsSizeLimit: number = 10240; // 10 MB
  documentsAcceptedTypes: string[] = [
    UploadAcceptFormats.pdf,
    UploadAcceptFormats.jpg,
    UploadAcceptFormats.doc,
    UploadAcceptFormats.docx,
  ];
  format: string = '';
  fileSize: string = '';

  FREE_TEXT_BLOCK_CONTROL_NAME = FREE_TEXT_BLOCK_CONTROL_NAME;
  DocSubtypeCodes = DocSubtypeCodes;
  statusesToExclude: string[] = DOCUMENTS_STATUS_TO_EXCLUDE;

  isDocumentRequired: boolean = true;

  private _unsubscribe: Subject<void> = new Subject();

  constructor(
    public allegationsDocService: AllegationsDocumentsService,
    private translateService: TranslateService,
    private fb: FormBuilder,
    private seDocumentsService: SeDocumentsService,
  ) {}

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }

  ngOnInit(): void {
    this.format = this.seDocumentsService.setCommaFileFormat(
      this.documentsAcceptedTypes,
    );

    this.setFileSize();

    this.setupFreeTextBlockData();

    this.setupForm();

    this.recoverAllegationsDataFromStorage();
  }

  private setFileSize(): void {
    this.fileSize = (this.documentsSizeLimit / 1000).toFixed(0);
  }

  private setupFreeTextBlockData(): void {
    this.allegationsPanelData = {
      title: this.panelTitle,
      colapsible: true,
    };

    this.documentsSigedaDescriptions = [
      {
        type: DocSigedaCodes.RECLAMACIO_JUSTIFICANT,
        subtype: DocSubtypeCodes.RECLAMACIO_JUSTIFICANT,
        description: this.translateService.instant(
          `SE_PADRO_CO2.SIGEDAS.${DocSigedaCodes.RECLAMACIO_JUSTIFICANT}-${DocSubtypeCodes.RECLAMACIO_JUSTIFICANT}`,
        ),
      },
    ];
  }

  private setupForm(): void {
    this.allegationsForm = this.fb.group({
      [FREE_TEXT_BLOCK_CONTROL_NAME]: ['', [Validators.maxLength(1000)]],
    });

    this.allegationsForm
      .get(FREE_TEXT_BLOCK_CONTROL_NAME)
      ?.valueChanges.pipe(takeUntil(this._unsubscribe), debounceTime(500))
      .subscribe(() => {
        this.emitFreeTextForm();
        this.isDocumentRequired = !this.isFreeTextLengthValid();
      });
  }

  private isFreeTextLengthValid(): boolean {
    const minCharacterLength = this.allegationsDocuments.length
      ? 0
      : MIN_CHARACTER_LENGTH;

    return (
      (this.allegationsForm.get(FREE_TEXT_BLOCK_CONTROL_NAME)?.value.length ??
        0) >= minCharacterLength
    );
  }

  private recoverAllegationsDataFromStorage(): void {
    if (!this.allegationValue) return;

    this.setFreeTextValue(this.allegationValue);
  }

  private setFreeTextValue(allegation?: string): void {
    if (allegation) {
      this.allegationsForm
        .get(FREE_TEXT_BLOCK_CONTROL_NAME)
        ?.setValue(allegation);

      this.allegationsForm.updateValueAndValidity();
    }
  }

  private emitFreeTextForm(): void {
    const eventData: AllegationsFormData = {
      isAllegationsFormValid: this.isFormValid(),
      allegationsFormValue: this.allegationsForm.getRawValue(),
      allegationsDocuments: this.allegationsDocuments,
      documentsSigedaDescriptions: this.documentsSigedaDescriptions,
    };

    this.freeTextForm.emit(eventData);
  }

  private isFormValid(): boolean {
    return (
      this.allegationsForm.get(FREE_TEXT_BLOCK_CONTROL_NAME)?.value ||
      this.allegationsDocuments.length
    );
  }

  onAllegationsAddedFiles(event: Event): void {
    this.allegationsDocuments = (event as CustomEvent).detail || [];

    this.emitFreeTextForm();
  }

  getDocumentsTableColumns(): Column[] {
    return [...TABLE_COLUMNS_DOC_TYPE_FIRST_ELEMENT];
  }

  getModalTableColumns(): Column[] {
    const modalTableColumnsObject = JSON.parse(
      JSON.stringify(MODAL_TABLE_COLUMNS),
    );
    if (
      modalTableColumnsObject[2]?.cellConfig &&
      modalTableColumnsObject[2]?.cellConfig['options']
    ) {
      modalTableColumnsObject[2].cellConfig['options'] =
        this.documentsSigedaDescriptions;
    }
    return modalTableColumnsObject;
  }
}
