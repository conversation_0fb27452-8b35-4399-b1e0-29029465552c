import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule, CurrencyPipe } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { DownloadExcelModalModule } from '@app/shared/components/download-excel-modal/download-excel-modal.module';
import { PaymentTypesModalModule } from '@app/shared/components/payment-types-modal/payment-types-modal.module';
import { HideOnCoordinatorDirective } from '@core/directives';
import { environment } from '@environments/environment';
import { NgbActiveModal, NgbModalModule } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule } from '@ngx-translate/core';
import { CellsModule, InfoMessageModule } from '@shared/components';
import {
  SeAlertModule,
  SeButtonDropdownModule,
  SeButtonModule,
  SeConfirmationMessageModule,
  SeDatepickerModule,
  SeDropdownModule,
  SeEmptyStateModule,
  SeInputModule,
  SeModalModule,
  SeMultiselectModule,
  SePaginationModule,
  SePanelModule,
  SeTableModule,
  SeTabsModule,
  SeTagModule,
  SeUserInfoBarModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { AssociatedDocumentationTableComponent } from './associated-documentation/associated-documentation.component';
import { CensusTableModule } from './census-table/census-table.module';
import { GestionsComponent } from './gestions/gestions.component';
import { PagamentsTableComponent } from './pagaments-table/pagaments-table.component';
import { PreviousExercisesModule } from './previous-exercises/previous-exercises.module';
import { ReceiptsComponent } from './receipts.component';
import { RequestsMadeComponent } from './requests-made/requests-made.component';

const routes: Routes = [
  {
    path: '',
    component: ReceiptsComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      isElementVisible: false,
      isHeaderVisible: false,
    },
  },
  {
    path: '**',
    redirectTo: '',
  },
];

@NgModule({
  declarations: [
    ReceiptsComponent,
    AssociatedDocumentationTableComponent,
    RequestsMadeComponent,
    GestionsComponent,
    PagamentsTableComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-gestions-procedures-table',
          url: environment.mfGestionsURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
        {
          tag: 'mf-gestions-payments-table',
          url: environment.mfGestionsURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
    SeButtonModule,
    SeButtonDropdownModule,
    SeTableModule,
    InfoMessageModule,
    SeTabsModule,
    SeInputModule,
    SeConfirmationMessageModule,
    SeUserInfoBarModule,
    SeAlertModule,
    SePanelModule,
    SeDropdownModule,
    SeTagModule,
    SeEmptyStateModule,
    SeDatepickerModule,
    SeMultiselectModule,
    SePaginationModule,
    CensusTableModule,
    PreviousExercisesModule,
    PaymentTypesModalModule,
    CellsModule,
    HideOnCoordinatorDirective,
    SeModalModule,
    DownloadExcelModalModule,
    NgbModalModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [NgbActiveModal, CurrencyPipe],
})
export class ReceiptsModule {}
