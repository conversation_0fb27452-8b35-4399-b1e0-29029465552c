<div>
  <se-panel
    [title]="
      'SE_PADRO_CO2.REAS_PROCESS.NOTIFICATIONS.TRIBUNAL.TITLE' | translate
    "
    class="processing-panel"
  >
    <div class="mb-3">
      {{
        'SE_PADRO_CO2.REAS_PROCESS.NOTIFICATIONS.TRIBUNAL.SUBTITLE' | translate
      }}
    </div>

    <se-link
      target="_self"
      [href]="receivingAgencyLink"
      [target]="'_blank'"
      iconName="matOpenInNewOutline"
      iconPosition="right"
      linkTheme="secondary"
      size="semibold"
      [disabled]="false"
      [ariaLabel]="
        'SE_PADRO_CO2.REAS_PROCESS.NOTIFICATIONS.TRIBUNAL.LINK_LABEL'
          | translate
      "
    >
      <span>{{
        'SE_PADRO_CO2.REAS_PROCESS.NOTIFICATIONS.TRIBUNAL.LINK_LABEL'
          | translate
      }}</span>
    </se-link>
  </se-panel>

  <mf-contribuent-notificacions
    *axLazyElement
    [identificationData]="contribuentWcInput"
    [displayUpdateAddressButtonOnly]="true"
    [updateAddressDuringProcedureOnly]="updateAddressDuringProcedureOnly"
    [origen]="OrigenEnum.REA"
    [panelMsg]="'SE_PADRO_CO2.REAS_PROCESS.NOTIFICATIONS.PANEL_MSG' | translate"
    [allowSafeData]="true"
    [sessionStorageName]="PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA"
    (notificationsOutput)="onContribuentOutput($event)"
  ></mf-contribuent-notificacions>

  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button
      type="button"
      btnTheme="secondary"
      (onClick)="onGoBackButtonClick()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.BACK' | translate }}
    </se-button>

    <se-button
      type="submit"
      btnTheme="primary"
      (onClick)="onContinueButtonClick()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.CONTINUE' | translate }}
    </se-button>
  </div>
</div>
