import { Component, Input } from '@angular/core';
import { Nullable } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-detail-element-dashed',
  templateUrl: './detail-element-dashed.component.html',
  styleUrls: ['./detail-element-dashed.component.scss'],
})
export class DetailElementDashedComponent {
  @Input() title: Nullable<string>;

  @Input() description: Nullable<string>;

  @Input() tooltipText: Nullable<string>;

  @Input() linkText: Nullable<string>;

  @Input() bold: boolean = false;

  @Input() linkCommand: Nullable<() => unknown>;

  onLinkClick(): void {
    if (this.linkCommand) {
      this.linkCommand();
    }
  }
}
