import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SeModalModule, SeTableModule } from 'se-ui-components-mf-lib';
import { SharesModalComponent } from './shares-modal.component';

@NgModule({
  declarations: [SharesModalComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SeModalModule,
    SeTableModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class SharesModalModule {}
