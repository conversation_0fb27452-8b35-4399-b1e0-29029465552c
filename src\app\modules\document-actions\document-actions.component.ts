import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { MenuItem } from 'primeng/api';
import { Subject, takeUntil } from 'rxjs';
import { SeButton, SeModal, SeModalService } from 'se-ui-components-mf-lib';

import { DocsService } from 'src/app/core/services/docs.service';
import { ModalSendDocumentationComponent } from '../send-documents-button/modal-send-documentation/modal-send-documentation.component';
import { SendDocumentForm } from '../send-documents-button/send-documents-button.model';
import { MfActionCourseService } from 'src/app/core/services/mf-action-course.service';
import { EndPointService } from 'src/app/core/services/end-points.service';

@Component({
  selector: 'app-document-actions',
  styleUrls: ['document-actions.component.scss'],
  template: `
    <div class="document-actions__web">
      <mf-documents-download-document-button
        *ngIf="fileName"
        [idDocument]="documentsIds"
        [idJustificant]="fileName"
        [btnTheme]="'secondary'"
        [size]="btnSize"
        [disabled]="isDownloadDocumentButtonDisabled"
        [showDownloadMenu]="showDownloadMenu"
        [items]="itemsDropDownload"
        [emitDownloadDocument]="emitDownloadDocument"
        (downloadDocumentEvent)="downloadDocument()"
      />
      <mf-documents-send-documents-button
        [idDocument]="documentsIds"
        [btnTheme]="'secondary'"
        [size]="btnSize"
        [disabled]="isSendDocumentButtonDisabled"
        [emitSendDocument]="emitSendDocument"
        (sendDocumentEvent)="sendDocument($event)"
      />
    </div>

    <div class="document-actions__mobile">
      <se-button-dropdown
        [items]="items"
        [disabled]="disabled"
        [buttonOptions]="buttonOptions"
      >
      </se-button-dropdown>
    </div>
  `,
})
export class DocumentActionsComponent implements OnInit, OnDestroy {
  @Input() btnSize: 'large' | 'default' | 'small' = 'small';
  @Input() documentsIds: string[] = [];
  @Input() iconPosition!: 'right' | 'left';
  @Input() showDownloadMenu = false;
  @Input() disabled = false;
  @Input() emitDownloadDocument = false;
  @Input() emitSendDocument = false;
  @Input() itemsDropDownload: MenuItem[] = [];
  @Input() isDownloadDocumentButtonDisabled = false;
  @Input() isSendDocumentButtonDisabled = false;
  @Input() buttonOptions: SeButton = {
    icon: 'matAddCircleOutlineOutline',
    iconSize: '1.25rem',
  };
  @Input() set customFileName(customName: string) {
    if (customName) {
      this.items[0].visible = true;
      this.fileName = customName;
    }
  }
  @Output() downloadDocumentEvent: EventEmitter<boolean> = new EventEmitter();
  @Output() sendDocumentEvent: EventEmitter<string> = new EventEmitter();

  items: MenuItem[] = [
    {
      id: 'download-documents',
      label: this.translateService.instant(
        'SE_DOCUMENTS_MF.DOWNLOAD_BUTTON_LABEL',
      ),
      visible: false,
      command: () => {
        if (this.emitDownloadDocument) {
          this.downloadDocumentEvent.emit();
        } else {
          this.docsService.downloadDocument(
            this.documentsIds,
            this.customFileName,
          );
        }
      },
    },
    {
      id: 'send-email',
      label: this.translateService.instant(
        'SE_DOCUMENTS_MF.SEND_DOCUMENTS_BUTTON.SEND_DOCUMENTS',
      ),
      command: () => {
        this.sendDocumentation();
      },
    },
  ];

  fileName!: string;
  private readonly _unsubscribe: Subject<void> = new Subject<void>();

  constructor(
    private readonly translateService: TranslateService,
    private readonly docsService: DocsService,
    private readonly modalService: SeModalService,
    private readonly endPointService: EndPointService,
    private readonly mfActionService: MfActionCourseService,
  ) {
    console.log(
      'Webcomponent: Documents > DocumentActionsComponent > constructor',
    );
  }

  ngOnInit(): void {
    this.documentsIds =
      this.documentsIds ||
      (this.mfActionService.getData()?.data?.idDocument ?? []);
  }

  private sendDocumentation(): void {
    const modalData: SeModal = {
      component: ModalSendDocumentationComponent,
    };
    this.modalService
      .openModal(modalData)
      .result.then((result: SendDocumentForm | null) => {
        if (result) {
          if (this.emitSendDocument) {
            this.sendDocumentEvent.emit(result.email);
          } else {
            this.endPointService
              .sendJustificant(result.email, this.documentsIds)
              .pipe(takeUntil(this._unsubscribe))
              .subscribe();
          }
        }
      });
  }

  downloadDocument(): void {
    this.downloadDocumentEvent.emit();
  }

  sendDocument(event: Event): void {
    const email = (event as CustomEvent<string>).detail;
    this.sendDocumentEvent.emit(email);
  }

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }
}
