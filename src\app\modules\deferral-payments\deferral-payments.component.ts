import { Component, OnInit } from '@angular/core';
import { AppRoutes, FunctionalModuleEnum } from '@app/core/models';
import { CustomRouterService, StorageService } from '@app/core/services';
import {
  DeferralPaymentsService,
  ProceduresHeaderService,
} from '@app/shared/services';
import { Column, Row } from 'se-ui-components-mf-lib';
import { BASE_TRANSLATE } from '../payments-process/payments-process.component';
import { TranslateService } from '@ngx-translate/core';
import { VehiclePayment } from '@app/shared/models';

@Component({
  selector: 'app-deferral-payments',
  templateUrl: './deferral-payments.component.html',
  styleUrls: ['./deferral-payments.component.scss'],
})
export class DeferralPaymentsComponent implements OnInit {
  vehiclesColumns: Column[] = [];
  vehiclesRows: Row[] = [];

  totalAmount: number = 0;

  protected idEntity: string = '';
  protected plates: string[] = [];
  protected idFunctionalModule: FunctionalModuleEnum =
    FunctionalModuleEnum.DEFERRAL_PAYMENTS;

  protected disableContinueButton: boolean = false;

  constructor(
    private readonly storage: StorageService,
    private readonly procedureHeaderService: ProceduresHeaderService,
    private readonly customRouter: CustomRouterService,
    private readonly translateService: TranslateService,
    private readonly deferralPaymentsService: DeferralPaymentsService,
  ) {}

  ngOnInit(): void {
    const { total, vehicles, idGestio } =
      this.storage.getDeferralPaymentVehicles();

    if (!vehicles) {
      this.goBack();
      return;
    }

    this.procedureHeaderService.setupDeferralPaymentsHeader(vehicles);

    this.vehiclesColumns = this.getVehiclesColumns();
    this.vehiclesRows = this.getVehiclesRows(vehicles ?? []);

    this.totalAmount = total ?? 0;

    this.plates = vehicles?.map((vehicle) => vehicle.matricula) ?? [];
    this.idEntity = idGestio ?? '';
  }

  goBack(): void {
    this.storage.clearVehiclesToBeRegistered();
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  async onContinue(): Promise<void> {
    const url = await this.deferralPaymentsService.getURLFormSEU(
      this.storage.getDeferralPaymentVehicles(),
    );
    window.location.href = url;
  }

  private getVehiclesRows(vehicles: VehiclePayment[]): Row[] {
    return (vehicles ?? []).map((vehicle) => {
      return {
        data: {
          vehicle: {
            value: `${vehicle.marca} - ${vehicle.matricula}`,
          },
          exercici: {
            value: vehicle.exercici,
          },
          quota: {
            value: vehicle.quota,
          },
          recarrec: {
            value: vehicle.recarrega,
          },
          total: {
            value: vehicle.total,
          },
        },
      };
    }) as Row[];
  }

  private getVehiclesColumns(): Column[] {
    return [
      {
        key: 'vehicle',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.VEHICLE`,
        ),
        size: 10,
      },
      {
        key: 'exercici',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.EXERCICI`,
        ),
        cellConfig: {
          align: 'right',
        },
        size: 8,
      },
      {
        key: 'quota',
        header: this.translateService.instant(`${BASE_TRANSLATE}.TABLE.QUOTA`),
        size: 8,
        cellConfig: {
          align: 'right',
        },
        cellComponentName: 'currencyCellComponent',
      },
      {
        key: 'recarrec',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.RECARREC`,
        ),
        size: 8,
        cellConfig: {
          align: 'right',
        },
        cellComponentName: 'currencyCellComponent',
        tooltip: true,
        tooltipText: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.RECARREC_TOOLTIP`,
        ),
      },
      {
        key: 'total',
        header: this.translateService.instant(`${BASE_TRANSLATE}.TABLE.TOTAL`),
        size: 10,
        cellConfig: {
          align: 'right',
        },
        cellComponentName: 'currencyCellComponent',
      },
    ];
  }

  protected onDisableContinueButton($event: boolean): void {
    this.disableContinueButton = $event;
  }
}
