import { iDocumentPadoct, SeHttpResponse } from 'se-ui-components-mf-lib';
import { ContestableActDocument } from '../../allegations-options';
export const CIVIL_SERVANT_DOC_SIZE_LIMIT = 10240; // 10 MB

export const SIGEDA_DESCRIPTION: ContestableActDocument[] = [
  {
    type: 'TD20-025',
    subtype: '0',
    description: '',
    allowedFiles: ['pdf'],
    allowedSize: CIVIL_SERVANT_DOC_SIZE_LIMIT,
  },
];

export interface SignDocumentRequest {
  idTramit: string;
  sendToSign: boolean;
  idFuncionalModule: string;
  nif: string;
  repNif?: string;
  matricules: string[];
  authAlta: boolean;
  authPagament: boolean;
  authSuport: boolean;
  authDomiciliacio: boolean;
  authAjornament: boolean;
  authNotificacio: boolean;
  authAlegacio: boolean;
  declaracioAjornament: boolean;
  numCompte: string;
}

export interface SignDocumentRequestResponse extends SeHttpResponse {
  content: SignetDocumentResponseContent;
}

export interface SignetDocumentResponseContent extends iDocumentPadoct {
  codeDocumentType: string;
  createdOn: string;
  creatorName: string;
  creatorNif: string;
  descriptionComplementary: string;
  error: boolean;
  errorList: SignError[];
  evidenceType: string;
  language: string;
  mongoParentId: string;
  nomFormat: string;
  origenCiutada: string;
  passiveSubject: string;
  sarcatDate: string;
  sarcatNumber: string;
  updatedOn: string;
  version: string;
}

export enum SignDocumentStatus {
  SIGN_ERROR = 'SIGN_ERROR',
  SIGNAT = 'SIGNAT',
  PENDING_SIGN = 'PENDING_SIGN',
  STAGED = 'STAGED',
}
export type SignDocumentStatusType = keyof typeof SignDocumentStatus;

export const SIGN_DOCUMENT_CALL_INTERVAL = 10; // 10 seconds

export interface SignError {
  code: SignErrorCode | string;
  description: string;
}

export enum SignErrorCode {
  REBUTJAT = 'REBUTJAT',
  VIDSIGN_SEND_FILE_ERROR = 'DOCUMENTS.VIDSIGN_SEND_FILE_ERROR',
  GENERATE_DOCUMENT_ERROR = 'COMMONS.PADOCT_RECEIPT_GENERATION_ERROR ',
}

export enum PaymentType {
  BIZUM = 'B',
  CUENTA = 'C',
  TARJETA = 'T',
}
