import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import {
  Nullable,
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import {
  ReasResumResponse,
  RequestUpdateResourcesTramit,
} from './reas-request.model';

@Injectable({
  providedIn: 'root',
})
export class ReasRequestEndpointService {
  constructor(private httpService: SeHttpService) {}

  updateResourcesTramit(
    idTramit: string,
    request: RequestUpdateResourcesTramit,
  ): Observable<SeHttpResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `reas/${idTramit}/motius`,
      method: 'put',
      body: request,
    };

    return this.httpService.put<SeHttpResponse>(httpRequest);
  }

  getReasResum(idTramit: string): Observable<Nullable<ReasResumResponse>> {
    return this.httpService
      .get<SeHttpResponse<ReasResumResponse>>({
        baseUrl: environment.baseUrlCo2,
        url: `reas/${idTramit}/resum`,
        method: 'get',
      })
      .pipe(
        map((response: SeHttpResponse<ReasResumResponse>) => response.content),
      );
  }
}
