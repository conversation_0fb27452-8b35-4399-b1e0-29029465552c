import { Injectable } from '@angular/core';
import { GetNotificationDataResponse } from '@app/core/models';
import {
  AddressTramitRequest,
  AddressTramitResponse,
  OrigenEnum,
} from '@app/shared/models';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root',
})
export class NotificationsEndpointsService {
  constructor(private httpService: SeHttpService) {}

  getNotificationData = (): Observable<GetNotificationDataResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlContribuent,
      url: `/te-notificacio-electronica`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  };

  processAddressTramit = (
    idTramit: string,
    request: AddressTramitRequest,
  ): Observable<AddressTramitResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `reas/${idTramit}/adreca`,
      method: 'put',
      body: request,
      clearExceptions: true,
    };

    return this.httpService.put(httpRequest);
  };

  processAddressTramitMiro(
    request: AddressTramitRequest,
  ): Observable<AddressTramitResponse> {
    const httpRequest: SeHttpRequest = {
      headers: { origen: OrigenEnum.REA },
      baseUrl: environment.baseUrlContribuent,
      url: `/adreca/tramitacio`,
      method: 'post',
      body: request,
      clearExceptions: true,
    };

    return this.httpService.post(httpRequest);
  }
}
