import { ComponentFixture, TestBed } from '@angular/core/testing';

import { GestionsComponent } from './gestions.component';
import { TranslateModule } from '@ngx-translate/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';

describe('GestionsComponent', () => {
  let component: GestionsComponent;
  let fixture: ComponentFixture<GestionsComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [GestionsComponent],
      imports: [TranslateModule.forRoot(), HttpClientTestingModule],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    });
    fixture = TestBed.createComponent(GestionsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
