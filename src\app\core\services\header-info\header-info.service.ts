import { Injectable, OnD<PERSON>roy } from '@angular/core';
import { Router } from '@angular/router';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { BehaviorSubject, Subject, takeUntil } from 'rxjs';

import { AppRoutes, IdentificationType, LoginRequest } from '@app/core/models';
import {
  HelpModalComponent,
  ModalVehiclesComponent,
  type HelpModalOption,
} from '@app/shared/components';
import { VehiclesSelectedInfo } from '@app/shared/models';
import {
  SeButton,
  SeButtonDropdown,
  SeDataStorageService,
  SeModalService,
  SePageLayoutService,
  SeUserService,
  TagProps,
  type Nullable,
  type SeHeaderInfoItem,
} from 'se-ui-components-mf-lib';
import { SeHeaderInfoCustomButton } from 'se-ui-components-mf-lib/lib/components/header-info/models/custom-button-props.model';
import { IdentificationsService } from '../identifications';
import { LoginResponseService } from '../login-response';
import { SpecificConfigurationService } from '../specific-configuration';
import { StorageService } from '../storage';
import { TitularDataModalComponent } from '@app/shared/components/titular-data-modal';
import {
  GetUserDataByIdPers,
  UserInfo,
} from '@app/shared/components/titular-data-modal/models/titular-data-modal.model';
import { TitularDataModalEndpointsService } from '@app/shared/components/titular-data-modal/services/titular-data-modal-endpoints.service';

@Injectable({
  providedIn: 'root',
})
export class HeaderInfoService implements OnDestroy {
  private infoItemsSubject = new BehaviorSubject<Nullable<SeHeaderInfoItem>[]>([
    null,
    null,
    null,
    this.dataStorageService.getItem('HEADER_VEHICLE'),
    this.dataStorageService.getItem('HEADER_NIF_TITULAR'),
    this.dataStorageService.getItem('HEADER_NIF_REPRESENTANT'),
  ]);

  private titleHeaderSubject = new BehaviorSubject<string>(
    this.dataStorageService.getItem('HEADER_TITLE'),
  );

  private titleHeaderMobileSubject = new BehaviorSubject<string>(
    this.dataStorageService.getItem('HEADER_TITLE_MOBILE'),
  );

  private tagsSubject = new BehaviorSubject<TagProps[]>([]);
  private customButtonSubject = new BehaviorSubject<SeButton | undefined>(
    undefined,
  );
  private dropdownButtonSubject = new BehaviorSubject<
    SeButtonDropdown | undefined
  >(undefined);
  private fullscreenMode = new BehaviorSubject<boolean>(false);

  public infoItems$ = this.infoItemsSubject.asObservable();
  public titleHeader$ = this.titleHeaderSubject.asObservable();
  public titleHeaderMobile$ = this.titleHeaderMobileSubject.asObservable();
  public tags$ = this.tagsSubject.asObservable();
  public customButton$ = this.customButtonSubject.asObservable();
  public dropdownButton$ = this.dropdownButtonSubject.asObservable();
  public fullscreenMode$ = this.fullscreenMode.asObservable();

  get isVisible(): boolean {
    return this.pageLayoutService.isHeaderVisible;
  }

  get styleClass(): string {
    return this.class ?? '';
  }

  private class: Nullable<string>;

  private _unsubscribe: Subject<void> = new Subject();

  constructor(
    private translate: TranslateService,
    private store: StorageService,
    private dataStorageService: SeDataStorageService,
    private seModalService: SeModalService,
    private userService: SeUserService,
    private pageLayoutService: SePageLayoutService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private loginSrv: LoginResponseService,
    private router: Router,
    private identificationService: IdentificationsService,
    private titularDataEndpointsService: TitularDataModalEndpointsService,
  ) {}

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }

  show(): void {
    this.pageLayoutService.setHeaderVisibility({});
  }

  hide(): void {
    this.pageLayoutService.setHeaderVisibility({ isHeaderVisible: false });
  }

  setFullscreenMode(fullscreenMode: boolean): void {
    this.fullscreenMode.next(fullscreenMode);
  }

  reset(): void {
    this.setFullscreenMode(false);
    this.class = null;
    this.titleHeaderSubject.next('SE_PADRO_CO2.APP_TITLE');
    this.titleHeaderMobileSubject.next('SE_PADRO_CO2.APP_TITLE_MOBILE');
    this.infoItemsSubject.next([null, null, null, null, null, null]);
    this.customButtonSubject.next(undefined);
    this.dropdownButtonSubject.next(undefined);
  }

  setTitleHeader(
    title: Nullable<string>,
    titleMobile?: Nullable<string>,
  ): void {
    if (title) {
      this.titleHeaderSubject.next(title);
      this.dataStorageService.setItem('HEADER_TITLE', title);
    }
    if (titleMobile || title) {
      this.titleHeaderMobileSubject.next((titleMobile || title) as string);
      this.dataStorageService.setItem(
        'HEADER_TITLE_MOBILE',
        titleMobile || title,
      );
    }
  }

  getBodyToRequestVehiclesDetail(
    matriculas: string[],
    exercici?: string,
  ): VehiclesSelectedInfo {
    return {
      provisional: this.specificConfigurationSrv.isProvisional,
      matriculas,
      exercici: exercici ?? this.specificConfigurationSrv.currentExercise,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
    };
  }

  setMatriculaOrVehicles(
    matriculas: string[],
    exercici?: string,
    hideExerciceColumn?: boolean,
    modalTitle?: string,
  ): void {
    let matricula: Nullable<string> = null;
    let vehiclesDetail: Nullable<VehiclesSelectedInfo> = null;

    if (matriculas.length > 1) {
      matricula = this.translate.instant(
        'SE_PADRO_CO2.SHARED_MODULE.REQUEST_SUMMARY.NUMBER_VEHICLES',
        {
          vehicleNumber: matriculas.length,
        },
      );
      vehiclesDetail = this.getBodyToRequestVehiclesDetail(
        matriculas,
        exercici,
      );
    } else {
      matricula = matriculas[0];
    }

    const vehicleItem: Nullable<SeHeaderInfoItem> = matricula
      ? {
          term: this.translate.instant('SE_PADRO_CO2.HEADER_INFO.VEHICLE'),
          details: matricula,
          ...(vehiclesDetail
            ? {
                link: {
                  label: this.translate.instant(
                    'SE_PADRO_CO2.BUTTONS.SEE_DETAIL',
                  ),
                  onLinkClick: this.onOpenVehiclesModal.bind(
                    this,
                    vehiclesDetail,
                    hideExerciceColumn,
                    modalTitle,
                  ),
                },
              }
            : []),
        }
      : null;

    this.spliceInfoItems(0, 0, vehicleItem);
    this.dataStorageService.setItem('HEADER_VEHICLE', vehicleItem);
  }
  private onOpenVehiclesModal(
    vehiclesDetail?: VehiclesSelectedInfo,
    hideExerciceColumn?: boolean,
    modalTitle?: string,
  ): void {
    if (vehiclesDetail?.matriculas && vehiclesDetail?.matriculas?.length) {
      const component: ModalVehiclesComponent = this.seModalService.openModal({
        title: modalTitle || 'SE_PADRO_CO2.MODAL.VEHICLES.TITLE_DOMICILIATION',
        severity: 'info',
        closable: true,
        closableLabel: 'SE_PADRO_CO2.BUTTONS.OK',
        hideIcon: true,
        size: 'xl',
        secondaryButton: false,
        component: ModalVehiclesComponent,
      }).componentInstance;

      component.vehiclesPlates = vehiclesDetail;
      component.hideExerciceColumn = hideExerciceColumn;
    }
  }

  setNifTitular(nifTitular?: Nullable<string>): void {
    const nifTitularItem: Nullable<SeHeaderInfoItem> =
      nifTitular !== null
        ? {
            term: this.translate.instant(
              'SE_PADRO_CO2.HEADER_INFO.NIF_TITULAR',
            ),
            details: nifTitular ?? this.userService.getNIF(),
          }
        : null;

    this.spliceInfoItems(0, 1, nifTitularItem);
    this.dataStorageService.setItem('HEADER_NIF_TITULAR', nifTitularItem);
  }

  setCoordinatorHeader(
    nifContribuent?: Nullable<string>,
    includeContribuentButton: boolean = false,
  ): void {
    const nifContribuentItem: Nullable<SeHeaderInfoItem> =
      nifContribuent !== null
        ? {
            term: this.translate.instant(
              'SE_PADRO_CO2.HEADER_INFO.NIF_CONTRIBUENT',
            ),
            details: nifContribuent ?? this.userService.getNIF(),
          }
        : null;

    this.spliceInfoItems(0, 1, nifContribuentItem);
    this.dataStorageService.setItem(
      'HEADER_NIF_CONTRIBUENT',
      nifContribuentItem,
    );

    this.setContribuentButton(includeContribuentButton);
  }

  setCivilServantHeader(includeContribuentButton: boolean = false): void {
    const nifTitular = this.store.civilServantVehicleNifTitular;
    const nameTitular = this.store.civilServantVehicleNameTitular;
    const nifRepresentant = this.store.civilServantVehicleNifRepresentant;
    const phone = this.store.civilServantVehiclePhone;
    const serviceType = this.store.isInPersonCivilServant
      ? 'IN_PERSON'
      : 'PHONE';
    const items: SeHeaderInfoItem[] = [
      {
        term: this.translate.instant(
          'SE_PADRO_CO2.HEADER_INFO.VEHICLE_TITULAR',
        ),
        details: `${nameTitular} (${nifTitular})`,
        link: {
          label: this.translate.instant(
            'SE_PADRO_CO2.BUTTONS.SEE_TITULAR_DETAIL',
          ),
          onLinkClick: this.getUserInfo.bind(
            this,
            'SE_PADRO_CO2.MODAL.TITULAR_DATA.TITLE',
          ),
        },
      },
      ...(nifRepresentant
        ? [
            {
              term: this.translate.instant(
                'SE_PADRO_CO2.HEADER_INFO.NIF_REPRESENT',
              ),
              details: nifRepresentant,
            },
            {},
          ]
        : []),
      ...(phone
        ? [
            {},
            {},
            {
              term: this.translate.instant(
                'SE_PADRO_CO2.HEADER_INFO.TELEPHONE',
              ),
              details: phone,
            },
          ]
        : []),
      {
        term: this.translate.instant(
          'SE_PADRO_CO2.HEADER_INFO.SERVICE_TYPE.TITLE',
        ),
        details: this.translate.instant(
          `SE_PADRO_CO2.HEADER_INFO.SERVICE_TYPE.${serviceType}`,
        ),
      },
    ];

    this.spliceInfoItems(0, 6, ...items);
    this.dataStorageService.setItem('HEADER_CIVIL_SERVANT', items);

    this.setContribuentButton(includeContribuentButton);
  }

  private getUserInfo(modalTitle: Nullable<string>): void {
    const idPersTitular = this.loginSrv.user.idPersTitular;

    if (!idPersTitular) return;

    const request: GetUserDataByIdPers = {
      idPersCens: idPersTitular || '',
    };

    this.titularDataEndpointsService
      .getUserInfo(request)
      .pipe(takeUntil(this._unsubscribe))
      .subscribe((data) => {
        if (data?.content) {
          this.onOpenTitularDataModal(modalTitle, data.content);
        }
      });
  }

  private onOpenTitularDataModal(
    modalTitle: Nullable<string>,
    userInfo: UserInfo,
  ): void {
    const component: TitularDataModalComponent = this.seModalService.openModal({
      title: modalTitle || 'SE_PADRO_CO2.MODAL.TITULAR_DATA.TITLE',
      severity: 'info',
      closable: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.CLOSE',
      size: 'xl',
      secondaryButton: false,
      component: TitularDataModalComponent,
    }).componentInstance;

    component.userInfo = userInfo;
  }

  private setContribuentButton(includeButton: boolean = false): void {
    const button: SeHeaderInfoCustomButton = {
      label: this.translate.instant(
        'SE_PADRO_CO2.HEADER_INFO.CONTRIBUENT_BUTTON',
      ),
      icon: 'matPermIdentityOutline',
      iconPosition: 'right',
      btnTheme: 'secondary',
      onButtonClick: () => {
        this.router.navigate([AppRoutes.IDENTIFICATION], {
          queryParams: { canviarContribuent: true },
        });
      },
    };

    this.customButtonSubject.next(includeButton ? button : undefined);
  }

  setConveniantHeader(includeContribuentButton: boolean = false): void {
    const nifRepresentative = this.store.taxpayerNIF;
    const nameRepresentative = this.store.taxpayerName;
    const items = [
      {
        term: this.translate.instant(
          'SE_PADRO_CO2.HEADER_INFO.NOM_REPRESENTANT',
        ),
        details: nameRepresentative,
      },
      {
        term: this.translate.instant(
          'SE_PADRO_CO2.HEADER_INFO.NIF_REPRESENTANT',
        ),
        details: nifRepresentative,
      },
    ];

    this.spliceInfoItems(0, 2, ...items);
    this.dataStorageService.setItem('HEADER_AGREED', items);

    this.setContribuentButton(includeContribuentButton);
  }

  setRepresentantHeader(includeRepresentativeButton: boolean = false): void {
    const nifRepresentative = this.store.taxpayerNIF;
    const nameRepresentative = this.store.taxpayerName;
    const items = [
      {
        term: this.translate.instant(
          'SE_PADRO_CO2.HEADER_INFO.NOM_REPRESENTANT',
        ),
        details: nameRepresentative,
      },
      {
        term: this.translate.instant(
          'SE_PADRO_CO2.HEADER_INFO.NIF_REPRESENTANT',
        ),
        details: nifRepresentative,
      },
    ];

    this.spliceInfoItems(0, 2, ...items);
    this.dataStorageService.setItem('HEADER_REPRESENTANT', items);

    this.setRepresentativeButton(includeRepresentativeButton);
  }

  private setRepresentativeButton(includeButton: boolean = false): void {
    const listRepresentatives = this.store.listRepresentatives;
    const button: SeButtonDropdown = {
      button: {
        label: this.translate.instant(
          'SE_PADRO_CO2.HEADER_INFO.REPRESENTATIVE_BUTTON',
        ),
        icon: 'matPermIdentityOutline',
        iconPosition: 'right',
      },
      items: listRepresentatives
        ?.filter(
          (representative) =>
            representative.documentDesti !== this.store.taxpayerNIF,
        )
        .map((representative) => {
          return {
            label: `${representative.nomDesti} (${representative.documentDesti})`,
            command: (): void => {
              this.loginWithNewRepresentative(
                representative.nomDesti,
                representative.documentDesti,
                IdentificationType.REPRESENTATIVE,
              );
            },
          };
        }),
    };

    this.dropdownButtonSubject.next(includeButton ? button : undefined);
  }

  private loginWithNewRepresentative(
    nameRepresentative: string,
    nifRepresentative: string,
    profileUser: IdentificationType,
  ): void {
    const body: LoginRequest = {
      nom: nameRepresentative,
      nifTitular: nifRepresentative,
    };
    this.identificationService
      .loginCo2(body, profileUser, AppRoutes.RECEIPTS, true)
      .pipe(takeUntil(this._unsubscribe))
      .subscribe((response) => {
        if (response?.content) {
          this.store.taxpayerNIF = nifRepresentative;
          this.store.taxpayerName = nameRepresentative;
        }
      });
  }

  private spliceInfoItems(
    start = 0,
    deleteCount = 0,
    ...items: Nullable<SeHeaderInfoItem>[]
  ): Nullable<SeHeaderInfoItem>[] {
    const infoItems = this.infoItemsSubject.getValue();
    const deletedItems = infoItems.splice(start, deleteCount, ...items);
    this.infoItemsSubject.next([...infoItems]);
    return deletedItems;
  }

  showHelpModal(selectedDropdownOption?: HelpModalOption): NgbModalRef {
    const modalInstance = this.seModalService.openSidebarModal(
      {
        severity: 'info',
        title: this.translate.instant('UI_COMPONENTS.BUTTONS.HELP'),
        keyboard: true,
        centered: true,
        titleTextWeight: 'semi-bold',
        windowClass: 'bg-transparent',
        backdrop: true,
        hideIcon: true,
        component: HelpModalComponent,
      },
      'right',
    );

    if (selectedDropdownOption) {
      modalInstance.componentInstance.selectedDropdownOption =
        selectedDropdownOption;
    }

    return modalInstance;
  }

  setStyleClass(styleClass: Nullable<string>): void {
    this.class = styleClass;
  }
}
