import { Injectable } from '@angular/core';
import { StatusCodes } from '@app/core/models';
import { TranslateService } from '@ngx-translate/core';
import { SeTagTheme } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class TagColorService {
  constructor(private translateService: TranslateService) {}

  getStatusCodeColor(statusCode: string): SeTagTheme {
    switch (statusCode) {
      case StatusCodes.Paid_telematically:
      case StatusCodes.Paid:
        return 'success';
      case StatusCodes.Pending:
        return 'warning';
      case StatusCodes.Canceled:
      case StatusCodes.Executive:
        return 'danger';
      default:
        return 'info';
    }
  }

  getPlateTagColor(newPlate?: boolean, modifiedPlate?: boolean): SeTagTheme {
    if (newPlate) return 'gray';
    if (modifiedPlate) return 'gray';
    return 'info';
  }

  getPlateTagValue(newPlate?: boolean, modifiedPlate?: boolean): string {
    if (newPlate)
      return this.translateService.instant('SE_PADRO_CO2.LABELS.NEW');
    if (modifiedPlate)
      return this.translateService.instant('SE_PADRO_CO2.LABELS.MODIFIED');
    return '';
  }
}
