<se-panel [title]="title">
  <div class="row flex-row" *ngIf="data">
    <!-- vehiculos - boton que abre modal con detalle de vehiculos -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing"
      *ngIf="
        data.vehicles?.matriculas?.length &&
        (data.vehicles?.matriculas)!.length > 1
      "
    >
      <span class="col-3 fw-bold">{{
        'SE_PADRO_CO2.LABELS.VEHICLES' | translate
      }}</span>
      <div class="col-md-9 col-12">
        <span>{{
          'SE_PADRO_CO2.SHARED_MODULE.REQUEST_SUMMARY.NUMBER_VEHICLES'
            | translate: { vehicleNumber: data.vehicles!.matriculas!.length }
        }}</span>
        <se-link
          class="ms-2"
          (onClick)="openVehiclesDetail()"
          [linkTheme]="'secondary'"
          >{{ 'SE_PADRO_CO2.BUTTONS.SEE_DETAIL' | translate }}</se-link
        >
      </div>
    </div>

    <!-- detalle del vehiculo cuando solo hay 1 -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
      *ngIf="data.vehicles?.matriculas?.length === 1 && data.singleVehicleText"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        'SE_PADRO_CO2.LABELS.VEHICLE' | translate
      }}</span>
      <span class="col-md-9 col-12">{{ data.singleVehicleText }}</span>
    </div>

    <!-- nombre del titular -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
      *ngIf="data.titular"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        'SE_PADRO_CO2.LABELS.HOLDER' | translate
      }}</span>
      <span class="col-md-9 col-12">{{ data.titular }}</span>
    </div>

    <!-- numero de cuenta bancaria -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
      *ngIf="data.iban"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        data.ibanLabel || 'SE_PADRO_CO2.LABELS.BANK_ACCOUNT' | translate
      }}</span>
      <span class="col-md-9 col-12">{{ data.iban! | iban }}</span>
    </div>

    <!-- fecha del proximo recibo -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
      *ngIf="data.dataProxRebut"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        'SE_PADRO_CO2.LABELS.NEXT_RECEPIPT_DATE'
          | translate: { exercice: data.exercici }
      }}</span>
      <span class="col-md-9 col-12">{{ data.dataProxRebut }}</span>
    </div>

    <!-- categoria -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
      *ngIf="data.categoria && data.vehicles?.matriculas?.length === 1"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        'SE_PADRO_CO2.LABELS.CATEGORY' | translate
      }}</span>
      <span class="col-md-9 col-12">{{ data.categoria }}</span>
    </div>

    <!-- emisiones de Co2 -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
      *ngIf="data.co2 && data.vehicles?.matriculas?.length === 1"
    >
      <span
        class="col-md-3 col-12 fw-bold"
        [innerHTML]="'SE_PADRO_CO2.LABELS.EMISSIONS' | translate"
      ></span>
      <span class="col-md-9 col-12">{{ data.co2 + ' g/Km' }}</span>
    </div>

    <!-- exercici -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
      *ngIf="data.exercici"
    >
      <span
        class="col-md-3 col-12 fw-bold"
        [innerHTML]="'SE_PADRO_CO2.LABELS.EXERCISE' | translate"
      ></span>
      <span class="col-md-9 col-12">{{ data.exercici }}</span>
    </div>

    <!-- cuota total -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
      *ngIf="data.quota !== undefined && data.quota !== null"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        'SE_PADRO_CO2.LABELS.TOTAL_FEE' | translate
      }}</span>
      <span class="col-md-9 col-12">{{ data.quota | currency }}</span>
    </div>

    <!-- tabla de motivo de alegaciones -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
      *ngIf="reasonRows.length"
    >
      <span class="col-md-3 col-12 fw-bold">{{ reasonLabel | translate }}</span>
      <se-table
        [styleClass]="'border-table'"
        [resizable]="false"
        [columns]="reasonColumns"
        [data]="reasonRows"
      ></se-table>
    </div>

    <div
      *ngIf="data.descripcion"
      [ngClass]="{ 'mt-4': reasonRows.length, 'mt-2': !reasonRows.length }"
      class="col-12 d-flex flex-md-row flex-column content-spacing"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        descriptionLabel | translate
      }}</span>
      <span
        class="col-md-9 col-12 overflow-ellipsis"
        [innerHTML]="data.descripcion"
      ></span>
    </div>

    <div class="col-12 col-md-12 col-lg-12 mb-2 mt-4" *ngIf="data.checkbox">
      <form class="mt-2 d-flex flex-column" [formGroup]="summaryForm">
        <se-checkbox
          class="col-12"
          formControlName="allegationsDr"
          [label]="data.checkbox"
          [id]="'checkbox-summary'"
          [tooltip]="false"
          [disabled]="true"
        ></se-checkbox>
      </form>
    </div>

    <!-- tabla de motivo de alegaciones -->
    <div
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-4"
      *ngIf="documentRows.length"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        documentLabel | translate
      }}</span>
      <se-table
        [styleClass]="'border-table'"
        [resizable]="false"
        [columns]="documentColumns"
        [data]="documentRows"
      ></se-table>
    </div>

    <!-- alerta - solo en alegaciones -->
    <div
      class="col-12 col-md-12 col-lg-12 mb-2 mt-4"
      *ngIf="reasonRows.length && isAllegationsTramit"
    >
      <se-alert
        [type]="'info'"
        [title]="'SE_PADRO_CO2.ALLEGATIONS.ALERT' | translate"
        [closeButton]="false"
      ></se-alert>
    </div>

    <!-- mensaje de pdf-info -->
    <div
      class="col-12 col-md-12 col-lg-12 mb-1"
      [ngClass]="{ 'mt-4': !reasonRows.length || !isAllegationsTramit }"
      *ngIf="showPDFInfo && isAllegationsTramit && footerMessage"
    >
      <p [innerHTML]="footerMessage | translate"></p>
    </div>

    <p
      *ngIf="data.showGdprMsg"
      [ngClass]="{ 'mt-4': !reasonRows.length || !isAllegationsTramit }"
      [innerHTML]="'SE_PADRO_CO2.PRESENTATION_RECEIPT.GDPR' | translate"
    ></p>
  </div>

  <ng-content></ng-content>
</se-panel>
