import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  AttachFile,
  Column,
  FileFormatsSeparation,
  FlattenedRow,
  SeAlertMessage,
  SeDropdownOption,
  SeModal,
  SeModalOutputEvents,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-upload-modal',
  templateUrl: './upload-modal.component.html',
  styleUrls: [],
})
export class UploadModalComponent implements OnInit {
  @Input({ required: true }) data!: SeModal;

  @Input({ required: true }) tableColumns!: Column[];

  @Input({ required: true }) set modalTableColumns(value: Column[]) {
    if (value && value.length) {
      this._modalTableColumns = value;
      this.optionsDropdown = this.getDocTypesList(this._modalTableColumns);
      this.filterModalTableColumnsByDocType();
    }
  }

  private _modalTableColumns!: Column[];

  get modalTableColumns(): Column[] {
    return this._modalTableColumns;
  }

  @Input() hasActions = true;

  @Input() hasModal: boolean = true;

  @Input() info: string = '';

  @Input() title!: string;

  @Input() subtitleText: string | undefined;

  @Input() accept: string[] = ['*'];

  @Input() docType!: string;

  @Input() multiple: boolean = false;

  @Input() required: boolean = true;

  @Input() disabled: boolean = false;

  @Input() infoModalAlert: SeAlertMessage | undefined;

  @Input() fileFormatSeparation: FileFormatsSeparation =
    FileFormatsSeparation.SLASH;

  /**
   * Size limit per file using Kbs
   * @default 500
   */
  @Input() sizeLimitPerFile: number = 5000;

  /**
   * Size limit for the sum of all the files using Kbs
   * @default 250000
   */

  @Input() groupSizeLimit: number = 25000;

  @Input() maxFiles: number = 25;

  @Input() dropAreaTitlePreLinkText: string | undefined;
  @Input() dropAreaTitleLinkText: string | undefined;
  @Input() dropAreaTitlePostLinkText: string | undefined;

  protected uploadedFiles: AttachFile[] = [];
  private optionsDropdown: SeDropdownOption[] = [];

  get isButtonDisabled(): boolean {
    let result: boolean = true;
    const fieldsRequired: string[] = this.modalTableColumns
      .filter((column: Column) => {
        return (
          (column.cellComponentName === 'dropdownCellComponent' ||
            column.cellComponentName === 'inputCellComponent') &&
          column.cellConfig?.['required']
        );
      })
      .map((column: Column) => column.key);

    result = this.uploadedFiles.some((file) => {
      return fieldsRequired.some(
        (value) => file[value] === undefined || file[value] === '',
      );
    });

    return result;
  }

  @Output() closeModalOutput = new EventEmitter<{
    files: AttachFile[] | string;
  }>();

  constructor(
    private activeModal: NgbActiveModal,
    private translate: TranslateService,
  ) {
    // Vacio
  }

  ngOnInit(): void {
    this.fillModalData();
  }

  private fillModalData(): void {
    this.data = {
      ...this.data,
      closable: true,
      closableLabel: this.translate.instant(
        'SE_DOCUMENTS_MF.TABLE_UPLOAD_FILES.MODAL.BUTTON',
      ),
      secondaryButton: true,
      secondaryButtonLabel: this.translate.instant(
        'UI_COMPONENTS.BUTTONS.CANCEL',
      ),
      title: 'SE_DOCUMENTS_MF.TABLE_UPLOAD_FILES.MODAL.TITLE',
      severity: 'info',
      hideIcon: true,
      closableDisabled: true,
    };
  }

  closeModal(event: string | AttachFile[]): void {
    this.closeModalOutput.emit({ files: event });
    this.activeModal.close();
  }

  protected handleFileLoaded(files: AttachFile[]): void {
    if (files) {
      this.uploadedFiles = [...this.uploadedFiles, ...files];
      this.filterModalTableColumnsByDocType();
      this.checkClosableDisabled();
    }
  }

  protected onEditFile(file: AttachFile): void {
    if (file) {
      const index = this.uploadedFiles.findIndex(
        (document) => document.id === file.id,
      );
      this.uploadedFiles[index] = file;
      this.filterModalTableColumnsByDocType();
      this.checkClosableDisabled();
    }
  }

  protected onDeleteFile(idDocument: string): void {
    if (idDocument) {
      this.uploadedFiles = this.uploadedFiles.filter(
        (document) => document.id !== idDocument,
      );
      this.filterModalTableColumnsByDocType();
      this.checkClosableDisabled();
    }
  }

  protected onSubmit(event: string | null = null): void {
    if (SeModalOutputEvents.MAIN_ACTION === event && this.uploadedFiles) {
      this.closeModal(this.uploadedFiles);
      return;
    }
    this.closeModal('Error');
  }

  private filterModalTableColumnsByDocType(): void {
    const subtypes = this.uploadedFiles
      .filter((el) => el['idType'])
      .map((el) => el['idType']);

    const index: number = this.modalTableColumns.findIndex(
      (column) =>
        column.cellComponentName === 'dropdownCellComponent' &&
        column.key === 'docType',
    );

    if (
      this.modalTableColumns[index]?.cellConfig &&
      this.modalTableColumns[index]?.cellConfig?.['options']
    ) {
      this.modalTableColumns[index].cellConfig!['options'] =
        this.optionsDropdown.filter(
          (el: SeDropdownOption) => !subtypes.includes(el.id?.toString()),
        );
    }
  }

  private getDocTypesList(tableColumns: Column[]): SeDropdownOption[] {
    const index: number = tableColumns.findIndex(
      (column) =>
        column.cellComponentName === 'dropdownCellComponent' &&
        column.key === 'docType',
    );

    if (index === -1 || !tableColumns?.[index].cellConfig?.['options'])
      return [];

    return tableColumns[index].cellConfig?.['options'].map(
      (option: SeDropdownOption) => {
        return {
          id: option[tableColumns[index].cellConfig?.['optionValue'] || 'id'],
          label:
            option[tableColumns[index].cellConfig?.['optionLabel'] || 'label'],
        };
      },
    );
  }

  private checkClosableDisabled(): void {
    this.data.closableDisabled =
      this.uploadedFiles.length === 0 || this.isButtonDisabled;
  }

  protected customTrackById(index: number, row: FlattenedRow): string {
    // const track = row['id'] + (row.cells[index].rowData['docType'].value ?? '');
    return row['id'];
  }
}
