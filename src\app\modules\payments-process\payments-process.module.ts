import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Routes, RouterModule } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { PaymentsProcessComponent } from './payments-process.component';
import { LazyElementsModule } from '@angular-extensions/elements';
import { environment } from '@environments/environment';
import {
  SeButtonModule,
  SeLinkModule,
  SePanelModule,
  SeTableModule,
  SeTagModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { PaymentsConfirmationComponent } from './payments-confirmation/payments-confirmation.component';
import { HideOnCoordinatorDirective } from '@app/core/directives';
import { InfoMessageModule } from '@app/shared/components';
import { CivilServantRequiredDocBlockModule } from '@app/shared/components/civil-servant-required-doc-block';

const routes: Routes = [
  { path: '', redirectTo: 'resum', pathMatch: 'full' },
  {
    path: 'resum',
    component: PaymentsProcessComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      isElementVisible: false,
      isHeaderVisible: false,
    },
  },
  {
    path: 'confirmacio',
    component: PaymentsConfirmationComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      isElementVisible: false,
      isHeaderVisible: false,
    },
  },
];

@NgModule({
  declarations: [PaymentsProcessComponent, PaymentsConfirmationComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    SeTagModule,
    SeLinkModule,
    CivilServantRequiredDocBlockModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-pagaments-proces-pagament',
          url: environment.mfPagamentsURL,
          loadingComponent: SpinnerComponent,
          preload: false,
        },
        {
          tag: 'mf-pagaments-confirmacio-pagament',
          url: environment.mfPagamentsURL,
          loadingComponent: SpinnerComponent,
          preload: false,
        },
      ],
    }),
    SeTableModule,
    SePanelModule,
    SeButtonModule,
    HideOnCoordinatorDirective,
    InfoMessageModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class PagamentsModule {}
