import { Component, Input, OnInit } from '@angular/core';
import { Column } from 'se-ui-components-mf-lib';
import { PagamentsTableService } from './pagaments-table.service';
import { ProcessFilter } from '../models/process.model';
import { PaginationService } from '@app/shared/services';

@Component({
  selector: 'app-pagaments',
  template: `
    <mf-gestions-payments-table
      *axLazyElement
      [columns]="paymentColumns"
      [processFilters]="co2Filters"
      [itemsPerPage]="itemsPerPage"
      [rowsPerPageOptions]="rowsPerPageOptions"
    ></mf-gestions-payments-table>
  `,
})
export class PagamentsTableComponent implements OnInit {
  paymentColumns: Column[] | undefined;
  protected itemsPerPage = this.paginationSrv.getItemsPerPage();
  protected rowsPerPageOptions = this.paginationSrv.getRowsPerPageOptions();
  @Input({ required: true }) co2Filters!: ProcessFilter;

  constructor(
    private pagamentsService: PagamentsTableService,
    private paginationSrv: PaginationService,
  ) {}

  ngOnInit(): void {
    this.paymentColumns = this.pagamentsService.getPaymentColumns();
  }
}
