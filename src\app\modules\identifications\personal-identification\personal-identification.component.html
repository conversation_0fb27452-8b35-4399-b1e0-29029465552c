<div class="app-identificacio">
  <!-- panel de radio buttons para seleccionar en nombre propio o en nombre de tercera persona -->
  <form
    *ngIf="!showThirdPersonForm"
    class="mt-2 d-flex flex-column"
    [formGroup]="identificationForm"
  >
    <se-panel
      [title]="'SE_PADRO_CO2.MODULE_IDENTIFICATION.QUESTION' | translate"
    >
      <div seHighlightRadioContainer>
        <se-radio
          id="radio-1"
          name="identification"
          [label]="
            'SE_PADRO_CO2.MODULE_IDENTIFICATION.OPTIONS.PERSONAL' | translate
          "
          [value]="IdentificationType.NOM_PROPI"
          formControlName="identificationType"
        >
        </se-radio>
      </div>
      <div seHighlightRadioContainer class="mt-4">
        <se-radio
          id="radio-2"
          name="identification"
          [label]="
            'SE_PADRO_CO2.MODULE_IDENTIFICATION.OPTIONS.THIRD_PERSON'
              | translate
          "
          [value]="IdentificationType.REPRESENTATIVE"
          [disabled]="!hasRepresentants"
          formControlName="identificationType"
        >
        </se-radio>
      </div>
      <div class="mt-4" *ngIf="statementOption && isResponsibleStatementShown">
        <mf-seguretat-declaracio-responsable
          *axLazyElement
          [table]="false"
          [list]="true"
          [subtitle]="true"
          [checkboxLabel]="
            'SE_PADRO_CO2.MODULE_IDENTIFICATION.RESPONSIBILITY_CHECK'
              | translate
          "
          [taxpayers]="statementOption"
          (onChange)="DRChanged($event)"
        >
        </mf-seguretat-declaracio-responsable>
      </div>
    </se-panel>
    <div class="d-flex flex-column flex-sm-row justify-content-sm-end mt-4">
      <se-button type="submit" [btnTheme]="'primary'" (click)="continue()">
        {{ 'SE_PADRO_CO2.BUTTONS.ACCESS' | translate }}
      </se-button>
    </div>
  </form>

  <!-- TERCERA PERSONA - sujeto pasivo -->
  <form *ngIf="showThirdPersonForm" class="row" [formGroup]="thirdPersonForm">
    <se-panel [title]="'SE_PADRO_CO2.PASSIVE_SUBJECT' | translate">
      <p>
        {{
          'SE_PADRO_CO2.MODULE_IDENTIFICATION.TAXPAYER.DESCRIPTION' | translate
        }}
      </p>
      <mf-seguretat-scoring
        *axLazyElement
        class="mb-4 mb-md-0"
        [taxpayer]="scoringTaxpayer"
        [taxPayerDocumentLabel]="taxPayerDocumentLabel"
        [taxPayerNameLabel]="'SE_PADRO_CO2.LABELS.SOCIAL_REASON' | translate"
        (onValidationChange)="onScoringValidationChange($event)"
        (onTaxpayerChange)="onScoringTaxpayerChange($event)"
      />
    </se-panel>

    <!-- boton de tramitar -->
    <div
      class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
    >
      <se-button type="submit" [btnTheme]="'secondary'" (click)="goBack()">
        {{ 'SE_PADRO_CO2.BUTTONS.RETURN' | translate }}
      </se-button>
      <se-button
        type="submit"
        [btnTheme]="'primary'"
        [disabled]="disableSubmitButton"
        (click)="continue()"
      >
        {{ 'SE_PADRO_CO2.BUTTONS.ACCESS' | translate }}
      </se-button>
    </div>
  </form>
</div>
