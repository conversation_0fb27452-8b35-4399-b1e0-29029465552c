import { Injectable } from '@angular/core';
import { WcComponentInput } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root'
})
export class MfActionCourseService {

  // Webcomponent > Input data
  private data!: WcComponentInput;

  setData(value: WcComponentInput): void {
    this.data = value;
  }

  getData(): WcComponentInput {
    return this.data;
  }

  resetData(): void {
    this.data = null!;
  }

}