import { Injectable } from '@angular/core';
import { Co2EmissionsModalConceptsData } from '../models/co2-emissions-modal.model';
import { DetallPadro } from '@app/shared/models';

@Injectable({
  providedIn: 'root',
})
export class Co2EmissionsModalService {
  getM1DieselFormula(detailData: DetallPadro): Co2EmissionsModalConceptsData[] {
    return [
      {
        title: 'CC',
        constant: '0,01642',
        value: detailData.cilindrada,
      },
      {
        title: 'MMX',
        constant: '0,0114',
        value: detailData.massaMaxima,
        operation: '+',
      },
      {
        title: 'MOM',
        constant: '0,05745',
        value: detailData.massaOrdreMarxa,
        operation: '+',
      },
      {
        title: 'TR',
        constant: '0,005106',
        value: detailData.tara,
        operation: '+',
      },
      {
        title: 'T',
        constant: '3,471',
        value: detailData.antiguitat,
        operation: '+',
      },
      {
        operation: '-',
        value: '37.15',
      },
    ];
  }

  getN1DieselFormula(detailData: DetallPadro): Co2EmissionsModalConceptsData[] {
    return [
      {
        title: 'CC',
        constant: '0,01144',
        value: detailData.cilindrada,
      },
      {
        title: 'PF',
        constant: '2,699',
        value: detailData.potenciaFiscal,
        operation: '+',
      },
      {
        title: 'PN',
        constant: '0,02635',
        value: detailData.potenciaNeta,
        operation: '+',
      },
      {
        title: 'MMX',
        constant: '0,02562',
        value: detailData.massaMaxima,
        operation: '+',
      },
      {
        title: 'MOM',
        constant: '0,03115',
        value: detailData.massaOrdreMarxa,
        operation: '+',
      },
      {
        title: 'T',
        constant: '2,922',
        value: detailData.antiguitat,
        operation: '+',
      },
      {
        operation: '-',
        value: '25.64',
      },
    ];
  }

  getGasolinaFormula(detailData: DetallPadro): Co2EmissionsModalConceptsData[] {
    return [
      {
        title: 'CC',
        constant: '0,01149',
        value: detailData.cilindrada,
      },
      {
        title: 'PF',
        constant: '3,879',
        value: detailData.potenciaFiscal,
        operation: '+',
      },
      {
        title: 'MOM',
        constant: '0,04008',
        value: detailData.massaOrdreMarxa,
        operation: '+',
      },
      {
        title: 'TR',
        constant: '0,009541',
        value: detailData.tara,
        operation: '+',
      },
      {
        title: 'T',
        constant: '2,605',
        value: detailData.antiguitat,
        operation: '+',
      },
      {
        operation: '+',
        value: '4.35',
      },
    ];
  }

  getHevFormula(detailData: DetallPadro): Co2EmissionsModalConceptsData[] {
    return [
      {
        title: 'PF',
        constant: '0,8533',
        value: detailData.potenciaFiscal,
      },
      {
        title: 'PN',
        constant: '0,1909',
        value: detailData.potenciaNeta,
        operation: '+',
      },
      {
        title: 'MMX',
        constant: '0,02794',
        value: detailData.massaMaxima,
        operation: '+',
      },
      {
        title: 'T',
        constant: '0,3922',
        value: detailData.antiguitat,
        operation: '+',
      },
      {
        operation: '+',
        value: '14.28',
      },
    ];
  }

  getLCategoryFormula(
    detailData: DetallPadro,
  ): Co2EmissionsModalConceptsData[] {
    return [
      {
        title: 'PF',
        constant: '3,311',
        value: detailData.potenciaFiscal,
      },
      {
        title: 'PN',
        constant: '0,262',
        value: detailData.potenciaNeta,
        operation: '+',
      },
      {
        title: 'MOM',
        constant: '0,1611',
        value: detailData.massaOrdreMarxa,
        operation: '+',
      },
      {
        title: 'T',
        constant: '1,026',
        value: detailData.antiguitat,
        operation: '+',
      },
      {
        operation: '+',
        value: '28.98',
      },
    ];
  }

  getRestVehiclesFormula(
    detailData: DetallPadro,
  ): Co2EmissionsModalConceptsData[] {
    return [
      {
        title: 'CC',
        constant: '0,03399',
        value: detailData.cilindrada,
      },
      {
        title: 'PN',
        constant: '0,06862',
        value: detailData.potenciaNeta,
        operation: '+',
      },
      {
        title: 'TR',
        constant: ' 0,04134',
        value: detailData.tara,
        operation: '+',
      },
      {
        title: 'T',
        constant: '1,996',
        value: detailData.antiguitat,
        operation: '+',
      },
      {
        operation: '+',
        value: '18.89',
      },
    ];
  }
}
