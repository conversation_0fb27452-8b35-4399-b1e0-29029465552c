import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import { SeHttpResponse, SeHttpService } from 'se-ui-components-mf-lib';
import {
  DeclarationTypes,
  PutCsvUploaded,
  PutTaxYearAndModelDeclaration,
} from '../tax-year-declaration.model';

@Injectable({
  providedIn: 'root',
})
export class TaxYearDeclarationEndpointService {
  constructor(private httpService: SeHttpService) {
    // Empty constructor
  }

  getDeclarationTypes(): Observable<SeHttpResponse<DeclarationTypes[]>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/model`,
      clearExceptions: true,
    });
  }

  getTaxYearsByImpost(impost: string): Observable<SeHttpResponse<number[]>> {
    return this.httpService.get({
      method: 'get',
      baseUrl: environment.baseUrlTributs,
      url: `/${impost}/exercici`,
      clearExceptions: true,
    });
  }

  putTaxYearAndModelDeclaration(
    body: PutTaxYearAndModelDeclaration,
  ): Observable<SeHttpResponse> {
    return this.httpService.put({
      method: 'put',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${body.idTramit}`,
      body,
      headers: {
        codiImpost: body.codiImpost,
        exercici: body.exercici,
        model: body.model,
      },
      clearExceptions: true,
    });
  }

  putFileCsvUploaded(
    body: PutCsvUploaded,
    idTramit: string,
  ): Observable<SeHttpResponse> {
    return this.httpService.put({
      method: 'put',
      baseUrl: environment.baseUrlDeclaracionsInformatives,
      url: `/declaracio/${idTramit}/fitxer`,
      body: {
        body,
      },
      clearExceptions: true,
    });
  }
}
