import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { Subject, takeUntil } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { AppRoutes } from '@core/models';
import { Column, Nullable, Row, SeModal } from 'se-ui-components-mf-lib';
import { Vehicle, VehiclesSelectedInfo } from '@shared/models';
import { CustomRouterService } from '@app/core/services';
import { ModalVehiclesEndpointService } from './modal-vehicles-endpoint.service';

const BASE_TRANSLATE = 'SE_PADRO_CO2.MODAL.VEHICLES';

@Component({
  selector: 'app-modal-vehicles',
  templateUrl: './modal-vehicles.component.html',
  styleUrls: ['./modal-vehicles.component.scss'],
})
export class ModalVehiclesComponent implements OnInit, OnDestroy {
  @Input() data: SeModal | undefined;
  @Input() vehiclesPlates: Nullable<VehiclesSelectedInfo>;
  @Input() hideExerciceColumn?: boolean = false;

  vehiclesColumns: Column[] = [];
  vehiclesRows: Row[] = [];
  vehicles: Nullable<Vehicle[]>;

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private activatedModalService: NgbActiveModal,
    private translateService: TranslateService,
    private endpointService: ModalVehiclesEndpointService,
    private customRouter: CustomRouterService,
  ) {}

  ngOnInit(): void {
    this.getVehiclesInfo();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  getVehiclesInfo(): void {
    if (this.vehiclesPlates) {
      const request: VehiclesSelectedInfo = {
        provisional: this.vehiclesPlates.provisional,
        matriculas: this.vehiclesPlates.matriculas,
        exercici: this.vehiclesPlates.exercici,
        idPersTitular: this.vehiclesPlates.idPersTitular,
      };
      this.endpointService
        .getVehiclesTableInfo(request)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result) => {
          if (result?.content) {
            this.vehicles = result?.content;
            this.setVehiclesColumns();
            this.setVehiclesRows();
            return;
          }

          this.vehicles = [];
        });
    }
  }

  setVehiclesColumns(): void {
    this.vehiclesColumns = this.getVehiclesColumns();
  }

  setVehiclesRows(): void {
    //TODO: Una vez tenga los datos del back, restructurar mapeo
    this.vehiclesRows = this.getVehiclesRows(this.vehicles);
  }

  getVehiclesRows(vehicles: Nullable<Vehicle[]>): Row[] {
    return (vehicles ?? []).map((vehicle) => {
      return {
        data: {
          matricula: {
            value: vehicle.matricula,
          },
          marca: {
            value: vehicle.marca,
          },
          model: {
            value: vehicle.model,
          },
          exercici: {
            value: this.vehiclesPlates?.exercici,
          },
        },
      };
    });
  }

  getVehiclesColumns(): Column[] {
    return [
      {
        key: 'matricula',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.MATRICULA`,
        ),
        size: 10,
      },
      {
        key: 'marca',
        header: this.translateService.instant(`${BASE_TRANSLATE}.TABLE.MARCA`),
        size: 10,
      },
      {
        key: 'model',
        header: this.translateService.instant(`${BASE_TRANSLATE}.TABLE.MODEL`),
        size: 10,
      },
      ...(!this.hideExerciceColumn
        ? [
            {
              key: 'exercici',
              header: this.translateService.instant(
                `${BASE_TRANSLATE}.TABLE.EXERCICI`,
              ),
              size: 10,
            },
          ]
        : []),
    ];
  }

  closeModal(): void {
    this.activatedModalService.close();
  }

  returnToList(): void {
    this.activatedModalService.close();
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }
}
