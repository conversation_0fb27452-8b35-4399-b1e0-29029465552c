import { Injectable } from '@angular/core';
import {
  DomiciledProcedureResponseFinalizar,
  ProcedureSaved,
  AppRoutes,
  Co2User,
  DomiciledProcedureRequest,
  DomiciledVehicle,
  ResourceProcessDocument,
} from '@app/core/models';
import {
  CustomRouterService,
  LoginResponseService,
  StorageService,
} from '@app/core/services';
import { ProcessNameToDisplayInPresentationReceipt } from '@app/modules/presentation-receipt';
import { DomiciliationEndpointService } from '@app/shared/services';
import { TranslateService } from '@ngx-translate/core';
import { Observable, merge, take, tap } from 'rxjs';
import {
  iDocumentPadoct,
  SeModalOutputEvents,
  SeModalService,
} from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class ModificationResumeUnsubscribeService {
  constructor(
    private seModalService: SeModalService,
    private translateService: TranslateService,
    private domiciliationEndpointService: DomiciliationEndpointService,
    private storageService: StorageService,
    private loginSrv: LoginResponseService,
    private customRouter: CustomRouterService,
  ) {}

  showConfirmationUnsubscribeModal(
    idEntity: string,
    nom: string | undefined,
    cognoms: string | undefined,
  ): void {
    const modalRef = this.seModalService.openModal({
      severity: 'warning',
      title: this.translateService.instant(
        'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.UNSUBSCRIBE.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.UNSUBSCRIBE.SUBTITLE',
      ),
      closable: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.CONTINUE_WITH_CANCELLATION',
      secondaryButton: true,
      secondaryButtonLabel: 'UI_COMPONENTS.BUTTONS.CANCEL',
    });

    const second: Observable<string> =
      modalRef.componentInstance.modalSecondaryButtonEvent;
    const close: Observable<string> =
      modalRef.componentInstance.modalOutputEvent;

    merge(close, second)
      .pipe(
        take(1),
        tap(() => modalRef.close()),
      )
      .subscribe((result: string) => {
        if (result === SeModalOutputEvents.MAIN_ACTION) {
          this.unsubscribeDomiciliation(idEntity, nom, cognoms);
        }
      });
  }

  private unsubscribeDomiciliation(
    idEntity: string,
    nom: string | undefined,
    cognoms: string | undefined,
  ): void {
    const userFromLoginSrv = this.loginSrv.user;
    this.domiciliationEndpointService
      .postDomiciledProcedureFinalizar(
        this.getRequestSaveDomiciliation(
          userFromLoginSrv,
          idEntity,
          nom,
          cognoms,
        ),
      )
      .subscribe((response: DomiciledProcedureResponseFinalizar) => {
        this.setDataToShowThemInPresentationReceipt(
          response.content as ProcedureSaved,
        );
        // baja
        this.storageService.processNameToDisplayPresentationReceipt =
          ProcessNameToDisplayInPresentationReceipt.Domiciliation;
        this.customRouter.navigateByBaseUrl(AppRoutes.PRESENTATION_RECEIPT);
      });
  }

  private getRequestSaveDomiciliation(
    userFromLoginSrv: Co2User,
    idEntity: string,
    nom: string | undefined,
    cognoms: string | undefined,
  ): DomiciledProcedureRequest {
    return {
      idPersTitular: userFromLoginSrv.idPersTitular as string,
      listMatriculas: this.storageService.vehiclesToBeUpdated.map(
        (vehicle: DomiciledVehicle) => vehicle.matricula as string,
      ),
      tipusAccess: userFromLoginSrv.tipusAccess,
      id: idEntity,
      nom,
      cognoms,
    };
  }

  private setDataToShowThemInPresentationReceipt(
    response: ProcedureSaved,
  ): void {
    this.storageService.setResourceProcessDocumentData({
      receipt: {
        idPadoct: response.idJustificant,
        nom: response.fileName,
      } as iDocumentPadoct,
      idFunctionalModule:
        ProcessNameToDisplayInPresentationReceipt.Domiciliations_unsubscribe,
    } as ResourceProcessDocument);
  }
}
