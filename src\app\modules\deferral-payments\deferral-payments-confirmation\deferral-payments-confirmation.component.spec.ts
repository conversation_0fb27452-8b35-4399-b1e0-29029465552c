import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeferralPaymentsConfirmationComponent } from './deferral-payments-confirmation.component';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TranslateModule } from '@ngx-translate/core';

describe('DeferralPaymentsConfirmationComponent', () => {
  let component: DeferralPaymentsConfirmationComponent;
  let fixture: ComponentFixture<DeferralPaymentsConfirmationComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DeferralPaymentsConfirmationComponent],
      imports: [HttpClientTestingModule, TranslateModule.forRoot()],
    });
    fixture = TestBed.createComponent(DeferralPaymentsConfirmationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
