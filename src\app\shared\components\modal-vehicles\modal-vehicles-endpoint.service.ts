import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';
import { VehiclesModalResponse } from '@shared/components';
import { VehiclesSelectedInfo } from '@app/shared/models';

@Injectable({
  providedIn: 'root',
})
export class ModalVehiclesEndpointService {
  constructor(private httpService: SeHttpService) {}

  getVehiclesTableInfo(
    body: VehiclesSelectedInfo,
  ): Observable<VehiclesModalResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `detall-vehicles`,
      method: 'post',
      body,
    };

    return this.httpService.post<VehiclesModalResponse>(httpRequest);
  }
}
