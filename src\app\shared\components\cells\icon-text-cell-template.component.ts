import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import {
  CellComponent,
  CellConfig,
  Column,
  FlattenedCell,
  FlattenedRow,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-icon-text-cell',
  template: `
    <div class="row">
      <div class="col-2">
        <ng-icon [name]="icon" class="tooltip-icon" [size]="'20px'"> </ng-icon>
      </div>
      <div class="col-auto align-content-center">
        <span class="col-auto">{{ text }}</span>
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IconTextComponent implements CellComponent {
  @Input() value: unknown;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  get icon(): string {
    return this.row.rowConfig['icon'] || '';
  }
  get text(): string {
    return this.row.rowConfig['label'] || '';
  }
}
