import { Injectable } from '@angular/core';
import { HeaderInfoService, StorageService } from '@app/core/services';
import { Vehicle } from '@app/shared/models';
import { IdentificationType } from '@core/models';
import { Nullable } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class ProceduresHeaderService {
  private isConveniat: boolean = false;
  private isRepresentant: boolean = false;
  private isCivilServant: boolean = false;
  private isSelfPerson: boolean = false;
  private isLoginSimple: boolean = false;

  constructor(
    private header: HeaderInfoService,
    private storage: StorageService,
  ) {
    this.isConveniat =
      this.storage.profileUser === IdentificationType.CONVENIAT;
    this.isRepresentant =
      this.storage.profileUser === IdentificationType.REPRESENTATIVE;
    this.isCivilServant =
      this.storage.profileUser === IdentificationType.CIVIL_SERVANT;
    this.isSelfPerson =
      this.storage.profileUser === IdentificationType.NOM_PROPI;
    this.isLoginSimple =
      this.storage.profileUser === IdentificationType.LOGIN_SIMPLE;
  }

  setupDomiciliationHeader(vehicles?: Nullable<Vehicle[]>): void {
    this.resetAndSetupHeaderTitle(
      'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.TITLE',
    );

    this.handleCommonHeaderSetup(vehicles);
  }

  setupPaymentHeader(vehicles?: Nullable<Vehicle[]>): void {
    if (this.isSelfPerson) return;

    this.resetAndSetupHeaderTitle('SE_PADRO_CO2.PAYMENTS_PROCESS.TITLE');
    this.header.show();

    this.handleCommonHeaderSetup(vehicles);
  }

  setupRecursHeader(matriculas?: Nullable<string[]>): void {
    this.resetAndSetupHeaderTitle('SE_PADRO_CO2.RECURS_PROCESS.TITLE');

    const vehiclesModalTitle =
      'SE_PADRO_CO2.RECURS_PROCESS.VEHICLES_MODAL.TITLE';

    this.handleCommonHeaderSetup(
      undefined,
      matriculas,
      vehiclesModalTitle,
      false,
    );
  }

  setupReasHeader(matriculas?: Nullable<string[]>): void {
    this.resetAndSetupHeaderTitle('SE_PADRO_CO2.REAS_PROCESS.TITLE');

    const vehiclesModalTitle = 'SE_PADRO_CO2.REAS_PROCESS.VEHICLES_MODAL.TITLE';

    this.handleCommonHeaderSetup(
      undefined,
      matriculas,
      vehiclesModalTitle,
      false,
    );
  }

  setupDeferralPaymentsHeader(vehicles?: Nullable<Vehicle[]>): void {
    this.resetAndSetupHeaderTitle(
      'SE_PADRO_CO2.DEFERRAL_PAYMENTS_PROCESS.TITLE',
    );
    this.handleCommonHeaderSetup(vehicles);
  }

  setupAlegationsHeader(matriculas?: Nullable<string[]>): void {
    this.resetAndSetupHeaderTitle('SE_PADRO_CO2.ALLEGATIONS.TITLE');

    this.handleCommonHeaderSetup(undefined, matriculas);
  }

  private resetAndSetupHeaderTitle(title: string): void {
    this.header.reset();
    this.header.setFullscreenMode(true);
    this.header.setTitleHeader(title);
  }

  private handleCommonHeaderSetup(
    vehicles: Nullable<Vehicle[]>,
    matriculas?: Nullable<string[]>,
    modalTitle?: string,
    hideExerciceColumn: boolean = true,
  ): void {
    if (this.isSelfPerson) this.header.setNifTitular();

    if (this.isLoginSimple) this.header.setNifTitular(null);

    if (this.isCivilServant) this.header.setCivilServantHeader();

    if (this.isRepresentant) this.header.setRepresentantHeader();

    if (this.isConveniat) this.header.setConveniantHeader();

    if (matriculas?.length || vehicles?.length)
      this.setHeaderVehiclesInfo(
        vehicles,
        matriculas,
        modalTitle,
        hideExerciceColumn,
      );
  }

  private setHeaderVehiclesInfo(
    vehicles: Nullable<Vehicle[]>,
    matriculas?: Nullable<string[]>,
    modalTitle?: string,
    hideExerciceColumn: boolean = true,
  ): void {
    const mappedMatriculas =
      vehicles?.map((vehicle) => vehicle.matricula) ?? [];

    this.header.setMatriculaOrVehicles(
      matriculas ?? mappedMatriculas,
      undefined,
      hideExerciceColumn,
      modalTitle,
    );
  }
}
