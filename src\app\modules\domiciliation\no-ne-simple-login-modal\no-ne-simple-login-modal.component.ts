import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SeModal } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-continue-without-new-vehicles-modal',
  template: `
    <se-modal [data]="data" (modalOutputEvent)="closeModal()">
      <p>
        <strong>
          {{
            'SE_PADRO_CO2.DOMICILIATION_MODULE.NO_NE_SIMPLE_LOGIN_MODAL.SUBTITLE'
              | translate
          }}
        </strong>
      </p>
    </se-modal>
  `,
})
export class NoNeSimpleLoginModalComponent {
  @Input() data!: SeModal;

  @Output() closeNeModal = new EventEmitter<void>();
  constructor(private activatedModalService: NgbActiveModal) {}

  closeModal(): void {
    this.closeNeModal.emit();
    this.activatedModalService.close();
  }
}
