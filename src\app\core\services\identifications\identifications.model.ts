export interface UserHasAgreementResponse {
  messages: string[];
  content: boolean;
}

export const ATESA_AGREEMENT_ID = '28';
export const COORDINADOR_AGREEMENT_ID = '145';
export const O12_AGREEMENT_ID = '115';
export const CO2_AGREEMENT_ID = '25';
export const CO2_TAX_ID = '38';

export interface GetRespresentativeMapResponse {
  isRepresentative: boolean;
  representativesData: RepresentativesData[];
}

export interface RepresentativesData {
  _id: string;
  ambit: string;
  clauProcediment: string;
  codiTipusActuacio: string;
  codiSistemaOrigen: string;
  codiTipusRelacio: string;
  codiDetallRelacioOrigen: string;
  codiDetallRelacioDesti: string;
  codiAmbitRelacioRepr: string;
  codiImpost: string;
  codiTipusProcediment: string;
  codiEstatRelacio: string;
  codiEvidenciaRelacio: string;
  codiRegimMatrimoni: string;
  detallRelacioOrigen: string;
  detallRelacioDesti: string;
  dataIniciValidesa: string;
  dataFinalValidesa: string;
  documentOrigen: string;
  documentDesti: string;
  dataSollicitud: string;
  dataTimestamp: string;
  estatRelacio: string;
  evidenciaRelacio: string;
  estat: string;
  fontOrigen: string;
  impost: string;
  idPerssCensDesti: string;
  idPersBdfOrigen: string;
  idPersSprOrigen: string;
  idPersGauOrigen: string;
  idPersBdfDesti: string;
  idRelacio: string;
  idPersSprDesti: string;
  idPersGauDesti: string;
  idPropagacio: string;
  idDocumentRelacio: string;
  niuOrigen: string;
  niuDesti: string;
  nomOrigen: string;
  nomDesti: string;
  regimBeMatrimoni: string;
  sistemaOrigen: string;
  tractat: string;
  tipusRelacio: string;
  tipusProcediment: string;
  tipusActuacio: string;
  usuariModificacio: string;
}
