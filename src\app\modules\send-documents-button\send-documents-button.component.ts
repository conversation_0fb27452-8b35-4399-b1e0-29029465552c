import {
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { Subject, takeUntil } from 'rxjs';

import { SeModal, SeModalService } from 'se-ui-components-mf-lib';
import { EndPointService } from 'src/app/core/services/end-points.service';
import { MfActionCourseService } from 'src/app/core/services/mf-action-course.service';
import { ModalSendDocumentationComponent } from './modal-send-documentation/modal-send-documentation.component';
import { SendDocumentForm } from './send-documents-button.model';

@Component({
  selector: 'app-send-documents-button',
  template: `
    <se-button
      [size]="size"
      [btnTheme]="btnTheme"
      [disabled]="disabled"
      [icon]="icon"
      [iconPosition]="iconPosition"
      [type]="type"
      (onClick)="sendDocumentation()"
    >
      {{
        buttonLabel ??
          ('SE_DOCUMENTS_MF.SEND_DOCUMENTS_BUTTON.SEND_DOCUMENTS' | translate)
      }}
    </se-button>
  `,
})
export class SendDocumentsButtonComponent implements OnInit, OnDestroy {
  @Input() idDocument: string[] = [];
  @Input() size!: 'large' | 'default' | 'small';
  @Input() btnTheme!: 'primary' | 'secondary' | 'onlyText' | 'danger';
  @Input() disabled = false;
  @Input() icon = '';
  @Input() iconPosition!: 'right' | 'left';
  @Input() type!: string;
  @Input() buttonLabel?: string;
  @Input() emitSendDocument = false;
  @Output() sendDocumentEvent: EventEmitter<string> = new EventEmitter();

  private readonly _unsubscribe: Subject<void> = new Subject<void>();

  constructor(
    private readonly modalService: SeModalService,
    private readonly endPointService: EndPointService,
    private readonly mfActionService: MfActionCourseService,
  ) {
    console.log(
      'Webcomponent: Documents > SendDocumentsButtonComponent > constructor',
    );
  }

  ngOnInit(): void {
    this.idDocument =
      this.idDocument ||
      (this.mfActionService.getData()?.data?.idDocument ?? []);
  }

  protected sendDocumentation(): void {
    const modalData: SeModal = {
      component: ModalSendDocumentationComponent,
    };
    this.modalService
      .openModal(modalData)
      .result.then((result: SendDocumentForm | null) => {
        if (result) {
          if (this.emitSendDocument) {
            this.sendDocumentEvent.emit(result.email);
          } else {
            this.endPointService
              .sendJustificant(result.email, this.idDocument)
              .pipe(takeUntil(this._unsubscribe))
              .subscribe();
          }
        }
      });
  }

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }
}
