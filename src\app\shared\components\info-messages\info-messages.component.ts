import { Component, Input } from '@angular/core';
import { environment } from '@environments/environment';

@Component({
  selector: 'app-info-messages',
  templateUrl: './info-messages.component.html',
  styleUrls: ['./info-messages.component.scss'],
})
export class InfoMessageComponent {
  /**
   *  Unique id for the info message
   * @type {string}
   * @memberof InfoMessageComponent
   */
  @Input() id: string = 'info-message-id-' + Math.random();

  /**
   * Title of the info message
   * @type {string}
   * @memberof InfoMessageComponent
   */
  @Input() title!: string;

  /**
   * Represents the text of the info message.
   * Can be a string for a paragraph or a list of strings for a li.
   * @type {(string | string[])}
   * @memberof InfoMessageComponent
   */
  @Input() text!: string[] | string;

  /**
   * name of the icon to be displayed in the info message
   * @type {'send' | 'calendar' | 'note'}
   * @memberof InfoMessageComponent
   */
  @Input() image?: 'send' | 'calendar' | 'note' | 'id_cat_logo';

  get src(): string {
    return `${environment.baseUrlCommons}/images/${this.image}.png`;
  }

  get isAList(): boolean {
    return Array.isArray(this.text);
  }
  get list(): string[] {
    return this.isAList ? (this.text as string[]) : [];
  }
}
