import { TextTagCellComponent } from '@shared/components';
import { Column } from 'se-ui-components-mf-lib';

export enum VehiclesToDomicileKeys {
  Plate = 'plate',
  Vehicle = 'vehicle',
}

const plateColumn = {
  header:
    'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.VEHICLES_TO_BE_REGISTERED.TABLE.CAR_REGISTRATION',
  key: VehiclesToDomicileKeys.Plate,
};
const vehicleColumn = {
  header:
    'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.VEHICLES_TO_BE_REGISTERED.TABLE.VEHICLE',
  key: VehiclesToDomicileKeys.Vehicle,
};

export const VEHICLES_TO_DOMICILE_COLUMNS: Column[] = [
  {
    ...plateColumn,
    cellComponent: TextTagCellComponent,
    resizable: true,
    cellConfig: {
      ellipsis: true,
      tooltip: false,
      tagValue:
        'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.VEHICLES_TO_BE_REGISTERED.TABLE.NEW',
      iconName: 'matInfoOutline',
      tagCell: { tagTheme: 'gray' },
    },
  },
  vehicleColumn,
];

export const OLD_VEHICLES_COLUMNS: Column[] = [plateColumn, vehicleColumn];

// modal de se-contribuent-mf / notificacions
export interface ContactDataInputData {
  daysToReminder: number;
  urlToRedirectOnAccess?: string;
  disableLogoutOnDeniedAccess: boolean;
  wcAsModal: boolean;
  showModal: boolean;
  hideReceipt: boolean;
  requiredNotificationCheck?: boolean;
  requiredNoticeCheck?: boolean;
  notificationsCheckLabel?: string;
  modalTitle?: string;
  noticesCheckLabel?: string;
  checksText?: string;
  checksTitle?: string;
  isSubscription?: boolean;
}
