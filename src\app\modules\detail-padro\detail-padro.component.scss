:host .mt-3 se-alert ::ng-deep {
  .alert {
    align-items: center !important;
  }
}

:host .calculus se-button ::ng-deep {
  button {
    width: 100% !important;
  }
}

:host {
  .detail-padro {
    .button-container {
      se-button {
        ::ng-deep {
          .se-button {
            padding-left: 0 !important;
            justify-content: flex-start;

            &:hover {
              background: transparent;
              border: 1px solid transparent;
              color: var(--color-primary-action);
            }
          }
        }
      }
    }

    .info-container {
      .right {
        se-panel {
          ::ng-deep {
            .se-panel__header {
              justify-content: center;
            }
          }
        }

        .currency {
          line-height: var(--line-2xl);
          text-align: left;
          color: var(--color-pink-500);
          min-height: 28px;
          font-weight: bold;
          font-size: 28px;
        }

        .info {
          border: solid 3px var(--color-green-200);
          padding: 24px;
          border-radius: 8px;

          .img {
            width: 86px;
            height: 64px;
          }
        }
      }
    }
  }

  .blue-border {
    border: solid 3px var(--color-blue-300);
    padding: 16px;
    border-radius: 8px;
  }
}
