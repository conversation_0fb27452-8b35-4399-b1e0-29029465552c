<div class="domiciliation-request">
  <se-panel
    [title]="
      'SE_PADRO_CO2.DOMICILIATION_MODULE.REQUEST_DIRECT_DEBIT_PANEL.TITLE'
        | translate
    "
  >
    <ng-container *ngIf="showDetail; else emptyStateTemplate">
      <div class="text-center pt-4 pb-2 domiciliation-request__dates">
        <p>
          {{
            'SE_PADRO_CO2.DOMICILIATION_MODULE.REQUEST_DIRECT_DEBIT_PANEL.EXPECTED_DATE'
              | translate: { exerciceDom }
          }}
        </p>
        <h4 *ngIf="domiciliationDate">{{ domiciliationDate }}</h4>
      </div>

      <hr />

      <div
        *ngIf="bodyVehicles?.matriculas"
        class="p-3 pt-0 d-flex flex-direction-row align-items-center justify-content-between domiciliation-request__vehicles"
      >
        <div>
          <p>
            <strong>
              {{
                'SE_PADRO_CO2.DOMICILIATION_MODULE.REQUEST_DIRECT_DEBIT_PANEL.VEHICLES'
                  | translate
              }}
            </strong>
          </p>
        </div>

        <div class="d-flex align-items-center">
          <p *ngIf="bodyVehicles!.matriculas?.length === 1">
            <strong>
              {{
                'SE_PADRO_CO2.SHARED_MODULE.REQUEST_SUMMARY.NUMBER_VEHICLE'
                  | translate
              }}
            </strong>
          </p>
          <p
            *ngIf="
              bodyVehicles!.matriculas?.length &&
              bodyVehicles!.matriculas!.length! > 1
            "
          >
            {{
              'SE_PADRO_CO2.SHARED_MODULE.REQUEST_SUMMARY.NUMBER_VEHICLES'
                | translate: { vehicleNumber: bodyVehicles!.matriculas!.length }
            }}
          </p>
          <se-link
            class="ms-3"
            [disabled]="false"
            [size]="'regular'"
            [linkTheme]="'secondary'"
            (onClick)="onOpenVehiclesModal()"
          >
            {{ 'SE_PADRO_CO2.BUTTONS.SEE_DETAIL' | translate }}
          </se-link>
        </div>
      </div>

      <div class="px-3">
        <se-alert
          *ngIf="alert"
          [type]="'info'"
          [closeButton]="false"
          [title]="alert.title"
        ></se-alert>
      </div>
    </ng-container>
  </se-panel>
</div>

<ng-template #emptyStateTemplate>
  <div class="p-4">
    <se-empty-state
      [backgroundTheme]="'primary'"
      icon="info"
      [message]="
        'SE_PADRO_CO2.DOMICILIATION_MODULE.REQUEST_DIRECT_DEBIT_PANEL.EMPTY_STATE_MESSAGE'
          | translate
      "
    >
    </se-empty-state>
  </div>
</ng-template>
