<div class="bank-details">
  <div class="row">
    <div class="col-12 col-md-6 bank-details__iban-panel">
      <mf-pagaments-dades-bancaries
        *axLazyElement
        [fileFormatSeparation]="fileFormatSeparation"
        [pagamentsData]="bankDetailInput"
        [collapsible]="false"
        [validateRepresentative]="isValidateRepresentative()"
        [subtitle]="
          'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_2.BANK_DETAIL_SUBTITLE'
            | translate
        "
        (onFormChanged)="onBankDetailChange($event)"
      >
      </mf-pagaments-dades-bancaries>
    </div>

    <div class="col-12 col-md-6 bank-details__request-direct-debit-panel">
      <app-domiciliation-request
        [showDetail]="true"
        [alert]="alertDomiciliationRequest"
        [domiciliationDate]="domiciliationDate"
        [bodyVehicles]="bodyToRequestVehiclesDetail"
      ></app-domiciliation-request>
    </div>
  </div>

  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button (onClick)="goBack()" [btnTheme]="'secondary'">{{
      'UI_COMPONENTS.BUTTONS.PREVIOUS' | translate
    }}</se-button>

    <se-button (onClick)="onContinue()" [disabled]="isNextButtonDisabled">{{
      'SE_PADRO_CO2.BUTTONS.CONTINUE' | translate
    }}</se-button>
  </div>
</div>
