import { TestBed } from '@angular/core/testing';
import { TranslateModule } from '@ngx-translate/core';
import type { Row, SeButton } from 'se-ui-components-mf-lib';
import { PreviousExercisesService } from './previous-exercises.service';

describe('PreviousExercisesService', () => {
  let service: PreviousExercisesService;

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [TranslateModule.forRoot()],
      providers: [PreviousExercisesService],
    });

    service = TestBed.inject(PreviousExercisesService);
  });

  it('should create', () => {
    expect(service).toBeTruthy();
  });

  describe('updateOtherActionsDropdownButtonConfig', () => {
    it('should keep all the button properties other than the "disabled" property', () => {
      const expectedButtonIcon = 'expectedButtonIcon';
      const expectedButtonIconPosition = 'right';
      const expectedButtonIconSize = '1rem';
      let button: SeButton = {
        icon: expectedButtonIcon,
        iconPosition: expectedButtonIconPosition,
        iconSize: expectedButtonIconSize,
      };
      const rows: Row[] = [];

      ({ button } = service.updateOtherActionsDropdownButtonConfig(
        button,
        rows,
      ));

      expect(button.icon).toEqual(expectedButtonIcon);
      expect(button.iconPosition).toEqual(expectedButtonIconPosition);
      expect(button.iconSize).toEqual(expectedButtonIconSize);
    });

    it('should return the button disabled if rows array is empty', () => {
      let button: SeButton = {};
      const rows: Row[] = [];

      ({ button } = service.updateOtherActionsDropdownButtonConfig(
        button,
        rows,
      ));

      expect(button.disabled).toBeTrue();
    });

    it('should return the button enabled if rows array is not empty', () => {
      let button: SeButton = {};
      const rows: Row[] = [{ data: {} }];

      ({ button } = service.updateOtherActionsDropdownButtonConfig(
        button,
        rows,
      ));

      expect(button.disabled).toBeFalse();
    });
  });
});
