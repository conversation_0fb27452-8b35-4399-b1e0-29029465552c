import { CommonModule, CurrencyPipe } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { NgIcon } from '@ng-icons/core';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonModule,
  SeLinkModule,
  SeModalModule,
  SePanelModule,
  SeTagModule,
  SeTooltipAccessibleModule,
} from 'se-ui-components-mf-lib';
import { DetailElementDashedComponent } from './detail-element-dashed/detail-element-dashed.component';
import { DetailElementComponent } from './detail-element/detail-element.component';
import { DetailPadroComponent } from './detail-padro.component';
import { HideOnCoordinatorDirective } from '@core/directives';
import {
  Co2EmissionsModalModule,
  InfoMessageModule,
  SharesModalModule,
  TaxPeriodModalModule,
} from '@app/shared/components';
import { RecursosIReclamacionsComponent } from './recursos-i-reclamacions/recursos-i-reclamacions.component';

const routes: Routes = [
  {
    path: '',
    component: DetailPadroComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      isElementVisible: false,
      isHeaderVisible: false,
    },
  },
  {
    path: '**',
    redirectTo: '',
  },
];

@NgModule({
  imports: [
    NgIcon,
    SeTagModule,
    CommonModule,
    SePanelModule,
    SeLinkModule,
    SeAlertModule,
    SeButtonModule,
    SeTooltipAccessibleModule,
    SeModalModule,
    InfoMessageModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    HideOnCoordinatorDirective,
    SharesModalModule,
    TaxPeriodModalModule,
    Co2EmissionsModalModule,
  ],
  declarations: [
    DetailPadroComponent,
    DetailElementComponent,
    DetailElementDashedComponent,
    RecursosIReclamacionsComponent,
  ],
  providers: [CurrencyPipe],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DetailPadroModule {}
