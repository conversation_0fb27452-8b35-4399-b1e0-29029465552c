import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeModalModule,
  SePanelModule,
  SeUploadFilesModule,
} from 'se-ui-components-mf-lib';
import { UploadFilesComponent } from './upload-files.component';
import { UploadModalComponent } from './upload-modal/upload-modal.component';

@NgModule({
  declarations: [UploadFilesComponent, UploadModalComponent],
  imports: [
    CommonModule,
    SeUploadFilesModule,
    TranslateModule,
    SePanelModule,
    SeModalModule,
    SeAlertModule,
  ],
  exports: [UploadFilesComponent],
})
export class UploadFilesModule {}
