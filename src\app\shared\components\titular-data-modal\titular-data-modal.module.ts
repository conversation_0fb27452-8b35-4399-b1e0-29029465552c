import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SeModalModule, SePanelModule } from 'se-ui-components-mf-lib';
import { TitularDataModalComponent } from './titular-data-modal.component';

@NgModule({
  declarations: [TitularDataModalComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SeModalModule,
    SePanelModule,
  ],
})
export class TitularDataModalModule {}
