import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SeModal, SeModalOutputEvents } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-previous-exercises-filters-modal',
  styleUrls: ['./previous-exercises.component.scss'],
  template: ` <se-modal
    [data]="data"
    (modalOutputEvent)="onContinue($event)"
    (modalSecondaryButtonEvent)="closeModal()"
  >
    <app-previous-exercises-filters [filterForm]="filterForm">
    </app-previous-exercises-filters>
  </se-modal>`,
})
export class PreviousExercisesFiltersModalComponent {
  @Input() data: SeModal | undefined;
  @Input() filterForm: FormGroup | undefined;
  @Output() handleApplyFilter: EventEmitter<void> = new EventEmitter<void>();

  constructor(private readonly activatedModalService: NgbActiveModal) {}

  protected onContinue(event: string): void {
    if (event === SeModalOutputEvents.MAIN_ACTION) {
      this.handleApplyFilter.emit();
    }

    this.closeModal();
  }

  protected closeModal(): void {
    this.activatedModalService.close();
  }
}
