import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';

import {
  SeAlertModule,
  SeButtonModule,
  SeCheckboxModule,
  SeFormControlErrorModule,
  SeInputModule,
  SeModalModule,
} from 'se-ui-components-mf-lib';
import { SendDocumentsButtonComponent } from './send-documents-button.component';
import { ModalSendDocumentationComponent } from './modal-send-documentation/modal-send-documentation.component';

@NgModule({
  declarations: [SendDocumentsButtonComponent, ModalSendDocumentationComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule,
    SeInputModule,
    SeCheckboxModule,
    SeButtonModule,
    SeModalModule,
    SeAlertModule,
    SeFormControlErrorModule
  ],
  exports: [],
  providers: [],
})
export class SendDocumentsButtonModule {}
