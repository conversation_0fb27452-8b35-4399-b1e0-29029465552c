<div class="info-message d-flex m-0 gap-3 p-3" [id]="id">
  <div class="info-message__left-content">
    <div class="info-message__img">
      <img [src]="src" alt="" />
    </div>
  </div>

  <div class="info-message__content">
    <div *ngIf="title" class="title" [innerHTML]="title | translate"></div>
    <ul class="info-message__list" *ngIf="isAList && list.length">
      <li
        *ngFor="let text of list"
        [innerHTML]="text | translate"
        class="text-list"
      ></li>
    </ul>

    <span *ngIf="text && !isAList" [innerHtml]="text">{{ text }}</span>
    <ng-content #footer></ng-content>
  </div>
</div>
