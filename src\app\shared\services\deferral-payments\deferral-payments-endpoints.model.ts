import { AtesaPhoneData, CommonError } from '@app/core/models';
import { VehiclePayment, VehicleSelected } from '@app/shared/models';

export interface CalculateDeferralPaymentRequest {
  idPersTitular: string;
  exercici?: string;
  provisional: boolean;
  vehicles?: VehicleSelected[];
  csv?: string; //pendentNotificacio
  continuarAmbValids?: boolean; //pagamentExecutivaError && pagamentAltresError
  tipusAccess: string;
  nifRepresentant?: string;
  trucadaTelefonica?: AtesaPhoneData;
}

export interface CalculateDeferralPaymentResponse {
  allPendentNotificacio: boolean;
  vehicles: VehiclePayment[];
  total: number;
  pendentNotificacio: boolean;
  importError: boolean;
  pagamentRecarregaError: boolean;
  pagamentExecutivaError: boolean;
  pagamentAltresError: boolean;
  referenciaMui?: string;
  identificadorMui?: string;
  importeMui?: string;
  idGestio?: string;
  urlRetorn?: string;
}

export interface ResumDeferralPaymentResponse {
  idAnnex: string;
  fileNameAnnex: string;
  error: boolean;
  idJustificant: string;
  fileNameJustificant: string;
  errors: CommonError[];
}
