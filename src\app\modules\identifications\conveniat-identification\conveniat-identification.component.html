<div class="app-identificacio">
  <!-- panel de radio buttons para seleccionar en nombre propio o en nombre de tercera persona -->
  <form class="mt-2 d-flex flex-column" [formGroup]="agreedForm">
    <ng-container *ngIf="!showThirdPersonForm">
      <se-panel
        [title]="'SE_PADRO_CO2.MODULE_IDENTIFICATION.QUESTION' | translate"
      >
        <div seHighlightRadioContainer>
          <se-radio
            id="radio-1"
            name="identification"
            [label]="
              'SE_PADRO_CO2.MODULE_IDENTIFICATION.OPTIONS.PERSONAL' | translate
            "
            [value]="IdentificationType.NOM_PROPI"
            formControlName="identificationType"
          >
          </se-radio>
        </div>
        <div seHighlightRadioContainer class="mt-4">
          <se-radio
            id="radio-2"
            name="identification"
            [label]="
              'SE_PADRO_CO2.MODULE_IDENTIFICATION.OPTIONS.THIRD_PERSON'
                | translate
            "
            [value]="IdentificationType.CONVENIAT"
            formControlName="identificationType"
          >
          </se-radio>
        </div>
      </se-panel>
      <div class="d-flex flex-column flex-sm-row justify-content-sm-end mt-4">
        <se-button type="submit" [btnTheme]="'primary'" (click)="continue()">
          {{ 'SE_PADRO_CO2.BUTTONS.ACCESS' | translate }}
        </se-button>
      </div>
    </ng-container>
    <div class="mt-2 d-flex flex-column" *ngIf="showThirdPersonForm">
      <se-panel [title]="'SE_PADRO_CO2.HOW_TO_ACT' | translate">
        <!-- TODO condition ngif when endpoint response -->
        <se-alert
          *ngIf="false"
          [title]="'SE_PADRO_CO2.ERRORS.NIF_NOT_FOUND' | translate"
          [type]="'error'"
          [closeButton]="false"
        >
        </se-alert>

        <p *ngIf="isRepresentative">
          {{
            'SE_PADRO_CO2.MODULE_IDENTIFICATION.TAXPAYER.DESCRIPTION'
              | translate
          }}
        </p>
        <div class="row">
          <div class="col-lg-2 col-md-4 col-12">
            <se-input
              formControlName="plate"
              [label]="'SE_PADRO_CO2.LABELS.PLATE'"
              [disabled]="false"
              [type]="'text'"
              [id]="'plate-id'"
            ></se-input>
          </div>
          <div class="col-lg-2 col-md-4 col-12">
            <se-input
              formControlName="nif"
              [label]="'UI_COMPONENTS.PASSIVE_SUBJECT.NIF'"
              [disabled]="false"
              [type]="'text'"
              [id]="'nif-id'"
            ></se-input>
          </div>
          <div class="col-lg-3 col-md-4 col-12" *ngIf="isRepresentative">
            <se-input
              formControlName="name"
              [label]="'UI_COMPONENTS.PASSIVE_SUBJECT.NAME'"
              [disabled]="false"
              [type]="'text'"
              [id]="'name-id'"
            >
            </se-input>
          </div>
          <div class="col-lg-2 col-md-4 col-12 mt-button-input">
            <se-button
              type="btn"
              [btnTheme]="'primary'"
              [disabled]="!agreedForm.valid"
              (onClick)="confirmIsValid()"
            >
              {{ 'SE_PADRO_CO2.BUTTONS.PROCESS' | translate }}
            </se-button>
          </div>
        </div>
      </se-panel>

      <div
        class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
      >
        <se-button type="submit" [btnTheme]="'secondary'" (click)="goBack()">
          {{ 'SE_PADRO_CO2.BUTTONS.RETURN' | translate }}
        </se-button>
        <se-button
          type="btn"
          [btnTheme]="'primary'"
          [disabled]="!agreedForm.valid || !canContinue"
          (click)="continue()"
        >
          {{ 'UI_COMPONENTS.BUTTONS.CONTINUE' | translate }}
        </se-button>
      </div>
    </div>
  </form>
</div>
