import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SeInputModule } from 'se-ui-components-mf-lib';
import { UserDataLoginSimpleFormComponent } from './user-data-login-simple-form.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [UserDataLoginSimpleFormComponent],
  exports: [UserDataLoginSimpleFormComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SeInputModule,
    ReactiveFormsModule,
    FormsModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class UserDataLoginSimpleFormModule {}
