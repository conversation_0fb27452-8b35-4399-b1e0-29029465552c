import { Injectable } from '@angular/core';
import { race, take } from 'rxjs';

import { AppRoutes, AtesaPhoneData, IdentificationType } from '@core/models';
import { CustomRouterService, StorageService } from '@core/services';
import { CsvModalComponent } from '@shared/components/csv-modal/csv-modal.component';
import { ExecutivaAlertModalComponent } from '@shared/components/executiva-alert-modal/executiva-alert-modal.component';
import {
  SelectedVehiclesReasValidationResponse,
  SelectedVehiclesValidationResponse,
  StartRecursRequest,
  ValidationVehicles,
} from '@shared/services/recurs-validations/recurs-validations-endpoints.model';
import { RecursValidationsEndpointsService } from '@shared/services/recurs-validations/recurs-validations-endpoints.service';
import {
  Nullable,
  SeModalOutputEvents,
  SeModalService,
} from 'se-ui-components-mf-lib';
import {
  RecursReaValidationMethodInputData,
  RecursValidationTramitType,
  ValidationModalInputData,
} from './recurs-validations.model';

@Injectable({
  providedIn: 'root',
})
export class RecursValidationsService {
  private advertimentTerminiAcceptat: Nullable<boolean>;
  private nomesVehiclesValids: Nullable<boolean>;
  private csvNotificacio: Nullable<string>;

  constructor(
    private readonly endpointsService: RecursValidationsEndpointsService,
    private readonly storageService: StorageService,
    private readonly modalService: SeModalService,
    private readonly customRouter: CustomRouterService,
  ) {
    // Intencionadament buit
  }

  public initReas({
    vehiclesInfo,
    advertimentTerminiAcceptat,
    nomesVehiclesValids,
    csvNotificacio,
    skipPreviReasValidation,
  }: RecursReaValidationMethodInputData): void {
    this.storageService.setVehiclesSelected(vehiclesInfo);

    if (!vehiclesInfo) return;

    const request: StartRecursRequest = this.getRequestBody({
      vehiclesInfo,
      advertimentTerminiAcceptat,
      nomesVehiclesValids,
      csvNotificacio,
    });

    this.endpointsService
      .setReas(request)
      .pipe(take(1))
      .subscribe(
        (response: Nullable<SelectedVehiclesReasValidationResponse>) => {
          if (!response) return;

          const { previRea, previRecurs } = response.validation;
          const { matriculas = [] } = vehiclesInfo;
          const nomPropi =
            this.storageService.profileUser === IdentificationType.NOM_PROPI;
          const tramit = RecursValidationTramitType.REAS;

          if (
            this.openCommonModals(
              response.validation,
              matriculas,
              nomesVehiclesValids,
              tramit,
            )
          )
            return;

          if (previRecurs && !nomPropi) {
            this.openPreviRecursModal({ tramit });
            return;
          }

          if (previRea && !nomPropi && !skipPreviReasValidation) {
            this.openPreviReasModal({ tramit });
            return;
          }

          this.goToReasProcess(response);
        },
      );
  }

  public initRecurs({
    vehiclesInfo,
    advertimentTerminiAcceptat,
    nomesVehiclesValids,
    csvNotificacio,
  }: RecursReaValidationMethodInputData): void {
    this.storageService.setVehiclesSelected(vehiclesInfo);
    this.advertimentTerminiAcceptat = advertimentTerminiAcceptat;
    this.nomesVehiclesValids = nomesVehiclesValids;
    this.csvNotificacio = csvNotificacio;

    if (!vehiclesInfo) return;

    const request: StartRecursRequest = this.getRequestBody({
      vehiclesInfo,
      advertimentTerminiAcceptat,
      nomesVehiclesValids,
      csvNotificacio,
    });

    this.endpointsService
      .setRecurs(request)
      .pipe(take(1))
      .subscribe((response: Nullable<SelectedVehiclesValidationResponse>) => {
        if (!response) return;

        const { previRea, previRecurs } = response.validation;
        const { matriculas = [] } = vehiclesInfo;
        const tramit = RecursValidationTramitType.RECURS;

        if (
          this.openCommonModals(
            response.validation,
            matriculas,
            nomesVehiclesValids,
            tramit,
          )
        )
          return;

        if (previRea && previRecurs) {
          this.openPreviReasModal({ tramit, showErrorModal: true });
          return;
        }

        if (previRecurs) {
          this.openPreviRecursModal({ response, tramit });
          return;
        }

        if (previRea) {
          this.openPreviReasModal({ tramit });
          return;
        }

        this.goToRecursProcess(response);
      });
  }

  private openCommonModals(
    validation: ValidationVehicles,
    matriculas: string[],
    nomesVehiclesValids: Nullable<boolean>,
    tramit: RecursValidationTramitType,
  ): boolean {
    const {
      csvNotificacioNecessari,
      vehiclesAdvertirTermini,
      vehiclesForaTermini,
      vehiclesRecurribles,
      allCsvNotificacio,
    } = validation;

    if (csvNotificacioNecessari && !nomesVehiclesValids) {
      this.openCSVModal({
        vehiclesRecurribles,
        multiple: matriculas.length > 1 && !allCsvNotificacio,
        tramit,
      });
      return true;
    }

    if (vehiclesForaTermini && vehiclesRecurribles.length === 0) {
      this.openCannotProcess({ multiple: matriculas.length > 1, tramit });
      return true;
    }

    if (vehiclesForaTermini && vehiclesRecurribles.length > 0) {
      this.openVehiclesValidsAlertModal({ vehiclesRecurribles, tramit });
      return true;
    }

    if (vehiclesAdvertirTermini) {
      this.openTerminiAlertModal({
        multiple: matriculas.length > 1,
        tramit,
      });
      return true;
    }

    return false;
  }

  private getRequestBody({
    vehiclesInfo,
    advertimentTerminiAcceptat,
    nomesVehiclesValids,
    csvNotificacio,
  }: RecursReaValidationMethodInputData): StartRecursRequest {
    const trucadaTelefonica = this.getTrucadaTelefonica();
    return {
      idPerssCens: vehiclesInfo?.idPersTitular || '',
      exercici: Number(vehiclesInfo?.exercici),
      matricules: vehiclesInfo?.matriculas || [],
      tipusAcces: vehiclesInfo?.tipusAccess as IdentificationType,
      advertimentTerminiAcceptat: !!advertimentTerminiAcceptat,
      nomesVehiclesValids: !!nomesVehiclesValids,
      csvNotificacio,
      ...(trucadaTelefonica ? { trucadaTelefonica } : {}),
      nifRepresentant: this.storageService.civilServantVehicleNifRepresentant,
    };
  }

  private getTrucadaTelefonica(): Nullable<AtesaPhoneData> {
    const numeroTelefon = this.storageService.civilServantVehiclePhone;
    const data = this.storageService.civilServantVehicleDate;
    const hora = this.storageService.civilServantVehicleHour;

    return numeroTelefon && data && hora
      ? { numeroTelefon, data, hora }
      : undefined;
  }

  private openCSVModal({
    vehiclesRecurribles,
    multiple,
    tramit,
  }: ValidationModalInputData): void {
    const modalRef = this.modalService.openModal({
      severity: 'info',
      size: 'xl',
      title:
        `SE_PADRO_CO2.${tramit}_PROCESS.VALIDATIONS_MODALS.MODAL_CSV.` +
        (multiple ? 'MULTIPLE' : 'SINGLE') +
        '.TITLE',
      closable: true,
      closableDisabled: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
      component: CsvModalComponent,
    });

    modalRef.componentInstance.multiple = multiple;

    modalRef.componentInstance.continueOutput
      .pipe(take(1))
      .subscribe((csvNotificacio: string) => {
        this.csvNotificacio = csvNotificacio;

        const vehiclesInfo = this.storageService.getVehiclesSelected()!;
        this.nomesVehiclesValids =
          multiple &&
          this.csvNotificacio === '' &&
          (vehiclesRecurribles || []).length > 0;

        if (this.nomesVehiclesValids) {
          vehiclesInfo.matriculas = vehiclesRecurribles?.map(
            ({ matricula }) => matricula,
          );
        }

        const request: RecursReaValidationMethodInputData = {
          vehiclesInfo,
          advertimentTerminiAcceptat: this.advertimentTerminiAcceptat,
          nomesVehiclesValids: this.nomesVehiclesValids,
          csvNotificacio: this.csvNotificacio,
        };

        if (tramit === RecursValidationTramitType.RECURS) {
          this.initRecurs(request);
        } else {
          this.initReas(request);
        }

        modalRef.close();
      });
  }

  private openCannotProcess({
    multiple,
    tramit,
  }: ValidationModalInputData): void {
    const modalRef = this.modalService.openModal({
      severity: 'error',
      title: `SE_PADRO_CO2.${tramit}_PROCESS.VALIDATIONS_MODALS.MODAL_CANNOT_PROCESS.TITLE`,
      subtitle:
        `SE_PADRO_CO2.${tramit}_PROCESS.VALIDATIONS_MODALS.MODAL_CANNOT_PROCESS.` +
        (multiple ? 'MULTIPLE' : 'SINGLE') +
        '.INFO',
      closable: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.CLOSE',
    });

    race([
      modalRef.componentInstance.modalOutputEvent,
      modalRef.componentInstance.modalSecondaryButtonEvent,
    ]).subscribe(() => {
      modalRef.close();
    });
  }

  private openVehiclesValidsAlertModal({
    vehiclesRecurribles,
    tramit,
  }: ValidationModalInputData): void {
    const closableLabel = 'SE_PADRO_CO2.BUTTONS.CONTINUE';
    const title = `SE_PADRO_CO2.${tramit}_PROCESS.VALIDATIONS_MODALS.MODAL_VEHICLES_FORA_TERMINI.TITLE`;
    const message = `SE_PADRO_CO2.${tramit}_PROCESS.VALIDATIONS_MODALS.MODAL_VEHICLES_FORA_TERMINI.MESSAGE`;

    const modalRef = this.modalService.openModal({
      severity: 'warning',
      size: 'xl',
      title,
      closable: (vehiclesRecurribles || []).length > 0,
      closableLabel,
      secondaryButton: true,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
      component: ExecutivaAlertModalComponent,
    });

    modalRef.componentInstance.vehicles = vehiclesRecurribles;
    modalRef.componentInstance.message = message;
    modalRef.componentInstance.tramitProcess = tramit;

    modalRef.componentInstance.continueOutput.pipe(take(1)).subscribe(() => {
      const vehiclesInfo = this.storageService.getVehiclesSelected()!;
      vehiclesInfo.matriculas = vehiclesRecurribles?.map(
        ({ matricula }) => matricula,
      );

      this.nomesVehiclesValids = true;

      const request: RecursReaValidationMethodInputData = {
        vehiclesInfo,
        advertimentTerminiAcceptat: this.advertimentTerminiAcceptat,
        nomesVehiclesValids: this.nomesVehiclesValids,
        csvNotificacio: this.csvNotificacio,
      };

      if (tramit === RecursValidationTramitType.RECURS) {
        this.initRecurs(request);
      } else {
        this.initReas(request);
      }
    });
  }

  private openTerminiAlertModal({
    multiple,
    tramit,
  }: ValidationModalInputData): void {
    const modalRef = this.modalService.openModal({
      severity: 'warning',
      title: `SE_PADRO_CO2.${tramit}_PROCESS.VALIDATIONS_MODALS.MODAL_FORA_TERMINI.TITLE`,
      subtitle:
        `SE_PADRO_CO2.${tramit}_PROCESS.VALIDATIONS_MODALS.MODAL_FORA_TERMINI.` +
        (multiple ? 'MULTIPLE' : 'SINGLE') +
        '.INFO',
      closable: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
    });

    modalRef.componentInstance.modalOutputEvent
      .pipe(take(1))
      .subscribe((data: SeModalOutputEvents) => {
        if (data === SeModalOutputEvents.MAIN_ACTION) {
          const vehiclesInfo = this.storageService.getVehiclesSelected();

          this.advertimentTerminiAcceptat = true;

          const request: RecursReaValidationMethodInputData = {
            vehiclesInfo,
            advertimentTerminiAcceptat: this.advertimentTerminiAcceptat,
            nomesVehiclesValids: this.nomesVehiclesValids,
            csvNotificacio: this.csvNotificacio,
          };

          if (tramit === RecursValidationTramitType.RECURS) {
            this.initRecurs(request);
          } else {
            this.initReas(request);
          }
        }

        modalRef.close();
      });
    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(take(1))
      .subscribe(() => {
        modalRef.close();
      });
  }

  private openPreviRecursModal({
    response,
    tramit,
  }: ValidationModalInputData): void {
    const nomPropi =
      this.storageService.profileUser === IdentificationType.NOM_PROPI;
    const nomPropiKey = nomPropi ? 'NOM_PROPI' : 'NOT_NOM_PROPI.' + tramit;
    const translationKey =
      'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_PREVI_RECURS';
    const modalRef = this.modalService.openModal({
      severity:
        nomPropi || tramit === RecursValidationTramitType.RECURS
          ? 'warning'
          : 'error',
      title: `${translationKey}.${nomPropiKey}.TITLE`,
      subtitle: `${translationKey}.${nomPropiKey}.MESSAGE`,
      closable: true,
      closableLabel:
        nomPropi || tramit === RecursValidationTramitType.RECURS
          ? 'SE_PADRO_CO2.BUTTONS.CONTINUE'
          : 'SE_PADRO_CO2.BUTTONS.CLOSE',
      secondaryButton: nomPropi || tramit === RecursValidationTramitType.RECURS,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
    });

    modalRef.componentInstance.modalOutputEvent
      .pipe(take(1))
      .subscribe((data: SeModalOutputEvents) => {
        if (
          data === SeModalOutputEvents.MAIN_ACTION &&
          (nomPropi || tramit === RecursValidationTramitType.RECURS)
        ) {
          this.goToRecursProcess(response!, false);
        }

        modalRef.close();
      });
    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(take(1))
      .subscribe(() => {
        modalRef.close();
      });
  }

  private openPreviReasModal({
    tramit,
    showErrorModal = false,
  }: ValidationModalInputData): void {
    const nomPropi =
      this.storageService.profileUser === IdentificationType.NOM_PROPI;
    const nomPropiKey =
      nomPropi && !showErrorModal ? 'NOM_PROPI' : 'NOT_NOM_PROPI.' + tramit;
    const translationKey =
      'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_PREVI_REAS';
    const showWarningModal =
      (nomPropi || tramit === RecursValidationTramitType.REAS) &&
      !showErrorModal;

    const modalRef = this.modalService.openModal({
      severity: showWarningModal ? 'warning' : 'error',
      title: `${translationKey}.${nomPropiKey}.TITLE`,
      subtitle: `${translationKey}.${nomPropiKey}.MESSAGE`,
      closable: true,
      closableLabel: showWarningModal
        ? 'SE_PADRO_CO2.BUTTONS.SUBMIT_REAS'
        : 'SE_PADRO_CO2.BUTTONS.CLOSE',
      secondaryButton: showWarningModal,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
    });

    modalRef.componentInstance.modalOutputEvent
      .pipe(take(1))
      .subscribe((data: SeModalOutputEvents) => {
        if (data === SeModalOutputEvents.MAIN_ACTION && showWarningModal) {
          const vehiclesInfo = this.storageService.getVehiclesSelected();

          this.initReas({
            vehiclesInfo,
            advertimentTerminiAcceptat: this.advertimentTerminiAcceptat,
            nomesVehiclesValids: this.nomesVehiclesValids,
            csvNotificacio: this.csvNotificacio,
            skipPreviReasValidation: true,
          });
        }

        modalRef.close();
      });
    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(take(1))
      .subscribe(() => {
        modalRef.close();
      });
  }

  private goToRecursProcess(
    response: SelectedVehiclesValidationResponse,
    showReasAlert: boolean = true,
  ): void {
    response.showReasAlert = showReasAlert;
    response.advertimentTerminiAcceptat = this.advertimentTerminiAcceptat;
    response.nomesVehiclesValids = this.nomesVehiclesValids;
    response.csvNotificacio = this.csvNotificacio;

    this.storageService.setRecursVehicles(response);
    this.storageService.setIdTramit(response.recursId);
    this.customRouter.navigateByBaseUrl(AppRoutes.APPEAL_FOR_RECONSIDERATION);
  }

  private goToReasProcess(
    response: SelectedVehiclesReasValidationResponse,
  ): void {
    this.storageService.setReasVehicles(response);
    this.storageService.setIdTramit(response.id);
    this.customRouter.navigateByBaseUrl(AppRoutes.REAS);
  }
}
