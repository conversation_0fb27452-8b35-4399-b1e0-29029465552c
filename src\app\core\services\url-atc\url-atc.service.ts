import { Injectable, isDevMode } from '@angular/core';
import { BASE_URL, BASE_URL_EL_MEU_ESPAI } from '@app/core/models';

import { TranslateService } from '@ngx-translate/core';
import { SeMfConfigurationService } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class BaseUrlService {
  private _BASE_ATC_URL = 'https://atc.gencat.cat/';

  baseUrl: string = '';

  constructor(
    private translateService: TranslateService,
    private readonly mfConfigurationService: SeMfConfigurationService,
  ) {
    if (!isDevMode()) {
      this.baseUrl = `/${this.translateService.currentLang}`;
    }
    // Mantenemos el /tracking en la URL
    if (window.location.href.includes('/tracking')) this.baseUrl = '/tracking';
  }

  /**
   * Devuelve 'https://atc.gencat.cat/' + 'ca' o 'es'
   */
  get BASE_ATC_URL(): string {
    return this._BASE_ATC_URL + `${this.translateService.currentLang}`;
  }

  goToElMeuEspai(section: string): void {
    if (
      this.mfConfigurationService.getIsMobileApp() &&
      section.includes(BASE_URL)
    ) {
      window.location.href = `${this.baseUrl}/secured/${section}`;
      return;
    }
    window.location.href = `${this.baseUrl}/${BASE_URL_EL_MEU_ESPAI}/${section}`;
  }
}
