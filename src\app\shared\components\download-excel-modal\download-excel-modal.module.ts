import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SeCheckboxModule, SeModalModule } from 'se-ui-components-mf-lib';
import { DownloadExcelModalComponent } from './download-excel-modal.component';

@NgModule({
  declarations: [DownloadExcelModalComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    SeModalModule,
    SeCheckboxModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class DownloadExcelModalModule {}
