import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { MenuItem } from 'primeng/api';
import { PaginatorState } from 'primeng/paginator';
import { Subject, take, takeUntil } from 'rxjs';

import { DownloadExcelModalComponent } from '@app/shared/components/download-excel-modal/download-excel-modal.component';
import { AppRoutes, IdentificationType, TABLE_SORT_NAMES } from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SituationsStoreService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { VechilesRowValue } from '@shared/components';
import {
  ListPadroRequest,
  ListPadroResponse,
  PadroItem,
  SortOrder,
  VehicleSelected,
  VehiclesSelectedInfo,
  YesOrNoOptionsEnum,
} from '@shared/models';
import {
  PadroListEndPointService,
  PaginationService,
  PaymentsService,
} from '@shared/services';
import {
  Column,
  Row,
  SeButton,
  SeDropdownOption,
  SeModalService,
  SortParams,
  TableSortOrder,
  SeDeviceService,
} from 'se-ui-components-mf-lib';
import { SeCheckbox } from 'se-ui-components-mf-lib/lib/components/checkbox/checkbox.model';
import { ReceiptsUtilsService } from '../receipts-utils.service';
import { PreviousExercisesFiltersModalComponent } from './previous-exercises-filters-modal.component';
import { PreviousExercisesService } from './previous-exercises.service';

@Component({
  selector: 'app-previous-exercises-table',
  templateUrl: './previous-exercises.component.html',
  styleUrls: ['./previous-exercises.component.scss'],
})
export class PreviousExercisesTableComponent implements OnInit, OnDestroy {
  tableColumns: Column[] = [];
  MIN_ELEMENTS_TO_SHOW_FILTERS: number = 6;
  MIN_ELEMENTS_TO_SHOW_PLATE_FILTER: number = 1;
  MIN_ELEMENTS_TO_SHOW_FILTER_BUTTONS: number = 0;

  filterForm: FormGroup = new FormGroup({});

  protected downloadButton = this.receiptsUtilsService.getDownloadButton();
  protected rangeFilter = this.receiptsUtilsService.getRangeFilterOptions();
  protected applyButton = this.receiptsUtilsService.getApplyButton();
  protected resetButton = this.receiptsUtilsService.getResetButton();

  protected otherActionDropdownButtonOptions: SeButton =
    this.receiptsUtilsService.getOtherDropdownOptions();
  protected dropdownButtonAnotherActions: MenuItem[] = [];

  yearOptions: SeDropdownOption[] = [];

  selectedRows: Row[] = [];

  exercise: string = this.specificConfigurationService.currentExercise;

  yesOrNoOptions: SeCheckbox[] = this.receiptsUtilsService.getYesOrNoOptions();

  private _tableRows: Row[] = [];

  set tableRows(data: PadroItem[]) {
    this.setListPadroData(data);
  }

  get tableRows(): Row[] {
    return this._tableRows;
  }

  get isCoordinador(): boolean {
    return this.store.profileUser === IdentificationType.COORDINADOR;
  }

  get canSearchByPlate(): boolean {
    return this.showPlateFilter && !this.loginWithPlate;
  }

  get loginWithPlate(): boolean {
    const user = this.loginService.user;
    return !!user?.matricula;
  }

  get showFilters(): boolean {
    return (
      this.totalRecords > this.MIN_ELEMENTS_TO_SHOW_FILTERS ||
      this.isFilteredByLowBar
    );
  }

  get showTable(): boolean {
    return (
      this.totalRecords > this.MIN_ELEMENTS_TO_SHOW_FILTER_BUTTONS ||
      this.filterForm.get('year')?.value ||
      (!this.filterForm.get('year')?.value &&
        (this.isFilteredByPlate || this.isFilteredByLowBar))
    );
  }

  get showPlateFilter(): boolean {
    return (
      !this.deviceService.isMobile() &&
      (this.totalRecords > this.MIN_ELEMENTS_TO_SHOW_PLATE_FILTER ||
        this.isFilteredByPlate ||
        this.isFilteredByLowBar)
    );
  }

  isFilteredByPlate: boolean = false;
  isFilteredByLowBar: boolean = false;

  totalRecords: number = 0;
  protected itemsPerPage = this.paginationSrv.getItemsPerPage();
  protected rowsPerPageOptions = this.paginationSrv.getRowsPerPageOptions();
  currentPage: number = 0;
  sortField: TABLE_SORT_NAMES | null = null;
  sortOrder: SortOrder = SortOrder.NONE;

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private listPadroEndpointService: PadroListEndPointService,
    private receiptsUtilsService: ReceiptsUtilsService,
    private situationsStoreService: SituationsStoreService,
    private loginService: LoginResponseService,
    private paymentService: PaymentsService,
    private customRouter: CustomRouterService,
    private store: StorageService,
    private previousExercisesService: PreviousExercisesService,
    public specificConfigurationService: SpecificConfigurationService,
    private seModalService: SeModalService,
    private paginationSrv: PaginationService,
    private readonly deviceService: SeDeviceService,
  ) {
    // Intencionadamente vacío
  }

  ngOnInit(): void {
    this.setTableColumns();
    this.setUpForm();
    this.getPadroDataOnInit();
    this.setYearOptions();
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private setUpForm(): void {
    this.filterForm = this.receiptsUtilsService.getCommonFormGroup();
    this.filterForm.addControl('situation', new FormControl(''));
    this.filterForm.addControl('year', new FormControl(''));
  }

  private setTableColumns(): void {
    this.tableColumns =
      this.receiptsUtilsService.getPreviousExercisesTableColumns();
  }

  setYearOptions(): void {
    this.yearOptions =
      (this.specificConfigurationService?.llistaExercicis.map(
        (year: string) => {
          return { label: year, id: year } as SeDropdownOption;
        },
      ) as SeDropdownOption[]) ?? [];
  }

  handlePageChange(event: PaginatorState): void {
    this.itemsPerPage = event.rows as number;
    const currentPage = event.page as number;

    this.getPadroData(currentPage);
  }

  getPadroDataOnInit(): void {
    const isLoginSimple =
      this.store.profileUser === IdentificationType.LOGIN_SIMPLE;
    const isConveniat = this.store.profileUser === IdentificationType.CONVENIAT;

    if (isLoginSimple || isConveniat) {
      this.getPadroData();
    } else {
      this.listPadroEndpointService
        .getListPadro(this.getPadronListRequest(true))
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((data: ListPadroResponse) => {
          if (data.content && data.content.total > 100) {
            this.filterForm
              .get('year')
              ?.setValue(this.yearOptions[this.yearOptions.length - 1].id);
          }
          this.getPadroData();
        });
    }
  }

  getPadroData(currentPage: number = 0): void {
    this.currentPage = currentPage;

    this.listPadroEndpointService
      .getListPadro(this.getPadronListRequest(false, currentPage))
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((data: ListPadroResponse) => {
        this.isFiltered();
        this.tableRows = data?.content?.results || [];
        this.totalRecords = data?.content?.total || 0;
      });
  }

  private isFiltered(): void {
    this.isFilteredByLowBar = Object.entries(this.filterForm.value)
      .filter((entry) => entry[0] !== 'plate' && entry[0] !== 'year')
      .map((entry) => entry[1])
      .some((value) => {
        if (typeof value === 'object' && value) {
          return Object.values(value).some((value) => value);
        }
        return value;
      });
    this.isFilteredByPlate = !!this.filterForm.get('plate')?.value;
  }

  onSelectionChange(rows: Row[]): void {
    this.selectedRows = rows;
    const { button, actions } =
      this.previousExercisesService.updateOtherActionsDropdownButtonConfig(
        this.otherActionDropdownButtonOptions,
        this.selectedRows,
      );
    this.otherActionDropdownButtonOptions = button;
    this.dropdownButtonAnotherActions = actions;
  }

  // onPagamentSelectedPlates(): void {
  //   const plates: string[] = this.selectedRows.map(
  //     (row: Row) => row.data['plate']?.value,
  //   );
  //   const exercici = this.selectedRows[0]['data']['exercise']?.value;
  //   this.goToPagament(plates, exercici);
  // }

  // private goToPagament(plates: string[], exercici: string): void {
  //   const vehiclesInfo: VehiclesSelectedInfo =
  //     this.receiptsUtilsService.getVehiclesSelectedInfoByPlates(
  //       plates,
  //       false,
  //       exercici,
  //     );
  //   this.paymentService.initPayment(vehiclesInfo);
  // }

  onPagamentSelectedPlates(): void {
    const vehicles = this.receiptsUtilsService.getVehiclesSelectedByRows(
      this.selectedRows,
    );
    this.goToPagament(vehicles);
  }

  private goToPagament(vehicles: VehicleSelected[]): void {
    const vehiclesInfo: VehiclesSelectedInfo =
      this.receiptsUtilsService.getVehiclesSelectedInfoByVehicles(
        vehicles,
        false,
        vehicles[0].exercici,
      );
    this.paymentService.initPayment(vehiclesInfo);
  }

  goToDetail(row: VechilesRowValue): void {
    const { situation, exercise, plate, codiSituacio } = row;
    this.customRouter.navigateByBaseUrl(AppRoutes.DETAIL, {
      queryParams: {
        idPersTitular: this.loginService.user.idPersTitular,
        matricula: plate.value,
        exercici: exercise.value,
        tipusAccess: this.loginService.user.tipusAccess,
        provisional: false,
        situacio: situation.value,
        codiSituacio: codiSituacio.value,
      },
    });
  }

  sortByColumn(sort: SortParams): void {
    if (sort.sortOrder === TableSortOrder.ASC) this.sortOrder = SortOrder.ASC;
    if (sort.sortOrder === TableSortOrder.DESC) this.sortOrder = SortOrder.DESC;
    if (
      sort.sortOrder !== TableSortOrder.ASC &&
      sort.sortOrder !== TableSortOrder.DESC
    ) {
      this.sortOrder = SortOrder.NONE;
    }
    this.sortField =
      TABLE_SORT_NAMES[sort.columnKey as keyof typeof TABLE_SORT_NAMES];
    if (this.sortField === TABLE_SORT_NAMES.domiciled) {
      this.sortField = TABLE_SORT_NAMES.definitiveDebt;
    }
    if (this.sortField === TABLE_SORT_NAMES.situation) {
      this.sortField = TABLE_SORT_NAMES.definitiveSituation;
    }
    if (this.sortField === TABLE_SORT_NAMES.shares) {
      this.sortField = TABLE_SORT_NAMES.definitiveShares;
    }
    this.getPadroData();
  }

  setListPadroData(data: PadroItem[]): void {
    if (data === undefined) return;

    this.situationsStoreService.situations$
      .pipe(take(1))
      .subscribe((situations) => {
        this._tableRows = this.previousExercisesService.getTableRowPadroData(
          data,
          situations || [],
          this.goToDetail.bind(this),
          this.setRequestToGoPayment.bind(this),
        );
      });
  }

  private setRequestToGoPayment = (row: VechilesRowValue): void => {
    const vehicle: VehicleSelected = {
      matricula: row.plate.value?.toString() ?? '',
      exercici: row.exercise.value?.toString() ?? '',
      idDeute: row.idDeute.value?.toString() ?? '',
    };
    this.goToPagament([vehicle]);
  };

  protected openModalFilter(): void {
    const modalRef = this.seModalService.openModal({
      title: 'SE_PADRO_CO2.MODAL.FILTER.TITLE',
      severity: 'info',
      closable: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.ACCEPT',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CLOSE',
      keyboard: true,
      hideIcon: true,
      size: 'xl',
      component: PreviousExercisesFiltersModalComponent,
    });

    modalRef.componentInstance.filterForm = this.filterForm;

    modalRef.componentInstance.handleApplyFilter
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.getPadroData();
      });
  }

  private getAccessType(): IdentificationType {
    return this.loginService.user.tipusAccess || IdentificationType.NOM_PROPI;
  }

  private getPadronListRequest(
    soloTotal: boolean = false,
    currentPage: number = 0,
  ): ListPadroRequest {
    const years: string =
      this.specificConfigurationService?.llistaExercicis?.join(',') as string;
    const user = this.loginService.user;
    const { rangeFilter, domiciled, plate, situation, year } =
      this.filterForm.value;
    const plateValue =
      plate?.toUpperCase() ||
      this.store.licensePlate?.toUpperCase() ||
      user?.matricula?.toUpperCase();
    return {
      filter: {
        tipusAccess: this.getAccessType(),
        nifTitular: null,
        idPersTitular: user.idPersTitular as string,
        quotaDes: rangeFilter?.from || -1,
        quotaFins: rangeFilter?.to || -1,
        nou: null,
        domicilat: this.mapDomiciled(domiciled),
        listMatriculas: plateValue ? [plateValue] : null,
        situacio: situation || null,
        exercici: year || years || null,
        provisional: false,
        soloTotal,
      },
      options: {
        first: currentPage + 1,
        rows: this.itemsPerPage,
        sortField: this.sortField,
        sortOrder: this.sortOrder,
      },
    };
  }

  private mapDomiciled(value: string[]): boolean | null {
    return value
      ? value.length === 1
        ? value[0] === YesOrNoOptionsEnum.yes
          ? true
          : false
        : null
      : null;
  }

  onRowClick(event: VechilesRowValue): void {
    this.goToDetail(event);
  }

  protected openDownloadExcelModal(): NgbModalRef {
    const modalRef = this.seModalService.openModal({
      title: 'SE_PADRO_CO2.RECEIPTS.EXCEL.MODAL_TITLE',
      severity: 'info',
      closable: true,
      closableLabel: 'SE_PADRO_CO2.RECEIPTS.EXCEL.DONWLOAD_EXCEL',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
      keyboard: true,
      hideIcon: false,
      size: 'xl',
      component: DownloadExcelModalComponent,
    });
    modalRef.componentInstance.request = this.getPadronListRequest();
    modalRef.componentInstance.request.options = {
      ...modalRef.componentInstance.request.options,
      first: 0, // Da igual la página, queremos todos los registros
      rows: this.totalRecords, // Establecemos el número total de registros para que los descargue todos
    };
    modalRef.componentInstance.selectedPlates = this.selectedRows.map(
      (row: Row) => row.data['plate']?.value,
    );
    return modalRef;
  }
}
