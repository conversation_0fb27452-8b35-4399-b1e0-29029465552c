import { Injectable } from '@angular/core';
import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { TranslateModule } from '@ngx-translate/core';

import { IdentificationType } from '@app/core/models';
import {
  HeaderInfoService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@app/core/services';
import { RecursValidationsService } from '@app/shared/services/recurs-validations';
import { ButtonComponent, SeButtonModule } from 'se-ui-components-mf-lib';
import { RecursosIReclamacionsComponent } from './recursos-i-reclamacions.component';
import type { VehiclesSelectedInfo } from '@app/shared/models';
import { HelpModalOptionEnum } from '@app/shared/components';

@Injectable({ providedIn: 'root' })
class SpecificConfigurationMockService {
  isProvisional = false;
  currentExercise = '2024';
}

@Injectable({ providedIn: 'root' })
class StorageMockService {
  profileUser = IdentificationType.NOM_PROPI;
}

@Injectable({ providedIn: 'root' })
class LoginResponseMockService {
  user = {
    idPersTitular: 'id-pers-titular-test',
    tipusAccess: IdentificationType.NOM_PROPI,
  };
}

describe('RecursosIReclamacionsComponent', () => {
  let specificConfigurationMockService: SpecificConfigurationMockService;
  let storageMockService: StorageService;
  let loginResponseMockService: LoginResponseService;
  let recursValidationsServiceSpy: jasmine.SpyObj<RecursValidationsService>;
  let headerInfoServiceSpy: jasmine.SpyObj<HeaderInfoService>;

  function setup(): {
    fixture: ComponentFixture<RecursosIReclamacionsComponent>;
    component: RecursosIReclamacionsComponent;
  } {
    const fixture = TestBed.createComponent(RecursosIReclamacionsComponent);
    return { fixture, component: fixture.componentInstance };
  }

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [RecursosIReclamacionsComponent],
      imports: [SeButtonModule, TranslateModule.forRoot()],
      providers: [
        {
          provide: SpecificConfigurationService,
          useValue: SpecificConfigurationMockService,
        },
        { provide: StorageService, useClass: StorageMockService },
        { provide: LoginResponseService, useClass: LoginResponseMockService },
        {
          provide: RecursValidationsService,
          useValue: jasmine.createSpyObj('RecursValidationsService', [
            'initRecurs',
          ]),
        },
        {
          provide: HeaderInfoService,
          useValue: jasmine.createSpyObj('HeaderInfoService', [
            'showHelpModal',
          ]),
        },
        {
          provide: StorageService,
          useValue: jasmine.createSpyObj('StorageService', [
            'setReasonsSelected',
          ]),
        },
      ],
    }).compileComponents();

    specificConfigurationMockService = TestBed.inject(
      SpecificConfigurationService,
    );
    storageMockService = TestBed.inject(StorageService);
    loginResponseMockService = TestBed.inject(LoginResponseService);
    recursValidationsServiceSpy = TestBed.inject(
      RecursValidationsService,
    ) as jasmine.SpyObj<RecursValidationsService>;
    headerInfoServiceSpy = TestBed.inject(
      HeaderInfoService,
    ) as jasmine.SpyObj<HeaderInfoService>;
  }));

  it("s'hauria de crear", () => {
    const { fixture, component } = setup();

    fixture.detectChanges();

    expect(component).toBeTruthy();
  });

  it("s'hauria d'amagar si el període del padró és provisional", () => {
    specificConfigurationMockService.isProvisional = true;
    const { fixture } = setup();

    fixture.detectChanges();

    expect(fixture.debugElement.children.length).toBe(0);
  });

  it("s'hauria d'amagar si el rebut del vehicle no pertany a l'exercici en curs", () => {
    specificConfigurationMockService.isProvisional = false;
    specificConfigurationMockService.currentExercise = '2024';
    const { fixture } = setup();

    fixture.componentRef.setInput('vehicleDetail', { exercici: '2023' });
    fixture.detectChanges();

    expect(fixture.debugElement.children.length).toBe(0);
  });

  it("s'hauria d'amagar si el perfil de l'usuari és login simple", () => {
    storageMockService.profileUser = IdentificationType.LOGIN_SIMPLE;
    specificConfigurationMockService.isProvisional = false;
    specificConfigurationMockService.currentExercise = '2024';
    const { fixture } = setup();

    fixture.componentRef.setInput('vehicleDetail', { exercici: '2024' });
    fixture.detectChanges();

    expect(fixture.debugElement.children.length).toBe(0);
  });

  it("s'hauria d'amagar si el perfil de l'usuari és coordinador", () => {
    storageMockService.profileUser = IdentificationType.COORDINADOR;
    specificConfigurationMockService.isProvisional = false;
    specificConfigurationMockService.currentExercise = '2024';
    const { fixture } = setup();

    fixture.componentRef.setInput('vehicleDetail', { exercici: '2024' });
    fixture.detectChanges();

    expect(fixture.debugElement.children.length).toBe(0);
  });

  it("s'hauria de mostrar si el període del padró és definitiu i el rebut del vehicle pertany a l'exercici en curs", () => {
    storageMockService.profileUser = IdentificationType.REPRESENTATIVE;
    specificConfigurationMockService.isProvisional = false;
    specificConfigurationMockService.currentExercise = '2024';
    const { fixture } = setup();

    fixture.componentRef.setInput('vehicleDetail', { exercici: '2024' });
    fixture.detectChanges();

    const p = fixture.debugElement.query(By.css('p')).nativeElement;
    expect(p.textContent).toContain(
      'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.QUESTION',
    );

    // TODO: descomentar botó de presentar REA quan estigui implementat
    const [helpButton, presentarRecursButton /* presentarReaButton */] =
      fixture.debugElement
        .queryAll(By.directive(ButtonComponent))
        .map(({ nativeElement }) => nativeElement);

    expect(helpButton.textContent).toContain(
      'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.HELP_BUTTON',
    );
    expect(presentarRecursButton.textContent).toContain(
      'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.PRESENTAR_RECURS_LINK',
    );
    // TODO: descomentar quan es pugui presentar REA
    // expect(presentarReaButton.textContent).toContain(
    //   'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.PRESENTAR_REA_LINK',
    // );
  });

  // TODO: afegir test quan REA estigui implementat
  xit("hauria d'amagar el botó de presentar REA si el perfil de l'usuari és nom propi", () => {
    specificConfigurationMockService.isProvisional = false;
    specificConfigurationMockService.currentExercise = '2024';
    storageMockService.profileUser = IdentificationType.NOM_PROPI;
    const { fixture } = setup();

    fixture.componentRef.setInput('vehicleDetail', { exercici: '2024' });
    fixture.detectChanges();

    const buttons = fixture.debugElement
      .queryAll(By.directive(ButtonComponent))
      .map(({ nativeElement }) => nativeElement);

    expect(buttons.length).toBe(2);
    expect(buttons[0].textContent).toContain(
      'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.HELP_BUTTON',
    );
    expect(buttons[1].textContent).toContain(
      'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.PRESENTAR_RECURS_LINK',
    );
  });

  it("hauria de mostrar el modal d'ajuda en prémer el botó d'ajuda ", () => {
    storageMockService.profileUser = IdentificationType.NOM_PROPI;
    specificConfigurationMockService.isProvisional = false;
    specificConfigurationMockService.currentExercise = '2024';
    const { fixture } = setup();

    fixture.componentRef.setInput('vehicleDetail', { exercici: '2024' });
    fixture.detectChanges();

    const helpButton = fixture.debugElement.query(
      By.css('se-button:nth-of-type(1)'),
    );

    helpButton.triggerEventHandler('onClick', {});
    expect(headerInfoServiceSpy.showHelpModal).toHaveBeenCalledOnceWith(
      HelpModalOptionEnum.RECURS_Y_REA,
    );
  });

  it("hauria d'iniciar el procés de recurs en prémer el botó de presentar recurs", () => {
    storageMockService.profileUser = IdentificationType.REPRESENTATIVE;
    loginResponseMockService.user.tipusAccess =
      IdentificationType.REPRESENTATIVE;
    specificConfigurationMockService.isProvisional = false;
    specificConfigurationMockService.currentExercise = '2024';
    const vehicleDetail = {
      matricula: 'matricula-test',
      exercici: '2024',
      nifTitular: 'nif-titular-test',
    };
    const expectedVehiclesSelectedInfo = {
      provisional: false,
      matriculas: [vehicleDetail.matricula],
      exercici: vehicleDetail.exercici,
      nifTitular: vehicleDetail.nifTitular,
      idPersTitular: loginResponseMockService.user.idPersTitular,
      tipusAccess: loginResponseMockService.user.tipusAccess,
    };
    const { fixture } = setup();

    fixture.componentRef.setInput('vehicleDetail', vehicleDetail);
    fixture.detectChanges();

    const presentarRecursButtonDE = fixture.debugElement.query(
      By.css('se-button:nth-of-type(2)'),
    );

    presentarRecursButtonDE.triggerEventHandler('onClick', {});

    expect(recursValidationsServiceSpy.initRecurs).toHaveBeenCalledOnceWith({
      vehiclesInfo: expectedVehiclesSelectedInfo as VehiclesSelectedInfo,
    });
  });
});
