import { Injectable } from '@angular/core';
import { map, Observable } from 'rxjs';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';
import { CheckCSVResponse } from './csv-endpoint.model';

@Injectable({
  providedIn: 'root',
})
export class CsvEndpointService {
  constructor(private httpService: SeHttpService) {}

  checkCSV(csv: string, idTributari: string): Observable<boolean> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/exists`,
      method: 'post',
      body: {
        csv,
        idTributari,
      },
    };

    return this.httpService
      .post<SeHttpResponse<CheckCSVResponse>>(httpRequest)
      .pipe(
        map((response: SeHttpResponse<CheckCSVResponse>) => {
          return !!response?.content;
        }),
      );
  }
}
