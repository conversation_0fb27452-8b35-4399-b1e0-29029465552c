import { Component, Input, OnInit } from '@angular/core';
import { AppRoutes } from '@app/core/models';
import {
  CustomRouterService,
  SpecificConfigurationService,
} from '@app/core/services';
import { FeatureFlagService } from '@app/core/services/feature-flag';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { SeModal, SeModalOutputEvents } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-payment-types',
  templateUrl: './payment-types-modal.component.html',
})
export class PaymentTypesModalComponent implements OnInit {
  @Input() data: SeModal | undefined;

  day: string = '';

  month: string = '';

  paymentStartDay: string = '';
  paymentStartMonth: string = '';
  paymentStartYear: string = '';

  paymentEndDay: string = '';
  paymentEndMonth: string = '';
  paymentEndYear: string = '';

  modifyAccountEndDay: string = '';
  modifyAccountEndMonth: string = '';
  modifyAccountEndYear: string = '';

  subtitle: string = '';
  domiciledTitle: string = '';
  domiciledSubtitle: string = '';
  paymentTitle: string = '';
  paymentSubtitle: string = '';

  constructor(
    private activatedModalService: NgbActiveModal,
    private translateService: TranslateService,
    private customRouter: CustomRouterService,
    private featureFlagSrv: FeatureFlagService,
    public specificConfigurationService: SpecificConfigurationService,
  ) {}

  ngOnInit(): void {
    this.initDates();
    this.initModalTexts();
  }
  private initDates(): void {
    const currentLang: string = this.translateService.store.currentLang;

    this.paymentStartMonth = new Date(
      this.specificConfigurationService.dates.publicacioPadroDefinitiu,
    ).toLocaleString(currentLang, {
      month: 'long',
    });

    this.paymentStartDay = new Date(
      this.specificConfigurationService.dates.publicacioPadroDefinitiu,
    )
      .getDate()
      .toString();

    this.paymentStartYear = new Date(
      this.specificConfigurationService.dates.publicacioPadroDefinitiu,
    )
      .getFullYear()
      .toString();

    this.paymentEndMonth = new Date(
      this.specificConfigurationService.dates.fiPeriodeVoluntariPagament,
    ).toLocaleString(currentLang, {
      month: 'long',
    });

    this.paymentEndDay = new Date(
      this.specificConfigurationService.dates.fiPeriodeVoluntariPagament,
    )
      .getDate()
      .toString();

    this.paymentEndYear = new Date(
      this.specificConfigurationService.dates.fiPeriodeVoluntariPagament,
    )
      .getFullYear()
      .toString();

    this.modifyAccountEndMonth = new Date(
      this.specificConfigurationService.dates.limitModificacionsCompte,
    ).toLocaleString(currentLang, {
      month: 'long',
    });

    this.modifyAccountEndDay = new Date(
      this.specificConfigurationService.dates.limitModificacionsCompte,
    )
      .getDate()
      .toString();

    this.modifyAccountEndYear = new Date(
      this.specificConfigurationService.dates.limitModificacionsCompte,
    )
      .getFullYear()
      .toString();

    this.month = new Date(
      this.specificConfigurationService.dates.limitDomiciliacions,
    ).toLocaleString(currentLang, {
      month: 'long',
    });

    this.day = new Date(
      this.specificConfigurationService.dates.limitDomiciliacions,
    )
      .getDate()
      .toString();
  }

  private initModalTexts(): void {
    const translateKey = 'SE_PADRO_CO2.MODAL.PAYMENT_OPTIONS';
    const registerDomiciliation = this.featureFlagSrv.registerDomiciliation;
    const modifyDomiciliation = this.featureFlagSrv.modifyDomiciliation;
    const isDomiciliationPeriode =
      this.specificConfigurationService.inTimeToBeDomiciled;

    if (!modifyDomiciliation && registerDomiciliation) {
      this.paymentTitle = `${translateKey}.PAYMENT_TITLE_NO_DOMICILIATIONS`;
      this.paymentSubtitle = `${translateKey}.PAYMENT_SUBTITLE_NO_DOMICILIATIONS`;

      if (isDomiciliationPeriode) {
        this.subtitle = `${translateKey}.SUBTITLE_NO_DOMICILIATIONS_IN_TIME`;
        this.domiciledTitle = `${translateKey}.DOMICILED_TITLE_NO_DOMICILIATIONS_IN_TIME`;
        this.domiciledSubtitle = `${translateKey}.DOMICILED_SUBTITLE_NO_DOMICILIATIONS_IN_TIME`;
      } else {
        this.subtitle = `${translateKey}.SUBTITLE_NO_DOMICILIATIONS_OUT_TIME`;
        this.domiciledTitle = `${translateKey}.DOMICILED_TITLE_NO_DOMICILIATIONS_OUT_TIME`;
        this.domiciledSubtitle = `${translateKey}.DOMICILED_SUBTITLE_NO_DOMICILIATIONS_OUT_TIME`;
      }
    }

    if (modifyDomiciliation && !registerDomiciliation) {
      this.subtitle = `${translateKey}.SUBTITLE_ALL_DOMICILIATIONS`;
      this.domiciledTitle = ``;
      this.domiciledSubtitle = ``;
      this.paymentTitle = ``;
      this.paymentSubtitle = ``;
    }

    if (
      isDomiciliationPeriode &&
      modifyDomiciliation &&
      registerDomiciliation
    ) {
      this.subtitle = `${translateKey}.SUBTITLE_SOME_DOMICILIATIONS_IN_TIME`;
      this.domiciledTitle = `${translateKey}.DOMICILED_TITLE_SOME_DOMICILIATIONS_IN_TIME`;
      this.domiciledSubtitle = `${translateKey}.DOMICILED_SUBTITLE_SOME_DOMICILIATIONS_IN_TIME`;
      this.paymentTitle = `${translateKey}.PAYMENT_TITLE_SOME_DOMICILIATIONS_IN_TIME`;
      this.paymentSubtitle = `${translateKey}.PAYMENT_SUBTITLE_SOME_DOMICILIATIONS_IN_TIME`;
    }

    if (
      !isDomiciliationPeriode &&
      modifyDomiciliation &&
      registerDomiciliation
    ) {
      this.subtitle = `${translateKey}.SUBTITLE_SOME_DOMICILIATIONS_OUT_TIME`;
      this.domiciledTitle = `${translateKey}.DOMICILED_TITLE_SOME_DOMICILIATIONS_OUT_TIME`;
      this.domiciledSubtitle = `${translateKey}.DOMICILED_SUBTITLE_SOME_DOMICILIATIONS_OUT_TIME`;
      this.paymentTitle = `${translateKey}.PAYMENT_TITLE_SOME_DOMICILIATIONS_OUT_TIME`;
      this.paymentSubtitle = `${translateKey}.PAYMENT_SUBTITLE_SOME_DOMICILIATIONS_OUT_TIME`;
    }
  }

  closeModal(): void {
    this.activatedModalService.close();
  }

  goToDomiciled(event: string): void {
    if (event === SeModalOutputEvents.MAIN_ACTION)
      this.navigateToCreateDomiciliation();

    this.closeModal();
  }

  protected navigateToCreateDomiciliation(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.DOMICILIATION);
  }
}
