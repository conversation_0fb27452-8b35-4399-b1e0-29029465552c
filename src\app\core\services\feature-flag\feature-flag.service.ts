import { Injectable } from '@angular/core';
import { SeDataStorageService } from 'se-ui-components-mf-lib';

const FEATURE_FLAG_REGISTER_DOMICILIATION =
  'co2-feature-flag-register-domiciliation';
const FEATURE_FLAG_MODIFY_DOMICILIATION =
  'co2-feature-flag-modify-domiciliation';
const FEATURE_FLAG_PENDING_PREVIOUS_PAYMENTS =
  'co2-feature-flag-peding-previous-payments';
const FEATURE_FLAG_PENDING_CURRENT_PAYMENTS =
  'co2-feature-flag-peding-current-payments';
@Injectable({
  providedIn: 'root',
})
export class FeatureFlagService {
  constructor(private dataStorageService: SeDataStorageService) {}

  get registerDomiciliation(): boolean {
    return this.dataStorageService.getItem(FEATURE_FLAG_REGISTER_DOMICILIATION);
  }
  set registerDomiciliation(value: boolean) {
    this.dataStorageService.setItem(FEATURE_FLAG_REGISTER_DOMICILIATION, value);
  }

  get modifyDomiciliation(): boolean {
    return this.dataStorageService.getItem(FEATURE_FLAG_MODIFY_DOMICILIATION);
  }
  set modifyDomiciliation(value: boolean) {
    this.dataStorageService.setItem(FEATURE_FLAG_MODIFY_DOMICILIATION, value);
  }

  get pedingPreviousPayments(): string {
    return this.dataStorageService.getItem(
      FEATURE_FLAG_PENDING_PREVIOUS_PAYMENTS,
    );
  }
  set pedingPreviousPayments(value: string) {
    this.dataStorageService.setItem(
      FEATURE_FLAG_PENDING_PREVIOUS_PAYMENTS,
      value,
    );
  }

  get pedingCurrentPayments(): string {
    return this.dataStorageService.getItem(
      FEATURE_FLAG_PENDING_CURRENT_PAYMENTS,
    );
  }
  set pedingCurrentPayments(value: string) {
    this.dataStorageService.setItem(
      FEATURE_FLAG_PENDING_CURRENT_PAYMENTS,
      value,
    );
  }

  clearRegisterDomiciliation(): void {
    this.dataStorageService.deleteItem(FEATURE_FLAG_REGISTER_DOMICILIATION);
  }

  clearModifyDomiciliation(): void {
    this.dataStorageService.deleteItem(FEATURE_FLAG_MODIFY_DOMICILIATION);
  }

  clearPedingPayments(): void {
    this.dataStorageService.deleteItem(FEATURE_FLAG_PENDING_PREVIOUS_PAYMENTS);
    this.dataStorageService.deleteItem(FEATURE_FLAG_PENDING_CURRENT_PAYMENTS);
  }
}
