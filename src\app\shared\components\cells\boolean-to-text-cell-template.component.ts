// cell-template.component.ts
import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import {
  CellComponent,
  CellConfig,
  Column,
  FlattenedCell,
  FlattenedRow,
  Nullable,
} from 'se-ui-components-mf-lib';
import { BooleanToTextService } from '@core/services';

@Component({
  selector: 'app-boolean-to-text-cell',
  template: `
    <div
      [ngClass]="{
        'text-ellipsis': cellConfig.ellipsis,
        'text-nowrap': cellConfig.nowrap,
      }"
      [ngStyle]="cellConfig['ngStyle']"
    >
      {{ transform(value) | translate }}
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
/**
 * Represents a default cell component used in a table.
 */
export class BooleanToTextCellComponent implements CellComponent {
  @Input() value: Nullable<boolean>;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  constructor(public booleanToTextService: BooleanToTextService) {}

  transform(value: Nullable<boolean>): string {
    const trueText = this.cellConfig['trueText'];
    const falseText = this.cellConfig['falseText'];
    return this.booleanToTextService.transform(value, trueText, falseText);
  }
}
