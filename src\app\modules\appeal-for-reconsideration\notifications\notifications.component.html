<div>
  <mf-contribuent-notificacions
    *axLazyElement
    [identificationData]="contribuentWcInput"
    [origen]="OrigenEnum.RECURS"
    [allowSafeData]="true"
    [emitContactDataOnActivateNoticesMode]="true"
    [updateContactDataMsg]="
      'SE_PADRO_CO2.RECURS_PROCESS.DADES_NOTIFICACIO.UPDATE_CONTACT_DATA_PANEL_MSG'
        | translate
    "
    [activateNoticesMsg]="
      'SE_PADRO_CO2.RECURS_PROCESS.DADES_NOTIFICACIO.ACTIVATE_NOTICES_PANEL_MSG'
        | translate
    "
    [sessionStorageName]="PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA"
    (notificationsOutput)="onContribuentOutput($event)"
  ></mf-contribuent-notificacions>
  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button
      type="button"
      btnTheme="secondary"
      (onClick)="onGoBackButtonClick()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.BACK' | translate }}
    </se-button>

    <se-button
      type="submit"
      btnTheme="primary"
      (onClick)="onContinueButtonClick()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.CONTINUE' | translate }}
    </se-button>
  </div>
</div>
