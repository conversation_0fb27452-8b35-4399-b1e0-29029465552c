<se-modal
  [data]="data"
  (modalOutputEvent)="closeModal()"
  (modalSecondaryButtonEvent)="closeModal()"
>
  <div
    *ngFor="let data of panelData; let isFirst = first"
    [ngClass]="{ 'mt-4': !isFirst }"
  >
    <se-panel [title]="data.panelTitle | translate">
      <div class="row flex-row">
        <ng-container *ngFor="let field of data.fields">
          <div
            class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
          >
            <span class="col-md-3 col-12 fw-bold">{{
              field.label | translate
            }}</span>
            <span class="col-md-9 col-12">{{ field.value | translate }}</span>
          </div>
        </ng-container>
      </div>
    </se-panel>
  </div>
</se-modal>
