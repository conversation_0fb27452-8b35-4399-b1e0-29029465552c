import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  RequestDownloadFile,
  ResponseDownloadFile,
  SeHttpRequest,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import {
  RequestDeleteFile,
  ResponseDeleteFile,
  RequestGetFiles,
  ResponseGetFiles,
  RequestGetDetailDocuments,
  ResponseGetDetailDocuments,
  IResponseRetryDocumentUpload,
  RequestUploadFile,
  ResponseUploadFile,
  ResponseUploadFiles,
  RequestDeleteDocuments,
  ResponseDeleteDocuments,
} from './../../models';

@Injectable({
  providedIn: 'root',
})
export class UploadFilesEndpointsService {
  constructor(private httpService: SeHttpService) {}

  // Download file
  downloadFile = (
    request: RequestDownloadFile,
  ): Observable<ResponseDownloadFile> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/${request.id}`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  };

  // Delete file
  deleteFile = (request: RequestDeleteFile): Observable<ResponseDeleteFile> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/${request.idDocument}`,
      method: 'delete',
    };
    return this.httpService.delete(httpRequest);
  };

  deleteFiles = (
    request: RequestDeleteDocuments,
  ): Observable<ResponseDeleteDocuments> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/llistat/delete`,
      method: 'post',
      body: request,
      clearExceptions: true,
    };
    return this.httpService.post(httpRequest);
  };

  // Get files
  getFiles = (request: RequestGetFiles): Observable<ResponseGetFiles> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/criteria`,
      method: 'post',
      body: request,
    };

    return this.httpService.post(httpRequest);
  };

  // Get files
  getDetailDocuments = (
    request: RequestGetDetailDocuments,
    spinner?: boolean,
  ): Observable<ResponseGetDetailDocuments> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/batch/llistat`,
      method: 'post',
      body: request,
      spinner: spinner,
    };

    return this.httpService.post(httpRequest);
  };

  // retryDocumentUpload
  retryDocumentUpload = (
    idEntity: string,
  ): Observable<IResponseRetryDocumentUpload> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/reintent/${idEntity}`,
      method: 'get',
    };
    return this.httpService.get(httpRequest);
  };

  // Upload file
  uploadFile = (request: RequestUploadFile): Observable<ResponseUploadFile> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2`,
      method: 'post',
      body: request,
    };
    return this.httpService.post(httpRequest);
  };

  // Upload file
  uploadFiles = (
    request: RequestUploadFile[],
  ): Observable<ResponseUploadFiles> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/multiple`,
      method: 'post',
      body: request,
    };
    return this.httpService.post(httpRequest);
  };
}
