<div class="app-reason-allegation pt-3">
  <se-alert
    *ngIf="
      vehiclesInfo &&
      vehiclesInfo.matriculas &&
      vehiclesInfo.matriculas.length > 1
    "
    [type]="'info'"
    [closeButton]="false"
    [title]="
      'SE_PADRO_CO2.ALLEGATIONS.ALERT_MULTIPLE_VEHICLES.TITLE' | translate
    "
    [subtitle]="
      'SE_PADRO_CO2.ALLEGATIONS.ALERT_MULTIPLE_VEHICLES.SUBTITLE' | translate
    "
    [subtitleClass]="'fw-normal'"
  >
  </se-alert>
  <!-- Allegations Options -->
  <app-allegations-options
    *ngIf="reasonsData"
    [title]="'SE_PADRO_CO2.ALLEGATIONS.REASON_TITLE' | translate"
    [subtitle]="'SE_PADRO_CO2.ALLEGATIONS.SELECT_ALLEGATIONS' | translate"
    [panelDescriptionDocuments]="
      'SE_PADRO_CO2.ALLEGATIONS.DOCUMENTS.SUBTITLE' | translate
    "
    [reasonsData]="reasonsData"
    [functionalModule]="functionalModule.ALLEGATIONS"
    [idTramit]="idTramit"
    [documentsSigedaDescriptions]="documentsSigedaDescriptions"
    (allegationsOptionsData)="onAllegationsOptionsChange($event)"
  ></app-allegations-options>

  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button
      type="button"
      btnTheme="secondary"
      (onClick)="onGoBackButtonClick()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate }}
    </se-button>

    <se-button
      type="submit"
      btnTheme="primary"
      (onClick)="onContinueButtonClick()"
      [disabled]="!reasonsFormData?.isReasonsFormValid"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.CONTINUE' | translate }}
    </se-button>
  </div>
</div>
