import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { XatbotAccess, XatbotCanal, XatbotTopic } from './xatbot.model';
import { HttpClient } from '@angular/common/http';

@Injectable({
  providedIn: 'root',
})
export class XatbotService {
  private renderer: Renderer2;
  private readonly pathVersio: string = '/co2/version.json';
  private readonly pathIndex: string = '/co2/index.html';

  constructor(
    private rendererFactory: RendererFactory2,
    private httpClient: HttpClient,
  ) {
    this.renderer = rendererFactory.createRenderer(null, null);
  }

  public loadScript(
    canal: XatbotCanal,
    topic: XatbotTopic,
    access: XatbotAccess,
    produccio: boolean,
  ): void {
    const urlXb = produccio
      ? 'https://uixatbot.atc.gencat.cat'
      : 'https://preproduccio.uixatbot.atc.gencat.cat';

    // Obtenemos el JSON de version
    this.getVersioJson(urlXb + this.pathVersio)
      .then((r) => {
        // Extraemos la version del JSON
        const versio = this.extractVersion(r);
        // Añadimos el script del Xatbot con la version obtenida
        this.addScript(
          urlXb + this.pathIndex + `?versio=${versio}`,
          canal,
          topic,
          access,
        );
      })
      .catch((e) => {
        console.warn('Error al obtener la versión del JSON', e);
        // Si hay algun error, añadimos el script del Xatbot con un timestamp como version
        const versio = Date.now();
        this.addScript(
          urlXb + this.pathIndex + `?versio=${versio}`,
          canal,
          topic,
          access,
        );
      });
  }

  private addScript(
    url: string,
    canal: XatbotCanal,
    topic: XatbotTopic,
    access: XatbotAccess,
  ): void {
    const scriptContent = `
        function getDataXatbot() {
          return {
            canal: '${canal}', // web_desktop, web_mobile
            topic: '${topic}', // listado
            access: '${access}', // LOGIN_AOC, SIMPLE_LOGIN
          };
        }

        $('#external-scripts').load(
          '${url}',
        );

        console.log('Xatbot loaded');
        console.log(getDataXatbot());
    `;
    const script = this.renderer.createElement('script');
    script.type = 'text/javascript';
    script.text = scriptContent;
    this.renderer.appendChild(document.body, script);
  }

  private async getVersioJson(url: string): Promise<unknown> {
    const timestamp = Date.now();
    try {
      // Obtiene el archivo JSON de la URL proporcionada
      const response = await this.httpClient
        .get(`${url}?v=${timestamp}`)
        .toPromise();
      return response;
    } catch (error) {
      console.warn('Error al obtener el archivo JSON: ' + url, error);
      return {};
    }
  }

  private extractVersion(json: unknown): string {
    let version = `${Date.now()}`;
    //Extrae la versión del componente
    if (json && (json as { version?: string }).version) {
      version = (json as { version: string }).version;
    } else {
      console.warn('Error: La versión no se encuentra en el JSON');
    }
    return version;
  }
}
