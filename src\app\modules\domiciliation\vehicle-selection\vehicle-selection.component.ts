import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit } from '@angular/core';
import {
  AppRout<PERSON>,
  DomiciledVehicle,
  DomiciledVehiclesRequest,
  IdentificationType,
} from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { DomiciliationEndpointService } from '@shared/services';
import { shareReplay, Subject, takeUntil } from 'rxjs';
import {
  Column,
  DeviceService,
  Row,
  SeAlertMessage,
  SeModalService,
  WcComponentEvent,
} from 'se-ui-components-mf-lib';
import { ContinueWithoutDomicilingNewVehiclesModalComponent } from '../continue-without-new-vehicles-modal/continue-without-domiciling-new-vehicles.component';
import {
  ContactDataInputData,
  VEHICLES_TO_DOMICILE_COLUMNS,
} from './model/vehicle-selection.model';
import { TagColorService } from '@app/core/services/tag-color';
import { NoNeSimpleLoginModalComponent } from '../no-ne-simple-login-modal/no-ne-simple-login-modal.component';
import { ProceduresHeaderService } from '../../../shared/services/procedures-header';

@Component({
  selector: 'app-vehicle-selection',
  templateUrl: './vehicle-selection.component.html',
  styleUrls: ['./vehicle-selection.component.scss'],
})
export class VehicleSelectionComponent implements OnInit, OnDestroy {
  protected data: Row[] = [];
  protected vehiclesToDomicileColumns: Column[] = VEHICLES_TO_DOMICILE_COLUMNS;
  protected selectedVehicles: Row[] = [];
  protected domiciliationDate = this.specificConfigurationSrv.nextReceiptDate;
  protected alertDomiciliationRequest: SeAlertMessage | undefined;
  protected contactDataInput: ContactDataInputData | undefined;
  protected hasProcessFinished = false;
  protected itemsPerPage = this.calculateItemsPerPage();
  protected isMobile = this.deviceSrv.isMobile();

  private allVehicles: DomiciledVehicle[] = [];
  private destroyed$ = new Subject<void>();

  get isAnyNewVehicleSelected(): boolean {
    return this.selectedVehicles.some((vehicle) => vehicle.data['nou'].value);
  }
  get outOfTimeToBeDomiciled(): boolean {
    return this.specificConfigurationSrv.outOfTimeToBeDomiciled;
  }
  get inTimeToBeDomiciled(): boolean {
    return this.specificConfigurationSrv.inTimeToBeDomiciled;
  }
  get isAnyOldVehicleSelected(): boolean {
    return this.selectedVehicles.some((vehicle) => !vehicle.data['nou'].value);
  }

  get isLoginSimple(): boolean {
    return this.storage.profileUser === IdentificationType.LOGIN_SIMPLE;
  }

  get isConveniat(): boolean {
    return this.storage.profileUser === IdentificationType.CONVENIAT;
  }

  get isRepresentant(): boolean {
    return this.storage.profileUser === IdentificationType.REPRESENTATIVE;
  }

  get isCivilServant(): boolean {
    return this.storage.profileUser === IdentificationType.CIVIL_SERVANT;
  }

  get isSelfPerson(): boolean {
    return this.storage.profileUser === IdentificationType.NOM_PROPI;
  }

  get isProvisional(): boolean {
    return this.specificConfigurationSrv.isProvisional;
  }

  constructor(
    private domiciliationEndpointSrv: DomiciliationEndpointService,
    private translateSrv: TranslateService,
    private loginSrv: LoginResponseService,
    private deviceSrv: DeviceService,
    private modalSrv: SeModalService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private storage: StorageService,
    private activatedModalService: NgbActiveModal,
    private customRouter: CustomRouterService,
    private header: ProceduresHeaderService,
    private tagColorService: TagColorService,
  ) {}

  ngOnInit(): void {
    this.header.setupDomiciliationHeader();

    this.setRequestDirectDebitAlertList();
    this.getVehiclesToBeDomiciliated();
    this.initializeContactDataModal();
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected onSelfAssessmentSelectionChange(list: Row[]): void {
    this.selectedVehicles = list;
  }

  protected goBack(): void {
    this.storage.clearVehiclesToBeRegistered();
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  protected onContinue(): void {
    this.checkUserSubscription();
  }

  private checkUserSubscription(): void {
    if (!this.loginSrv.user.idPersTitular) return;

    this.domiciliationEndpointSrv
      .getNotificationDataById(this.loginSrv.user.idPersTitular)
      .pipe(takeUntil(this.destroyed$), shareReplay(1))
      .subscribe((response) => {
        if (!response.content) return;
        // cuando hay algun vehiculo nuevo
        // y no esta suscrito a las notificaciones electronicas
        // debe aparecer el modal de datos de contacto
        if (
          !response.content?.notificacioElectronica &&
          this.isAnyNewVehicleSelected &&
          this.inTimeToBeDomiciled
        ) {
          this.handleNotNe();
        } else {
          const fn = (vehicle: Row): string[] => {
            return vehicle.data['checkbox'].value;
          };
          this.navigateToSecondStepAndSaveVehiclesToBeRegisteredInStorage(fn);
        }
      });
  }

  private handleNotNe(): void {
    if (this.isSelfPerson) {
      this.contactDataInput = {
        ...this.contactDataInput,
        showModal: true,
      } as ContactDataInputData;

      return;
    }

    if (
      this.isLoginSimple ||
      this.isConveniat ||
      this.isRepresentant ||
      this.isCivilServant
    ) {
      this.showNoNeVehicleSimpleLoginModal();

      return;
    }
  }

  private showNoNeVehicleSimpleLoginModal(): void {
    const component: NoNeSimpleLoginModalComponent = this.modalSrv.openModal({
      title: 'SE_PADRO_CO2.DOMICILIATION_MODULE.NO_NE_SIMPLE_LOGIN_MODAL.TITLE',
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CLOSE',
      severity: 'warning',
      component: NoNeSimpleLoginModalComponent,
    }).componentInstance;

    component.closeNeModal.pipe(takeUntil(this.destroyed$)).subscribe(() => {
      if (this.isAnyOldVehicleSelected) {
        this.openContinueWithoutDomicilingNewVehiclesModal();
      }
    });
  }

  protected onContactDataOutput(event: Event): void {
    const customEvent = event as CustomEvent<WcComponentEvent>;

    // customEvent.detail --> la 1ª vez viene como []
    if (customEvent.detail && customEvent.detail.componentEvent) {
      const { hasAccess, receiptCsv, receiptId } =
        customEvent.detail.componentEvent;
      // ya se suscribió
      if (hasAccess && receiptCsv && receiptId && this.hasProcessFinished) {
        // hasProcessFinished - se creó porque el componente de contact-data emite este evento al cargando este componente y cuando ya se han ingresado estos datos, pasa esa validacion
        const fn = (vehicle: Row): string[] => {
          return vehicle.data['checkbox'].value;
        };
        this.navigateToSecondStepAndSaveVehiclesToBeRegisteredInStorage(fn);
      } else {
        // ha cerrado el modal entonces solo se procesarán los vehiculos que no son nuevos
        if (this.isAnyOldVehicleSelected) {
          this.openContinueWithoutDomicilingNewVehiclesModal();
        }
      }
    }
  }

  private initializeContactDataModal(): void {
    const notificationsLabelKey =
      'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.CONTACT_DATA';
    // datos copiados de se-contribuent-mf notifications
    this.contactDataInput = {
      daysToReminder: 0.0007, // 0.0007 days === 1 min
      disableLogoutOnDeniedAccess: true,
      wcAsModal: true,
      showModal: false,
      hideReceipt: false,
      isSubscription: true,
      requiredNotificationCheck: true,
      notificationsCheckLabel: this.translateSrv.instant(
        `${notificationsLabelKey}.NOTIFICATION_CHECK`,
      ),
      noticesCheckLabel: this.translateSrv.instant(
        `${notificationsLabelKey}.NOTICE_CHECK`,
      ),
      modalTitle: this.translateSrv.instant(`${notificationsLabelKey}.TITLE`),
      checksText: this.translateSrv.instant(`${notificationsLabelKey}.TEXT`),
      checksTitle: this.translateSrv.instant(
        `${notificationsLabelKey}.CHECKS_TITLE`,
      ),
    };

    this.hasProcessFinished = true;
  }

  // para solicitud de domiciliacion app-domiciliation-request - panel derecho
  private setRequestDirectDebitAlertList(): void {
    const { percentDiscount } = this.specificConfigurationSrv;
    this.alertDomiciliationRequest = {
      title: this.translateSrv.instant(
        'SE_PADRO_CO2.DOMICILIATION_MODULE.REQUEST_DIRECT_DEBIT_PANEL.ALERT_LIST.FIRST',
        { percentDiscount },
      ),
    } as SeAlertMessage;
  }

  // ---para la tabla de vehiculos a domiciliar - panel izquierdo ↓↓↓
  private getVehiclesToBeDomiciliated(): void {
    this.domiciliationEndpointSrv
      .getVehiclesToBeDomiciliated(this.getRequestForVehiclesToBeDomiciliated())
      .pipe(takeUntil(this.destroyed$))
      .subscribe((result) => {
        if (result?.content) {
          this.allVehicles = result.content;
          this.setDomiciledVehiclesParsedInTable(result?.content);
        }
      });
  }

  private getRequestForVehiclesToBeDomiciliated(): DomiciledVehiclesRequest {
    return {
      provisional: this.specificConfigurationSrv.isProvisional,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
      exercici: this.specificConfigurationSrv.currentExercise,
      matricula: this.storage.licensePlate,
      tipusAccess: this.loginSrv.user.tipusAccess,
    };
  }

  private setDomiciledVehiclesParsedInTable(
    vehicles: DomiciledVehicle[],
  ): void {
    this.data = vehicles.map((vehicle) => ({
      data: {
        plate: {
          value: vehicle.matricula,
          cellConfig: {
            tagCell: {
              tagTheme: this.tagColorService.getPlateTagColor(
                vehicle.nou,
                vehicle.modificat,
              ),
            },
            tagValue: this.tagColorService.getPlateTagValue(
              vehicle.nou,
              vehicle.modificat,
            ),
          },
        },
        nou: { value: vehicle.nou },
        modificat: { value: vehicle.modificat },
        model: { value: vehicle.model },
        brand: { value: vehicle.marca },
        vehicle: { value: `${vehicle.marca} ${vehicle.model}` },
        checkbox: {
          value: this.storage.vehiclesToBeRegistered?.find(
            (storageVehicle) => storageVehicle.matricula === vehicle.matricula,
          ),
        },
      },
    }));

    this.selectedVehicles = this.data.filter(
      (row) => row.data['checkbox'].value,
    );
  }
  // ---para la tabla de vehiculos a domiciliar - panel izquierdo ↑↑↑

  // solo se muestra cuando cierran el modal de notificaciones electrónicas
  private openContinueWithoutDomicilingNewVehiclesModal(): void {
    const component: ContinueWithoutDomicilingNewVehiclesModalComponent =
      this.modalSrv.openModal({
        title:
          'SE_PADRO_CO2.DOMICILIATION_MODULE.CONTINUE_WITHOUT_DOMICILING_NEW_VEHICLES_MODAL.TITLE',
        severity: 'warning',
        closable: true,
        closableLabel: 'SE_PADRO_CO2.BUTTONS.CONTINUE',
        centered: true,
        titleTextWeight: 'semi-bold',
        secondaryButton: true,
        secondaryButtonLabel: 'UI_COMPONENTS.BUTTONS.CANCEL',
        component: ContinueWithoutDomicilingNewVehiclesModalComponent,
      }).componentInstance;

    // los vehiculos que no son nuevos porque no estan suscritos a las notificaciones electronicas
    component.tableData = this.selectedVehicles.filter(
      (vehicle) => !vehicle.data['nou'].value,
    );

    // solo guarda en domiciliationSrv los vehiculos que no son nuevos
    component.handleAcceptModalOutput
      .pipe(takeUntil(this.destroyed$))
      .subscribe(() => {
        // filtro de solo las matriculas de los vehiculos que no son nuevos y qye estan seleccionados
        const fn = (vehicle: Row): string[] => {
          return vehicle.data['checkbox'].value && !vehicle.data['nou'].value;
        };
        this.activatedModalService.close();
        this.navigateToSecondStepAndSaveVehiclesToBeRegisteredInStorage(fn);
      });
  }

  // filtra los vehiculos de la tabla con los que vienen en el responde BE segun la función de filtro
  private getVehiclesToBeRegistered(
    filterFn: (vehicle: Row) => string[],
  ): DomiciledVehicle[] {
    const plates = this.selectedVehicles
      .filter(filterFn)
      .map((vehicle) => vehicle.data['plate'].value);

    return this.allVehicles.filter((item) => plates.includes(item.matricula));
  }

  private navigateToSecondStepAndSaveVehiclesToBeRegisteredInStorage(
    fn: (vehicle: Row) => string[],
  ): void {
    this.storage.vehiclesToBeRegistered = this.getVehiclesToBeRegistered(fn);
    this.customRouter.navigateByBaseUrl(AppRoutes.BANK_DETAILS);
  }

  private calculateItemsPerPage(): number {
    return this.deviceSrv.isMobile() ? 5 : 10;
  }
}
