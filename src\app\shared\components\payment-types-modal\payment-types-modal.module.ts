import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SeModalModule } from 'se-ui-components-mf-lib';
import { PaymentTypesModalComponent } from './payment-types-modal.component';

@NgModule({
  declarations: [PaymentTypesModalComponent],
  imports: [CommonModule, TranslateModule.forChild(), SeModalModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [PaymentTypesModalComponent],
})
export class PaymentTypesModalModule {}
