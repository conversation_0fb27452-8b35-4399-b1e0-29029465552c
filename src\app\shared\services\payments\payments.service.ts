import { Injectable } from '@angular/core';
import { take } from 'rxjs';

import { BaseUrlService } from '@app/core/services/url-atc/url-atc.service';
import {
  BankDetailInput,
  DocData,
} from '@app/modules/domiciliation/bank-details/model/bank-details.model';
import { AppRoutes, IdentificationType } from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  StorageService,
} from '@core/services';
import { CsvModalComponent } from '@shared/components/csv-modal/csv-modal.component';
import { ExecutivaAlertModalComponent } from '@shared/components/executiva-alert-modal/executiva-alert-modal.component';
import { TramitProcessType, VehiclesSelectedInfo } from '@shared/models';
import {
  Nullable,
  SeMfConfigurationService,
  SeModalService,
  SeUserService,
} from 'se-ui-components-mf-lib';
import {
  CalculatePaymentRequest,
  CalculatePaymentResponse,
} from './payments-endpoints.model';
import { PaymentsEndpointsService } from './payments-endpoints.service';
import { TranslateService } from '@ngx-translate/core';

const DEUTES_EL_MEU_ESPAI = `deutes`;

@Injectable({
  providedIn: 'root',
})
export class PaymentsService {
  constructor(
    private readonly endpointsService: PaymentsEndpointsService,
    private readonly storageService: StorageService,
    private readonly modalService: SeModalService,
    private readonly customRouter: CustomRouterService,
    private readonly userService: SeUserService,
    private readonly baseUrlService: BaseUrlService,
    private readonly loginService: LoginResponseService,
    private readonly translate: TranslateService,
    private readonly mfConfigurationService: SeMfConfigurationService,
  ) {
    // Intencionadamente vacío
  }

  public initPayment(
    vehiclesInfo: Nullable<VehiclesSelectedInfo>,
    continuarAmbValids?: boolean,
    csv?: string,
  ): void {
    this.storageService.setVehiclesSelected(vehiclesInfo);

    if (!vehiclesInfo) {
      return;
    }

    const request: CalculatePaymentRequest = this.getRequestBody(
      vehiclesInfo,
      continuarAmbValids,
      csv,
    );

    this.endpointsService
      .setPayment(request)
      .pipe(take(1))
      .subscribe((response: Nullable<CalculatePaymentResponse>) => {
        if (response) {
          const { idBBDD, refPago, idPagament } = response;

          if (idBBDD && refPago && idPagament) {
            this.goToPaymentProcess(response);
          } else if (
            response.pagamentExecutivaError ||
            response.pagamentAltresError
          ) {
            this.openExecutivaAlertModal(response);
          } else if (response.pendentNotificacio) {
            //If has an error or state --> Open modal
            this.openCSVModal(
              !response.allPendentNotificacio,
              continuarAmbValids,
            );
          }
        }
      });
  }

  private getRequestBody(
    vehiclesInfo: VehiclesSelectedInfo,
    continuarAmbValids?: boolean,
    csv?: string,
  ): CalculatePaymentRequest {
    const atesaPhone = this.storageService.civilServantVehiclePhone;
    const atesaDate = this.storageService.civilServantVehicleDate;
    const atesaHour = this.storageService.civilServantVehicleHour;
    return {
      idPersTitular: vehiclesInfo.idPersTitular,
      vehicles: vehiclesInfo.vehicles ?? [],
      provisional: vehiclesInfo.provisional,
      csv,
      continuarAmbValids,
      tipusAccess:
        this.loginService.user.tipusAccess || IdentificationType.NOM_PROPI,
      ...(atesaPhone && atesaDate && atesaHour
        ? {
            trucadaTelefonica: {
              numeroTelefon: atesaPhone,
              data: atesaDate,
              hora: atesaHour,
            },
          }
        : {}),
    };
  }

  private openCSVModal(multiple: boolean, continuarAmbValids?: boolean): void {
    const modalRef = this.modalService.openModal({
      severity: 'info',
      size: 'xl',
      title:
        'SE_PADRO_CO2.MODAL.CSV_MODAL.' +
        (multiple ? 'MULTIPLE' : 'SINGLE') +
        '.TITLE',
      closable: true,
      closableDisabled: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
      component: CsvModalComponent,
    });

    modalRef.componentInstance.multiple = multiple;

    modalRef.componentInstance.continueOutput
      .pipe(take(1))
      .subscribe((csv: string) => {
        this.initPayment(
          this.storageService.getVehiclesSelected(),
          continuarAmbValids,
          csv,
        );
      });
  }

  private openExecutivaAlertModal(response: CalculatePaymentResponse): void {
    let closableLabel = 'SE_PADRO_CO2.MODAL.EXECUTIVA_ALERT.BUTTON_PAY';
    let title = 'SE_PADRO_CO2.MODAL.EXECUTIVA_ALERT.TITLE';
    let message = 'SE_PADRO_CO2.MODAL.EXECUTIVA_ALERT.MESSAGE_1';
    const messageInfo = 'SE_PADRO_CO2.MODAL.EXECUTIVA_ALERT.MESSAGE_2';
    let goToDebtsPayment = false;
    const vehicles = this.storageService.getVehiclesSelected()?.vehicles ?? [];

    //Comprovar situació (1 rebut) - En executiva
    if (response.pagamentExecutivaError && vehicles.length === 1) {
      title = 'SE_PADRO_CO2.MODAL.EXECUTIVA_ALERT.TITLE_SINGLE';
      message = 'SE_PADRO_CO2.MODAL.EXECUTIVA_ALERT.MESSAGE_SINGLE';

      if (
        this.storageService.profileUser === IdentificationType.NOM_PROPI &&
        !this.mfConfigurationService.getIsMobileApp()
      ) {
        closableLabel =
          'SE_PADRO_CO2.MODAL.EXECUTIVA_ALERT.GO_TO_DEBTS_PAYMENT';
        goToDebtsPayment = true;
      }
    }

    const modalRef = this.modalService.openModal({
      severity: 'warning',
      title,
      closable: true,
      closableLabel:
        response.vehicles.length > 0 || goToDebtsPayment
          ? closableLabel
          : 'SE_PADRO_CO2.BUTTONS.ACCEPT',
      secondaryButton: response.vehicles.length > 0 || goToDebtsPayment,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
      component: ExecutivaAlertModalComponent,
    });

    modalRef.componentInstance.vehicles = goToDebtsPayment
      ? []
      : response.vehicles;
    modalRef.componentInstance.message = message;
    modalRef.componentInstance.messageInfo = messageInfo;
    modalRef.componentInstance.tramitProcess = TramitProcessType.PAGAMENTS;

    modalRef.componentInstance.continueOutput.pipe(take(1)).subscribe(() => {
      if (goToDebtsPayment) {
        this.baseUrlService.goToElMeuEspai(DEUTES_EL_MEU_ESPAI);
      } else if (response.vehicles.length > 0) {
        const vehiclesInfo = this.storageService.getVehiclesSelected();
        vehiclesInfo!.matriculas = response.vehicles.map(
          (vehicle) => vehicle.matricula,
        );

        this.initPayment(vehiclesInfo, true);
      }
    });
  }

  private goToPaymentProcess(response: CalculatePaymentResponse): void {
    this.storageService.setPaymentVehicles(response);
    this.customRouter.navigateByBaseUrl(AppRoutes.PAYMENTS_PROCESS);
  }

  getBankDetailInput({
    iban,
    dr,
    isIbanRequired,
    docData,
    isPresenter = true,
    drLabel = 'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_2.BANK_DETAIL_DR',
  }: {
    iban?: string;
    dr?: boolean;
    isIbanRequired?: boolean;
    docData?: DocData;
    isPresenter?: boolean;
    drLabel?: string;
  }): BankDetailInput {
    const co2User = this.loginService.user;
    // TODO review casuistica
    //const nif = this.userService.getNIF();

    return {
      //TODO [Fran G] ñapa para que funcione, hay que revisar el componente de declaracion responsable, no esta bien
      isPresenter: isPresenter,
      //TODO ---
      iban: iban,
      isIbanRequired: isIbanRequired,
      ...(docData ? { docData: docData } : {}),
      taxpayers: [
        {
          nif: co2User.nifTitular as string,
          name: co2User.nom as string,
        },
        //TODO [Fran G] ñapa para que funcione, hay que revisar el componente de declaracion responsable, no esta bien
        {
          nif: this.userService.getNIF() as string,
          name: this.userService.getName() as string,
        },
        //TODO ---
      ],
      agreementCode: '25',
      DRValue: dr,
      dRLabel: this.translate.instant(drLabel),
    };
  }

  isValidateRepresentative = (): boolean => {
    return (
      this.storageService.profileUser !== IdentificationType.LOGIN_SIMPLE &&
      this.storageService.profileUser !== IdentificationType.NOM_PROPI
    );
  };
}
