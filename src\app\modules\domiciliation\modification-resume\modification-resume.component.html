<se-alert
  *ngIf="titleText"
  [title]="titleText"
  [type]="'info'"
  [closeButton]="false"
>
</se-alert>
<app-request-summary
  [title]="summaryTitle"
  [data]="directDebitSummary"
  [showPDFInfo]="showPDFInfo"
  [modalTitle]="modalTitle"
  [hideVehiclesModalExerciceColumn]="true"
  [footerMessage]="'SE_PADRO_CO2.PRESENTATION_RECEIPT.INFO_2' | translate"
>
  <app-user-data-login-simple-form [loginSimpleUserForm]="loginSimpleUserForm">
  </app-user-data-login-simple-form>
</app-request-summary>

<div class="mt-4">
  <app-civil-servant-required-doc-block
    [idEntity]="idEntity"
    [idFunctionalModule]="idFunctionalModule"
    [plates]="plates"
    [authDomiciliacio]="true"
    (disableContinueButton)="onDisableContinueButton($event)"
  >
  </app-civil-servant-required-doc-block>
</div>

<div
  class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
>
  <se-button (onClick)="goBack()" [btnTheme]="'secondary'">
    {{ 'UI_COMPONENTS.BUTTONS.PREVIOUS' | translate }}
  </se-button>
  <se-button
    (onClick)="onContinue()"
    [disabled]="!!loginSimpleUserForm?.invalid || disableContinueButton"
  >
    {{ continueButtonText | translate }}
  </se-button>
</div>
