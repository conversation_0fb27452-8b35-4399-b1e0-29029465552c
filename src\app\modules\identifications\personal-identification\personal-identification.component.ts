import { Component, OnDestroy } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Subject, takeUntil } from 'rxjs';
import {
  LoginCertificates,
  Nullable,
  SeAuthService,
  SeMessageI,
  SeMessageService,
  SeValidations,
} from 'se-ui-components-mf-lib';
import {
  AppRoutes,
  BASE_URL,
  DeclaracioResponsableChange,
  IdentificationType,
  LoginRequest,
  ValidateButtonEnum,
} from '@core/models';
import { IdentificationsService, StorageService } from '@core/services';
import { Taxpayer } from '../models';
import { BaseUrlService } from '@app/core/services/url-atc/url-atc.service';

@Component({
  selector: 'app-personal-identification',
  templateUrl: './personal-identification.component.html',
  styleUrls: ['./personal-identification.component.scss'],
})
export class PersonalIdentificationComponent implements OnDestroy {
  identificationForm: FormGroup;

  thirdPersonForm: FormGroup = this.fb.group({
    thirdPresenterNif: [
      '',
      SeValidations.listValidations([
        { validator: Validators.required },
        { validator: SeValidations.dniNieCif() },
      ]),
    ],
    thirdPresenterName: ['', [Validators.required]],
  });

  ValidateButtonEnum = ValidateButtonEnum;
  statementOption: Nullable<Taxpayer>;
  IdentificationType = IdentificationType;

  // User validations
  procedure: boolean = false;
  representative: boolean = false;

  // Other
  isStatementChecked!: boolean;
  showThirdPersonForm: boolean = false;
  isResponsibleStatementShown: boolean = false;
  notValidData: SeMessageI = {
    severity: 'warning',
    title: 'SE_PADRO_CO2.MODULE_IDENTIFICATION.NO_VALID_TITLE',
    subtitle: 'SE_PADRO_CO2.MODULE_IDENTIFICATION.NO_VALID_SUBTITLE',
  };

  get hasRepresentants(): boolean {
    return this.identificationsSrv.isRepresentative;
  }

  get isLegalUser(): boolean {
    const user = this.authService.getSessionStorageUser();
    return (
      user?.certificateType === LoginCertificates.JUR_PERSON ||
      user?.certificateType === LoginCertificates.REPRE_JUR
    );
  }

  protected scoringTaxpayer: Taxpayer | undefined;
  protected taxPayerDocumentLabel = 'NIF';
  protected disableSubmitButton = true;

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private authService: SeAuthService,
    private storageSrv: StorageService,
    private identificationsSrv: IdentificationsService,
    private fb: FormBuilder,
    private msgService: SeMessageService,
    private readonly baseUrlService: BaseUrlService,
  ) {
    this.identificationForm = this.fb.group({
      identificationType: [IdentificationType.NOM_PROPI],
      responsibleStatement: [],
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  DRChanged = (event: Event): void => {
    const responsibleStatement = (
      event as CustomEvent<DeclaracioResponsableChange>
    )?.detail.DR;
    this.identificationForm
      .get('responsibleStatement')
      ?.setValue(responsibleStatement);
    this.disableSubmitButton = !responsibleStatement;
  };

  goBack(): void {
    this.showThirdPersonForm = false;
  }

  continue(): void {
    if (
      this.identificationForm.get('identificationType')?.value ===
        IdentificationType.REPRESENTATIVE &&
      !this.showThirdPersonForm
    ) {
      this.thirdPersonForm.patchValue({
        thirdPresenterNif: '',
        thirdPresenterName: '',
      });
      this.showThirdPersonForm = true;
    } else {
      this.onNavigateToPadronList();
    }
  }

  protected onNavigateToPadronList(): void {
    if (
      this.identificationForm.get('identificationType')?.value ===
      IdentificationType.NOM_PROPI
    ) {
      // Si actua en nombre propio se redirige al meu espai
      this.identificationsSrv
        .loginCo2({}, IdentificationType.NOM_PROPI)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe(() => {
          this.baseUrlService.goToElMeuEspai(
            `${BASE_URL}/${AppRoutes.RECEIPTS}`,
          );
        });
    } else {
      const body: LoginRequest = {
        nom: this.scoringTaxpayer!.name,
        nifTitular: this.scoringTaxpayer!.nif,
      };
      this.login(body, IdentificationType.REPRESENTATIVE);
    }
  }

  protected onScoringValidationChange(event: Event): void {
    const customEvent = event as CustomEvent<{
      valid: boolean;
    }>;
    const { valid } = customEvent.detail;
    if (valid) {
      this.verifyIfIsRepresentative(this.scoringTaxpayer!.nif);
    } else {
      this.disableSubmitButton = true;
    }
  }

  protected onScoringTaxpayerChange(event: Event): void {
    const { name, nif } = (event as CustomEvent<Taxpayer>).detail;

    this.msgService.resetMessages();

    this.scoringTaxpayer = { name, nif: nif?.toUpperCase() };
  }

  private login(body: LoginRequest, profileUser: IdentificationType): void {
    this.identificationsSrv
      .loginCo2(body, profileUser, AppRoutes.RECEIPTS)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.storageSrv.taxpayerName = this.scoringTaxpayer!.name;
        this.storageSrv.taxpayerNIF = this.scoringTaxpayer!.nif;
      });
  }

  private verifyIfIsRepresentative(representativeNIF: string): void {
    this.identificationsSrv
      .verifyIfIsRepresentative(representativeNIF)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((isRepresentative) => {
        this.disableSubmitButton = !isRepresentative;
        if (!isRepresentative) {
          this.msgService.addMessages([
            {
              severity: 'error',
              title:
                'SE_PADRO_CO2.MODULE_IDENTIFICATION.ALERT.IS_NOT_REPRESENTATIVE',
            },
          ]);
        }
      });
  }
}
