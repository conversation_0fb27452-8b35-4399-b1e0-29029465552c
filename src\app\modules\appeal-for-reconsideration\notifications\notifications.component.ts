import { Component, OnInit } from '@angular/core';
import {
  Nullable,
  SeAuthService,
  SeUser,
  WebComponentsService,
} from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';
import {
  StorageService,
  CustomRouterService,
  LoginResponseService,
  PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA,
} from '@core/services';
import { AppRoutes, IdentificationType } from '@core/models';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';
import { AppelForReconsiderationService } from '../services/appel-for-reconsideration.service';
import {
  ContribuentInputData,
  ContribuentOutputData,
  DestinationOrgansT,
  OrigenEnum,
} from '@app/shared/models';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
})
export class NotificationsComponent implements OnInit {
  receivingAgency: Nullable<string>;
  receivingAgencyCode: Nullable<DestinationOrgansT>;
  // Contact Data
  contribuentWcUrlCss = environment.wcUrlContribuentCss;
  contribuentWcInput!: ContribuentInputData;

  // Constant
  PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA =
    PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA;
  OrigenEnum = OrigenEnum;

  private user: SeUser | undefined;

  constructor(
    private seAuthService: SeAuthService,
    private wcDataService: StorageService,
    private wcService: WebComponentsService,
    private customRouter: CustomRouterService,
    private procedureHeaderService: ProceduresHeaderService,
    private appelForReconsiderationService: AppelForReconsiderationService,
    private loginSrv: LoginResponseService,
  ) {
    this.wcService.setWebComponentStyle(
      this.contribuentWcUrlCss,
      'contribuent',
    );
  }

  ngOnInit(): void {
    this.user = this.seAuthService.getSessionStorageUser();

    this.setContactDataInput();

    this.setupHeader();
  }

  private setContactDataInput(): void {
    this.contribuentWcInput = {
      isThirdPerson:
        this.wcDataService.profileUser !== IdentificationType.NOM_PROPI,
      nifPassiveSubject: this.user?.nif || '',
      idPersCens: this.loginSrv.user.idPersTitular || '',
      esAtesa:
        (this.user?.esAtesa &&
          !this.wcDataService.civilServantVehicleNifRepresentant) ||
        false,
    };
  }

  private setupHeader(): void {
    if (!this.user?.nif) return;

    const vehiclesInfo =
      this.appelForReconsiderationService.getVehiclesSelectedInfo(
        this.user.nif,
      );

    this.procedureHeaderService.setupRecursHeader(vehiclesInfo?.matriculas);
  }

  onContribuentOutput($event: Event): void {
    const contribuentData = ($event as CustomEvent)
      .detail as ContribuentOutputData;

    this.wcDataService.setNotificationsData({
      ...contribuentData,
      receivingAgency: this.receivingAgency,
      receivingAgencyCode: this.receivingAgencyCode,
    });
  }

  onGoBackButtonClick(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.APPEAL_FOR_RECONSIDERATION);
  }
  onContinueButtonClick(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.RECONSIDERATION_RESUM);
  }
}
