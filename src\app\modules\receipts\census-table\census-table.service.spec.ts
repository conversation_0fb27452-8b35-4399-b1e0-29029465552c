/* eslint-disable @typescript-eslint/no-explicit-any */
import { Injectable } from '@angular/core';
import { TestBed } from '@angular/core/testing';
import { TranslateService } from '@ngx-translate/core';
import {
  type Row,
  type SeButton,
  type SeDropdownOption,
} from 'se-ui-components-mf-lib';

import { IdentificationType, type Co2User } from '@app/core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@app/core/services';
import { TagColorService } from '@app/core/services/tag-color';
import type { PadroItem, VehiclesSelectedInfo } from '@app/shared/models';
import { ReceiptsUtilsService } from '../receipts-utils.service';
import { CensusTableService } from './census-table.service';
import { PaymentsService } from '@app/shared/services';

@Injectable({
  providedIn: 'root',
})
class SpecificConfigurationMockService {
  isProvisional = false;
  currentExercise = '2025';
  inTimeToBeDeferred = true;
}

describe('CensusTableService', () => {
  let service: CensusTableService;
  let translateServiceSpy: jasmine.SpyObj<TranslateService>;
  let receiptsUtilsServiceSpy: jasmine.SpyObj<ReceiptsUtilsService>;
  let specificConfigurationMockService: SpecificConfigurationMockService;
  let tagColorServiceSpy: jasmine.SpyObj<TagColorService>;
  let storageServiceSpy: jasmine.SpyObj<StorageService>;
  let paymentServiceSpy: jasmine.SpyObj<PaymentsService>;
  let customRouterServiceSpy: jasmine.SpyObj<CustomRouterService>;
  let loginResponseServiceSpy: jasmine.SpyObj<LoginResponseService>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        CensusTableService,
        {
          provide: TranslateService,
          useValue: jasmine.createSpyObj('TranslateService', ['instant']),
        },
        {
          provide: SpecificConfigurationService,
          useClass: SpecificConfigurationMockService,
        },
        {
          provide: StorageService,
          useValue: jasmine.createSpyObj('StorageService', [
            'profileUser',
            'setVehiclesSelected',
            'clearIdTramit',
          ]),
        },
        {
          provide: TagColorService,
          useValue: jasmine.createSpyObj('TagColorService', [
            'getPlateTagColor',
            'getPlateTagValue',
            'getStatusCodeColor',
          ]),
        },
        {
          provide: ReceiptsUtilsService,
          useValue: jasmine.createSpyObj('ReceiptsUtilsService', [
            'getSelectedPlates',
            'goToResurs',
            'getVehiclesSelectedInfoByVehicles',
            'getVehiclesSelectedInfoByPlates',
          ]),
        },
        {
          provide: PaymentsService,
          useValue: {
            initPayment: jasmine.createSpy('initPayment'),
          },
        },
        {
          provide: CustomRouterService,
          useValue: {
            navigateByBaseUrl: jasmine.createSpy('navigateByBaseUrl'),
          },
        },
        {
          provide: LoginResponseService,
          useValue: { user: (): Co2User => ({}) as Co2User },
        },
      ],
    });

    service = TestBed.inject(CensusTableService);
    translateServiceSpy = TestBed.inject(
      TranslateService,
    ) as jasmine.SpyObj<TranslateService>;
    receiptsUtilsServiceSpy = TestBed.inject(
      ReceiptsUtilsService,
    ) as jasmine.SpyObj<ReceiptsUtilsService>;
    specificConfigurationMockService = TestBed.inject(
      SpecificConfigurationService,
    );
    tagColorServiceSpy = TestBed.inject(
      TagColorService,
    ) as jasmine.SpyObj<TagColorService>;
    storageServiceSpy = TestBed.inject(
      StorageService,
    ) as jasmine.SpyObj<StorageService>;
    paymentServiceSpy = TestBed.inject(
      PaymentsService,
    ) as jasmine.SpyObj<PaymentsService>;
    customRouterServiceSpy = TestBed.inject(
      CustomRouterService,
    ) as jasmine.SpyObj<CustomRouterService>;
    loginResponseServiceSpy = TestBed.inject(
      LoginResponseService,
    ) as jasmine.SpyObj<LoginResponseService>;
  });

  it('should create', () => {
    expect(service).toBeTruthy();
  });

  describe('updateOtherActionsDropdownButtonConfig', () => {
    it('should keep all the button properties other than the "disabled" property', () => {
      const expectedButtonIcon = 'expectedButtonIcon';
      const expectedButtonIconPosition = 'right';
      const expectedButtonIconSize = '1rem';
      let button: SeButton = {
        icon: expectedButtonIcon,
        iconPosition: expectedButtonIconPosition,
        iconSize: expectedButtonIconSize,
      };
      const rows: Row[] = [];

      ({ button } = service.updateOtherActionsDropdownButtonConfig(
        button,
        rows,
        {} as any,
      ));

      expect(button.icon).toEqual(expectedButtonIcon);
      expect(button.iconPosition).toEqual(expectedButtonIconPosition);
      expect(button.iconSize).toEqual(expectedButtonIconSize);
    });

    it('should return the button disabled if rows array is empty', () => {
      let button: SeButton = {};
      const rows: Row[] = [];

      ({ button } = service.updateOtherActionsDropdownButtonConfig(
        button,
        rows,
        {} as any,
      ));

      expect(button.disabled).toBeTrue();
    });

    it("should return the button disabled if user profile is 'LOGIN_SIMPLE'", () => {
      let button: SeButton = {};
      const rows: Row[] = [{ data: {} }];
      storageServiceSpy.profileUser = IdentificationType.LOGIN_SIMPLE;

      ({ button } = service.updateOtherActionsDropdownButtonConfig(
        button,
        rows,
        {} as any,
      ));

      expect(button.disabled).toBeTrue();
    });

    it("should return the button disabled if user profile is 'COORDINADOR'", () => {
      let button: SeButton = {};
      const rows: Row[] = [{ data: {} }];
      storageServiceSpy.profileUser = IdentificationType.COORDINADOR;

      ({ button } = service.updateOtherActionsDropdownButtonConfig(
        button,
        rows,
        {} as any,
      ));

      expect(button.disabled).toBeTrue();
    });

    it('should return the button enabled if rows array is not empty and user profile is not "LOGIN_SIMPLE" or "COORDINADOR"', () => {
      let button: SeButton = {};
      const rows: Row[] = [{ data: {} }];
      storageServiceSpy.profileUser = IdentificationType.NOM_PROPI;

      ({ button } = service.updateOtherActionsDropdownButtonConfig(
        button,
        rows,
        {} as any,
      ));

      expect(button.disabled).toBeFalse();
    });

    it('should return contain the action: "Presentar recurs" and "Ajornar o fraccionar"', () => {
      const button: SeButton = {};
      const rows: Row[] = [{ data: {} }];
      storageServiceSpy.profileUser = IdentificationType.NOM_PROPI;
      const expectedLabel = 'Presentar recurs';
      const expectedLabelTranslationKey = 'SE_PADRO_CO2.BUTTONS.SUBMIT_RECURS';
      translateServiceSpy.instant.and.returnValue(expectedLabel);

      const { actions } = service.updateOtherActionsDropdownButtonConfig(
        button,
        rows,
        {} as any,
      );

      actions[0].command!({});

      expect(actions.length).toBe(2);
      expect(actions[0].label).toBe(expectedLabel);
      expect(translateServiceSpy.instant).toHaveBeenCalledWith(
        expectedLabelTranslationKey,
      );
      expect(receiptsUtilsServiceSpy.getSelectedPlates).toHaveBeenCalledTimes(
        1,
      );
      expect(receiptsUtilsServiceSpy.goToResurs).toHaveBeenCalledTimes(1);
    });
  });

  describe('getTableRowPadroData', () => {
    it('should add "Presentar recurs" and "Ajornar o fraccionar" actions to the actions column of each row', () => {
      specificConfigurationMockService.isProvisional = false;
      tagColorServiceSpy.getPlateTagColor.and.returnValue('info');
      tagColorServiceSpy.getPlateTagValue.and.returnValue('');
      tagColorServiceSpy.getStatusCodeColor.and.returnValue('success');
      translateServiceSpy.instant.and.callFake((value) => value);
      storageServiceSpy.profileUser = IdentificationType.NOM_PROPI;
      const expectedVehiclesSelectedInfo: VehiclesSelectedInfo = {
        provisional: false,
        matriculas: ['matricula-test'],
        exercici: '2025',
        nifTitular: 'nif-titular-test',
        idPersTitular: 'id-pers-titular-test',
        tipusAccess: IdentificationType.NOM_PROPI,
      };
      receiptsUtilsServiceSpy.getSelectedPlates.and.returnValue(
        expectedVehiclesSelectedInfo,
      );
      const data: PadroItem[] = [
        {
          nifTitular: 'nif-titular-test',
          quota: 123,
          nou: false,
          modificat: false,
          domicilat: false,
          matricula: 'matricula-test',
          vehicle: 'vehicle-test',
          codiSituacio: '4',
          situacio: 'Pagat telemàticament',
          exercici: '2023',
          provisional: false,
          idDeute: 'id-deute-test',
        },
      ];
      const situationOptions: SeDropdownOption[] = [
        { id: '1', label: 'Ajornat', value: '1' },
        { id: '7', label: 'Anul·lat', value: '7' },
        { id: '3', label: 'Domiciliat', value: '3' },
        { id: '100', label: 'En executiva', value: '100' },
        { id: '2', label: 'Fraccionat', value: '2' },
        { id: '6', label: 'Pagament pendent', value: '6' },
        { id: '5', label: 'Pagat', value: '5' },
        { id: '4', label: 'Pagat telemàticament', value: '4' },
      ];

      const expectedRows: Row[] = [
        {
          data: {
            plate: {
              value: 'matricula-test',
              cellConfig: { tagCell: { tagTheme: 'info' }, tagValue: '' },
            },
            carType: { value: 'vehicle-test' },
            domiciled: { value: false },
            situation: {
              value: 'Pagat telemàticament',
              cellConfig: { tagCell: { tagTheme: 'success' } },
              cellComponentName: 'tagCellComponent',
            },
            codiSituacio: { value: '4' },
            shares: { value: 123 },
            nou: { value: false },
            modificat: { value: false },
            idDeute: { value: 'id-deute-test' },
            actions: {
              value: '',
              cellComponentName: 'iconActionsCellComponent',
              cellConfig: {
                ellipsis: true,
                iconActions: {
                  icons: [
                    {
                      title: 'SE_PADRO_CO2.BUTTONS.DETAIL',
                      label: 'SE_PADRO_CO2.BUTTONS.DETAIL',
                      ariaLabel: 'SE_PADRO_CO2.BUTTONS.DETAIL',
                      name: 'matRemoveRedEyeOutline',
                      command: jasmine.any(Function),
                    },
                  ],
                  button: {
                    btnTheme: 'onlyText',
                    icon: 'matMoreVertSharp',
                    size: 'small',
                    ariaLabel: 'SE_PADRO_CO2.BUTTONS.MORE_ACTIONS',
                  },
                  buttonActions: [
                    {
                      label: 'SE_PADRO_CO2.BUTTONS.DETAIL',
                      command: jasmine.any(Function),
                    },
                    {
                      label: 'SE_PADRO_CO2.BUTTONS.SUBMIT_RECURS',
                      command: jasmine.any(Function),
                    },
                    {
                      label: 'SE_PADRO_CO2.BUTTONS.SUBMIT_DEFERRAL',
                      command: jasmine.any(Function),
                    },
                  ],
                },
              },
            },
          },
        },
      ];

      const rows = service.getTableRowPadroData(data, situationOptions);

      rows[0].data?.['actions'].cellConfig?.[
        'iconActions'
      ]?.buttonActions[1].command({ item: expectedRows[0] });

      expect(rows).toEqual(expectedRows);
      expect(
        receiptsUtilsServiceSpy.getSelectedPlates,
      ).toHaveBeenCalledOnceWith([expectedRows[0]]);
      expect(receiptsUtilsServiceSpy.goToResurs).toHaveBeenCalledOnceWith(
        expectedVehiclesSelectedInfo,
      );
    });
  });

  describe('goToPayment', () => {
    let row: any;

    beforeEach(() => {
      spyOn(service as any, 'getVehicleSelectedByRow').and.callThrough();
    });

    it('should call initPayment with VehiclesSelectedInfo when row has plate and idDeute', () => {
      row = {
        plate: { value: '1234ABC' },
        idDeute: { value: 'id-deute-test' },
        shares: { value: 100 },
      };
      const expectedVehiclesSelectedInfo: VehiclesSelectedInfo = {
        provisional: false,
        vehicles: [
          {
            matricula: '1234ABC',
            exercici: '2025',
            idDeute: 'id-deute-test',
            quota: 100,
          },
        ],
        exercici: '2025',
        nifTitular: 'nif-titular-test',
        idPersTitular: 'id-pers-titular-test',
        tipusAccess: IdentificationType.NOM_PROPI,
      };
      receiptsUtilsServiceSpy.getVehiclesSelectedInfoByVehicles.and.returnValue(
        expectedVehiclesSelectedInfo,
      );

      (service as any).goToPayment(row);

      expect((service as any).getVehicleSelectedByRow).toHaveBeenCalledWith(
        row,
      );
      expect(paymentServiceSpy.initPayment).toHaveBeenCalledWith(
        expectedVehiclesSelectedInfo,
      );
    });

    it('should not call initPayment when getVehicleSelectedByRow returns null', () => {
      row = {
        plate: { value: null },
        idDeute: { value: null },
      };

      (service as any).goToPayment(row);

      expect(paymentServiceSpy.initPayment).not.toHaveBeenCalled();
    });
  });

  describe('goToDetail', () => {
    it('should navigate to detail route with correct queryParams', () => {
      const row: any = {
        situation: { value: 'situacio-test' },
        plate: { value: 'matricula-test' },
        codiSituacio: { value: 'codiSituacio-test' },
      };

      (loginResponseServiceSpy as any).user = {
        idPersTitular: 'idPersTitular-test',
        tipusAccess: 'tipusAccess-test' as any,
      };

      service.goToDetail(row);

      expect(customRouterServiceSpy.navigateByBaseUrl).toHaveBeenCalledOnceWith(
        'detall-padro',
        {
          queryParams: {
            idPersTitular: 'idPersTitular-test',
            matricula: 'matricula-test',
            exercici: specificConfigurationMockService.currentExercise,
            provisional: specificConfigurationMockService.isProvisional,
            situacio: 'situacio-test',
            tipusAccess: 'tipusAccess-test',
            codiSituacio: 'codiSituacio-test',
          },
        },
      );
    });
  });
  describe('setRequestToGoAllegations', () => {
    let menu: any;
    let goToAllegationsSpy: jasmine.Spy;

    beforeEach(() => {
      goToAllegationsSpy = spyOn(service as any, 'goToAllegations');
    });

    it('should call goToAllegations with plate value from menu item', () => {
      menu = {
        item: {
          data: {
            plate: { value: '1234ABC' },
          },
        },
      };

      (service as any).setRequestToGoAllegations(menu);

      expect(goToAllegationsSpy).toHaveBeenCalledOnceWith(['1234ABC']);
    });

    it('should call goToAllegations with empty string if plate value is missing', () => {
      menu = {
        item: {
          data: {
            plate: { value: undefined },
          },
        },
      };

      (service as any).setRequestToGoAllegations(menu);

      expect(goToAllegationsSpy).toHaveBeenCalledOnceWith(['']);
    });

    it('should not throw if menu or item is undefined', () => {
      expect(() =>
        (service as any).setRequestToGoAllegations(undefined),
      ).not.toThrow();
      expect(() =>
        (service as any).setRequestToGoAllegations({}),
      ).not.toThrow();
    });
  });
  describe('goToAllegations', () => {
    let plates: string[];
    let vehiclesInfoMock: VehiclesSelectedInfo;

    beforeEach(() => {
      vehiclesInfoMock = {
        provisional: false,
        matriculas: ['1234ABC'],
        exercici: '2025',
        nifTitular: 'nif-titular-test',
        idPersTitular: 'id-pers-titular-test',
        tipusAccess: IdentificationType.NOM_PROPI,
      };
      receiptsUtilsServiceSpy.getVehiclesSelectedInfoByPlates.and.returnValue(
        vehiclesInfoMock,
      );
    });

    it('should call getVehiclesSelectedInfoByPlates with correct arguments', () => {
      plates = ['1234ABC'];
      (service as any).goToAllegations(plates);

      expect(
        receiptsUtilsServiceSpy.getVehiclesSelectedInfoByPlates,
      ).toHaveBeenCalledOnceWith(
        plates,
        specificConfigurationMockService.isProvisional,
        specificConfigurationMockService.currentExercise,
      );
    });

    it('should set vehicles selected in storageData', () => {
      plates = ['1234ABC'];
      (service as any).goToAllegations(plates);
      expect(storageServiceSpy.setVehiclesSelected).toHaveBeenCalledOnceWith(
        vehiclesInfoMock,
      );
    });

    it('should clear idTramit in storageData', () => {
      plates = ['1234ABC'];
      (service as any).goToAllegations(plates);
      expect(storageServiceSpy.clearIdTramit).toHaveBeenCalled();
    });

    it('should navigate to ALLEGATIONS route', () => {
      plates = ['1234ABC'];
      (service as any).goToAllegations(plates);
      expect(customRouterServiceSpy.navigateByBaseUrl).toHaveBeenCalledOnceWith(
        'allegacions',
      );
    });
  });
});
