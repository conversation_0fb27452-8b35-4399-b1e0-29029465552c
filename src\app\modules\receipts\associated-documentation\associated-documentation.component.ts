import { Component, OnInit } from '@angular/core';
import { PaginationService } from '@app/shared/services';
import { IdentificationType } from '@core/models';
import { LoginResponseService } from '@core/services';
import { DocumentsRowData } from '@shared/components';
import { IconTextComponent } from '@shared/components/cells';
import { Column, Row, SeDocumentsService } from 'se-ui-components-mf-lib';
import { AssociatedDocumentsService } from './associated-documentation-endpoint.service';
import {
  AssociatedDocuments,
  AssociatedDocumentsResponse,
} from './associated-documentation.model';

@Component({
  selector: 'app-associated-documentation-table',
  templateUrl: './associated-documentation.component.html',
  styleUrls: ['./associated-documentation.component.scss'],
})
export class AssociatedDocumentationTableComponent implements OnInit {
  private iconNames: { [key: string]: string } = {
    PDF: 'matPictureAsPdfOutline',
    //  TODO agregar imagenes y resto de textos
  };

  tableColumns: Column[] = [
    {
      header: 'SE_PADRO_CO2.LABELS.NAME',
      tooltip: false,
      tooltipText: 'SE_PADRO_CO2.LABELS.PLATE',
      key: 'name',
      cellComponent: IconTextComponent,
      resizable: true,
      cellConfig: { ellipsis: true, tooltip: true },
    },
    {
      header: 'SE_PADRO_CO2.LABELS.EXERCISE',
      tooltip: false,
      tooltipText: 'SE_PADRO_CO2.LABELS.DOMICILED',
      key: 'exercise',
      cellComponentName: 'defaultCellComponent',
      resizable: true,
      cellConfig: {
        ellipsis: true,
        align: 'center',
        tooltip: true,
      },
    },
    {
      header: 'SE_PADRO_CO2.LABELS.SIZE',
      tooltip: false,
      tooltipText: 'SE_PADRO_CO2.LABELS.VEHICLE',
      key: 'size',
      cellComponentName: 'defaultCellComponent',
      resizable: true,
      cellConfig: {
        ellipsis: true,
        tooltip: true,
        align: 'center',
      },
    },
    {
      header: 'SE_PADRO_CO2.LABELS.ACTIONS',
      tooltip: false,
      tooltipText: 'SE_PADRO_CO2.LABELS.ACTIONS',
      key: 'actions',
      id: 'actions',
      size: 10,
      resizable: true,
      cellComponentName: 'iconActionsCellComponent',
      cellConfig: {
        ellipsis: true,
        tooltip: true,
        iconActions: {
          icons: [
            {
              title: 'SE_PADRO_CO2.BUTTONS.DOWNLOAD',
              label: 'SE_PADRO_CO2.BUTTONS.DOWNLOAD',
              name: 'matFileDownloadOutline',
              command: (row: DocumentsRowData): void => {
                this.seDocumentsService.downloadJoinFiles(
                  row.ids.value as string[],
                  'documents-associats',
                );
              },
            },
          ],
        },
      },
    },
  ];

  data: Row[] = [];

  currentPage: number = 0;
  protected itemsPerPage = this.paginationSrv.getItemsPerPage();
  protected rowsPerPageOptions = this.paginationSrv.getRowsPerPageOptions();

  constructor(
    private loginService: LoginResponseService,
    private associatedDocumentsService: AssociatedDocumentsService,
    private seDocumentsService: SeDocumentsService,
    private paginationSrv: PaginationService,
  ) {}

  ngOnInit(): void {
    this.getDocumentation();
  }

  private getDocumentation(): void {
    const user = this.loginService.user;
    this.associatedDocumentsService
      .getAssociatedDocuments({
        tipusAccess: user.tipusAccess || IdentificationType.NOM_PROPI,
        matricula: user?.matricula || null,
        idPersTitular: user.idPersTitular as string,
      })
      .subscribe((response: AssociatedDocumentsResponse) => {
        if (response.content) this.setData(response.content);
      });
  }

  private setData(data: AssociatedDocuments[]): void {
    this.data = data.map((document: AssociatedDocuments) => ({
      rowConfig: {
        icon: this.getIconName(document.format),
        label: document.nom,
      },
      data: {
        name: {
          value: document.nom,
        },
        size: { value: `${document.tamany} MB` },
        exercise: { value: document.exercici },
        ids: { value: document.ids },
      },
    }));
  }

  private getIconName(value: string): string {
    return this.iconNames[value];
  }
}
