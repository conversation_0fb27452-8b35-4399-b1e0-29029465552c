import { ChangeDetectorRef, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { map, Observable, Subject, takeUntil } from 'rxjs';
import {
  Column,
  FileFormatsSeparation,
  iDocumentPadoct,
  Nullable,
  SeAlertMessage,
  SeAuthService,
  SeHttpResponse,
  SeModalOutputEvents,
  SeUser,
} from 'se-ui-components-mf-lib';
import { ContestableActDocument, Reasons } from '@shared/components';
import {
  CustomRouterService,
  HeaderInfoService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import {
  AppRoutes,
  DOCUMENTS_STATUS_TO_EXCLUDE,
  FunctionalModuleEnum,
  IdentificationType,
  StatusCodes,
} from '@core/models';
import {
  BankDetailInput,
  DocData,
} from '@modules/domiciliation/bank-details/model/bank-details.model';
import { PaymentsService } from '@shared/services';
import { FormGroup } from '@angular/forms';
import { ReasonAllegationEndpointService } from '@modules/allegations/reason-allegation/reason-allegation-endpoint.service';
import { DocumentsUpload, VehiclesSelectedInfo } from '@app/shared/models';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';
import { ReasRequestEndpointService } from './reas-request-endpoint.service';
import { ReasService } from '../services/reas.service';
import {
  MIN_CHARACTER_LENGTH,
  RequestUpdateResourcesTramit,
} from './reas-request.model';
import {
  AllegationsFormData,
  DocSigedaCodes,
  DocSubtypeCodes,
  FREE_TEXT_BLOCK_CONTROL_NAME,
} from '@app/shared/components/allegations-free-text';

@Component({
  selector: 'app-reason-request',
  templateUrl: './reas-request.component.html',
  styleUrls: ['./reas-request.component.scss'],
})
export class ReasRequestComponent implements OnInit, OnDestroy {
  private unsubscribe: Subject<void> = new Subject();

  protected bankDetailInput: Nullable<BankDetailInput>;

  isIbanInvalid: boolean = true;

  private iban: string | undefined;
  private dr: boolean | undefined;
  private bankDataDoc: iDocumentPadoct[] = [];

  vehiclesInfo: Nullable<VehiclesSelectedInfo>;

  documentsSigedaDescriptions: ContestableActDocument[] = [];

  reasonsData: Reasons[] | undefined;
  idTramit: Nullable<string>;
  functionalModule = FunctionalModuleEnum;
  isSuspensioAlertVisible = false;

  reasonsFormData: Nullable<AllegationsFormData>;

  allegationsDocuments: iDocumentPadoct[] = [];
  allegationFormValue: string = '';

  infoPanelAlert?: SeAlertMessage;
  showInfoPanelAlert: boolean = false;

  fileFormatSeparation: FileFormatsSeparation = FileFormatsSeparation.COMMA;
  statusesToExclude: string[] = DOCUMENTS_STATUS_TO_EXCLUDE;

  private user: SeUser | undefined;

  constructor(
    private storageData: StorageService,
    private loginSrv: LoginResponseService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private header: HeaderInfoService,
    private seAuthService: SeAuthService,
    private reasRequestEndpoints: ReasRequestEndpointService,
    private reasonsAllegationEndpoints: ReasonAllegationEndpointService,
    private customRouter: CustomRouterService,
    private paymentsSrv: PaymentsService,
    private cdr: ChangeDetectorRef,
    private procedureHeaderService: ProceduresHeaderService,
    private reasService: ReasService,
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  ngOnInit(): void {
    this.user = this.seAuthService.getSessionStorageUser();
    this.vehiclesInfo = this.vehiclesInfo =
      this.reasService.getVehiclesSelectedInfo(this.user.nif);
    this.idTramit = this.storageData.getReasVehicles().id;

    this.isSuspensioAlertVisible = this.isSomeVehicleUnpaid();

    this.procedureHeaderService.setupReasHeader(this.vehiclesInfo?.matriculas);

    if (this.idTramit) {
      this.getReasonsData(this.idTramit);

      this.getStorageData();

      this.getBankDetails(this.idTramit)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe(({ iban, dr }) => {
          this.iban = iban;
          this.dr = dr;
          this.setBankDetails({ iban, dr });
        });
    } else {
      this.onGoBackButtonClick();
    }
  }

  private getStorageData(): void {
    const reasonFormData = this.storageData.getReasonsSelected();

    if (reasonFormData) {
      this.allegationFormValue =
        (reasonFormData[FREE_TEXT_BLOCK_CONTROL_NAME] as string) || '';
    }
  }

  private isSomeVehicleUnpaid(): boolean {
    return this.storageData
      .getReasVehicles()
      ?.validation?.vehiclesRecurribles?.some(
        ({ codiSituacio }) =>
          codiSituacio !== StatusCodes.Paid &&
          codiSituacio !== StatusCodes.Paid_telematically,
      );
  }

  private getBankDetails(
    idTramit: string,
  ): Observable<{ iban: string | undefined; dr: boolean | undefined }> {
    return this.reasRequestEndpoints.getReasResum(idTramit).pipe(
      map((resum) => ({
        iban: resum?.ibanNumeroCompte,
        dr: resum?.declaracioNumeroCompte,
      })),
    );
  }

  getVehiclesSelectedInfo(): VehiclesSelectedInfo | null {
    const recursResponse = this.storageData.getReasVehicles();

    if (
      !recursResponse?.validation?.vehiclesRecurribles ||
      recursResponse?.validation?.vehiclesRecurribles.length === 0
    ) {
      return null;
    }

    return {
      provisional: this.specificConfigurationSrv.isProvisional,
      matriculas: recursResponse.validation.vehiclesRecurribles.map(
        (vehicle) => vehicle.matricula,
      ),
      exercici: this.specificConfigurationSrv.currentExercise,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
      nifTitular: this.user?.nif,
    };
  }

  private getReasonsData(recursId: string): void {
    const request = recursId;
    this.reasonsAllegationEndpoints
      .getReasons(request)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((result) => {
        if (result.content) {
          this.reasonsData = result.content.motius ?? [];
          this.documentsSigedaDescriptions = result.content.documents ?? [];
          this.infoPanelAlert = this.getAlertMessageDocuments();
        }
      });
  }

  getModalTableColumns(): Column[] {
    return this.reasService.getFileModalTableColumns(
      this.documentsSigedaDescriptions,
      this.allegationsDocuments,
    );
  }

  getDocumentsTableColumns(): Column[] {
    return this.reasService.getFileTableColumns();
  }

  getAcceptedFiles(): string[] {
    return this.reasService.getAcceptedFileTypes(
      this.documentsSigedaDescriptions,
    );
  }

  getAllowedFileSize(): number {
    return this.reasService.getAllowedFileSize(
      this.documentsSigedaDescriptions,
    );
  }

  getAlertMessageDocuments(): SeAlertMessage {
    return this.reasService.getAlertMessageDocuments(
      this.documentsSigedaDescriptions,
    );
  }

  onFreeTextAreaChange(data: AllegationsFormData): void {
    this.reasonsFormData = data;
  }

  onAllegationsAddedFiles(event: Event): void {
    const documentsAdded: iDocumentPadoct[] =
      (event as CustomEvent).detail || [];

    this.allegationsDocuments = this.filterDocumentsAdded(documentsAdded);

    this.showInfoPanelAlert = this.allegationsDocuments.length === 0;
  }

  private filterDocumentsAdded(
    documentsAdded?: iDocumentPadoct[],
    filterByReclamacioJustificant = false,
  ): iDocumentPadoct[] {
    return (
      documentsAdded?.filter((doc) => {
        const isReclamatioJustificant =
          doc.codSigedaType === DocSigedaCodes.RECLAMACIO_JUSTIFICANT &&
          doc.codeDescriptionComplementary ===
            DocSubtypeCodes.RECLAMACIO_JUSTIFICANT;

        return filterByReclamacioJustificant
          ? isReclamatioJustificant
          : !isReclamatioJustificant;
      }) || []
    );
  }

  onGoBackButtonClick(): void {
    this.header.reset();
    this.storageData.clearReasonsSelected();
    this.storageData.clearResourcesTramitInfo();
    this.storageData.clearIdTramit();
    this.storageData.deleteNotificationsData();
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  private updateRecursTramit(request: RequestUpdateResourcesTramit): void {
    if (this.idTramit) {
      this.reasRequestEndpoints
        .updateResourcesTramit(this.idTramit, request)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result: SeHttpResponse) => {
          if (result.content) {
            this.customRouter.navigateByBaseUrl(AppRoutes.REAS_NOTIFICATION);
          }
        });
    }
  }

  onContinueButtonClick(): void {
    if (!this.isReasonLengthValid()) {
      this.reasService
        .openLessThanNCharacterAlertModal(MIN_CHARACTER_LENGTH)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe(() => this.scrollToAllegationsFreeTextArea());

      return;
    }
    if (this.evaluateIbanWarningModalDisplay()) {
      this.reasService
        .openAlertModal()
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result) => {
          if (result === SeModalOutputEvents.MAIN_ACTION) {
            this.configureAndSubmitTramitRequest();
          }
        });

      return;
    }

    this.configureAndSubmitTramitRequest();
  }

  private isReasonLengthValid(): boolean {
    const minCharacterLength = this.reasonsFormData?.allegationsDocuments.length
      ? 0
      : MIN_CHARACTER_LENGTH;

    return (
      (this.reasonsFormData?.allegationsFormValue[FREE_TEXT_BLOCK_CONTROL_NAME]
        .length ?? 0) >= minCharacterLength
    );
  }

  private scrollToAllegationsFreeTextArea(): void {
    const divElement: HTMLElement = document.getElementById(
      'allegations-free-textarea',
    ) as HTMLElement;
    if (divElement) {
      divElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }

  private evaluateIbanWarningModalDisplay(): boolean {
    const isNomPropi =
      this.storageData.profileUser === IdentificationType.NOM_PROPI;
    const isConveniat =
      this.storageData.profileUser === IdentificationType.CONVENIAT;
    const isRepresentant =
      this.storageData.profileUser === IdentificationType.REPRESENTATIVE;
    const isFuncionario =
      this.storageData.profileUser === IdentificationType.CIVIL_SERVANT;

    const hasPaidVehicles = !this.isSomeVehicleUnpaid();

    return (
      !this.iban &&
      ((isNomPropi && !hasPaidVehicles) ||
        isConveniat ||
        isRepresentant ||
        isFuncionario)
    );
  }

  private configureAndSubmitTramitRequest(): void {
    if (this.idTramit) {
      const request: RequestUpdateResourcesTramit = {
        motiu: {
          motiu: '0',
          submotiu: '0',
          descripcio:
            this.reasonsFormData?.allegationsFormValue[
              FREE_TEXT_BLOCK_CONTROL_NAME
            ] || '',
          documents: this.getMotiuDocuments(),
        },
        ibanNumeroCompte: this.iban || undefined,
        declaracioNumeroCompte: this.dr || undefined,
        ...(this.bankDataDoc
          ? {
              documentNumeroCompte: this.bankDataDoc?.map((doc) => ({
                id: doc.id,
                filename: doc.nom,
                documentTypeId: doc.codeDescriptionComplementary,
                description: doc.description,
                documentType: doc.nomSigedaType || '',
              }))[0],
            }
          : {}),
      };

      this.storageData.setReasonsSelected(
        this.reasonsFormData?.allegationsFormValue,
      );
      this.updateRecursTramit(request);
    }
  }
  private getMotiuDocuments(): DocumentsUpload[] {
    const freeTextDocuments: iDocumentPadoct[] = this.filterDocumentsAdded(
      this.reasonsFormData?.allegationsDocuments,
      true,
    );

    return (
      [...(this.allegationsDocuments || []), ...(freeTextDocuments || [])].map(
        (doc) => ({
          id: doc.id,
          filename: doc.nom,
          documentTypeId: doc.codeDescriptionComplementary,
          description: doc.description,
          documentType: this.reasService.getDocumentType(
            doc,
            this.documentsSigedaDescriptions,
          ),
        }),
      ) || []
    );
  }
  protected isValidateRepresentative = (): boolean =>
    this.paymentsSrv.isValidateRepresentative();

  protected onBankDetailChange(event: Event): void {
    const customEvent = event as CustomEvent<FormGroup>;
    this.isIbanInvalid = customEvent.detail.invalid;
    this.iban = customEvent.detail.value.iban;
    this.bankDataDoc = this.iban ? customEvent.detail.value.bankDataDoc : [];
    this.dr = customEvent.detail.value.DR;

    this.cdr.detectChanges();
  }

  private setBankDetails({
    iban,
    dr,
  }: {
    iban: string | undefined;
    dr: boolean | undefined;
  }): void {
    const isIbanRequired = this.validateIbanRequired();
    const isPresenter =
      this.storageData.profileUser !== IdentificationType.CONVENIAT;
    const docData: DocData = {
      entityId: this.idTramit || '',
      idFunctionalModule: this.functionalModule.REAS,
      lstCodGTATSigedaType: ['TD11-021'],
      subtypes: ['0'],
    };

    this.bankDetailInput = this.paymentsSrv.getBankDetailInput({
      iban,
      dr,
      isIbanRequired,
      docData,
      isPresenter,
    });
  }

  private validateIbanRequired(): boolean {
    const isNomPropi =
      this.storageData.profileUser === IdentificationType.NOM_PROPI;
    const hasPaidVehicles = !this.isSomeVehicleUnpaid();

    return isNomPropi && hasPaidVehicles;
  }
}
