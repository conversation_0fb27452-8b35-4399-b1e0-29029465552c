import { Component, Input } from '@angular/core';
import { Column } from 'se-ui-components-mf-lib';
import { GestionsService } from './gestions.service';
import { ProcessFilter } from '../models/process.model';
import { PaginationService } from '@app/shared/services';

@Component({
  selector: 'app-gestions',
  template: `
    <mf-gestions-procedures-table
      *axLazyElement
      [columns]="processColumns"
      [itemsPerPage]="itemsPerPage"
      [rowsPerPageOptions]="rowsPerPageOptions"
      [processFilters]="co2Filters"
    ></mf-gestions-procedures-table>
  `,
})
export class GestionsComponent {
  processColumns: Column[] | undefined;
  protected itemsPerPage = this.paginationSrv.getItemsPerPage();
  protected rowsPerPageOptions = this.paginationSrv.getRowsPerPageOptions();
  @Input({ required: true }) co2Filters!: ProcessFilter;

  constructor(
    private gestionsService: GestionsService,
    private paginationSrv: PaginationService,
  ) {
    this.processColumns = this.gestionsService.getProcessColumns();
  }
}
