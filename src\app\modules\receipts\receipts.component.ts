import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AppRoutes,
  DomiciledVehiclesRequest,
  IdentificationType,
} from '@core/models';
import {
  CustomRouterService,
  HeaderInfoService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { FeatureFlagService } from '@core/services/feature-flag';
import { TranslateService } from '@ngx-translate/core';
import {
  DomiciliationEndpointService,
  PadroListEndPointService,
} from '@shared/services';
import { forkJoin, Subject, take, takeUntil } from 'rxjs';
import {
  SeAlertMessage,
  SeAuthService,
  SeDeviceService,
  SeMfConfigurationService,
  SeUser,
} from 'se-ui-components-mf-lib';
import { ReceiptTabs } from './models';
import { ProcessFilter } from './models/process.model';
import { ListPadroRequest } from '@app/shared/models';
import { XatbotService } from '@app/shared/services/xatbot/xatbot.service';
import {
  XatbotAccess,
  XatbotCanal,
  XatbotTopic,
} from '@app/shared/services/xatbot/xatbot.model';
import { ReceiptsUtilsService } from './receipts-utils.service';

@Component({
  selector: 'app-receipts-identification',
  templateUrl: './receipts.component.html',
  styleUrls: ['./receipts.component.scss'],
})
export class ReceiptsComponent implements OnInit, OnDestroy {
  // ratenow
  private readonly familyProcedure = 'FAM3';
  private readonly procedureId = 'F3CAS34';

  private unsubscribe: Subject<void> = new Subject();

  exercise: string = this.specificConfigurationService.currentExercise;
  receiptTabs = ReceiptTabs;

  protected co2FiltersGestions: ProcessFilter = { co2: true };
  protected co2FiltersPagaments: ProcessFilter = { co2: true };
  protected activeTabIndex: ReceiptTabs = this.receiptTabs.CENSUS_TABLE;
  protected vehiclesAlert: SeAlertMessage | undefined;

  domiciliatedInfoMessage: string = `${this.translateService.instant('SE_PADRO_CO2.RECEIPTS.DOMICILED')}  <strong>${this.translateService.instant('SE_PADRO_CO2.RECEIPTS.BONIFICATION', { percent: this.specificConfigurationService.percentDiscount })} </strong>`;

  notificationMessage: string = `${this.translateService.instant(
    'SE_PADRO_CO2.RECEIPTS.NOTIFICATIONS',
  )}`;

  infoMessage: string[] = [];
  infoMessageTitle: string = '';
  censusTabLabel: string = '';
  censusTotalPendent: string = '';
  previousTotalPendent: string = '';

  private user: SeUser | undefined;
  private production: boolean = false;

  get isContribuent(): boolean {
    // y por login simple tbm
    return this.store.profileUser === IdentificationType.NOM_PROPI;
  }

  get provisional(): boolean {
    return this.specificConfigurationService.isProvisional;
  }

  get isFirstPerson(): boolean {
    return (
      this.loginService?.user?.tipusAccess === IdentificationType.NOM_PROPI
    );
  }

  get showRegisterDomiciliation(): boolean {
    return this.featureFlagSrv.registerDomiciliation;
  }

  get showModifyDomiciliation(): boolean {
    return this.featureFlagSrv.modifyDomiciliation;
  }

  get currentExercise(): string {
    return this.specificConfigurationService.currentExercise;
  }

  get isLoginSimple(): boolean {
    return this.store.profileUser === IdentificationType.LOGIN_SIMPLE;
  }

  get isCivilServant(): boolean {
    return this.store.loginCase === IdentificationType.CIVIL_SERVANT;
  }

  get isRepresentant(): boolean {
    return this.store.loginCase === IdentificationType.REPRESENTATIVE;
  }

  get isCoordinador(): boolean {
    return this.store.loginCase === IdentificationType.COORDINADOR;
  }

  get isConveniat(): boolean {
    return this.store.loginCase === IdentificationType.CONVENIAT;
  }

  get isProvisional(): boolean {
    return this.specificConfigurationService.isProvisional;
  }

  get inElMeuEspai(): boolean {
    return window.location.href.includes('/el-meu-espai-atc/');
  }

  get outOfTimeToBeDomiciled(): boolean {
    return this.specificConfigurationService.outOfTimeToBeDomiciled;
  }

  get domiciliatedInfoMessageTitle(): string {
    return this.outOfTimeToBeDomiciled
      ? 'SE_PADRO_CO2.RECEIPTS.DOMICILIATE_CO2_RECEIPTS_OUT_OF_TIME'
      : 'SE_PADRO_CO2.RECEIPTS.DOMICILIATE_CO2_RECEIPTS';
  }

  constructor(
    private translateService: TranslateService,
    private specificConfigurationService: SpecificConfigurationService,
    private loginService: LoginResponseService,
    private header: HeaderInfoService,
    private store: StorageService,
    private seAuthService: SeAuthService,
    private domiciliationSrv: DomiciliationEndpointService,
    private featureFlagSrv: FeatureFlagService,
    private customRouter: CustomRouterService,
    private listPadroEndpointService: PadroListEndPointService,
    private xatbotSrv: XatbotService,
    private devSrv: SeDeviceService,
    private mfConfSrv: SeMfConfigurationService,
    private receiptsUtilService: ReceiptsUtilsService,
  ) {
    this.header.setFullscreenMode(false);
    this.user = this.seAuthService.getSessionStorageUser();
    this.production = this.user.environment === 'pro';

    const loginUser = this.loginService.user;

    this.co2FiltersPagaments = {
      co2: true,
      idPersCensSp: this.loginService.getUserId(),
      contribuentNifSp: loginUser?.nifTitular || this.user?.nif,
    };

    this.co2FiltersGestions = {
      ...this.co2FiltersPagaments,
      ...(this.isConveniat
        ? { idPersCensPresentador: this.loginService.getPresenterId() }
        : {}),
    };
  }

  ngOnInit(): void {
    this.setUpHeader();

    if (this.store.tabSelectedInReceiptPage) {
      this.onTabChange(this.store.tabSelectedInReceiptPage);
    }
    this.infoMessage = this.isProvisional
      ? this.getInfoMessageProvisional()
      : this.getInfoMessageDefinitive();
    this.censusTabLabel = this.getCensusTabLabel();

    this.getTotalPendents();

    this.setupVehiclesAlert();

    this.setupSurvey(); // setear datos de la encuesta de la lista de padron

    this.getFeatureFlagDomiciliation(); // para mostrar u ocultar las puertas a domiciliaciones

    const esApp = this.mfConfSrv.getIsMobileApp();

    // Cargamos el Xatbot para los contribuyentes
    if (
      !this.user?.esLoginGicar &&
      !this.user?.esAtesa &&
      !this.user?.esCoordinador &&
      !this.user?.es012
    )
      this.xatbotSrv.loadScript(
        this.devSrv.isMobileOrTablet()
          ? esApp
            ? XatbotCanal.WEB_APP
            : XatbotCanal.WEB_MOBILE
          : XatbotCanal.WEB_DESKTOP,
        XatbotTopic.LISTADO,
        this.isLoginSimple ? XatbotAccess.SIMPLE_LOGIN : XatbotAccess.LOGIN_AOC,
        this.production,
      );
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private setUpHeader(): void {
    this.header.reset();
    this.header.show();
    this.header.setTitleHeader(
      this.translateService.instant('SE_PADRO_CO2.APP_TITLE'),
      this.translateService.instant('SE_PADRO_CO2.APP_TITLE_MOBILE'),
    );

    if (this.inElMeuEspai) {
      this.header.setStyleClass('header--el-meu-espai');

      return;
    }

    if (this.isLoginSimple) {
      return;
    }

    if (this.isCivilServant) {
      this.header.setCivilServantHeader(true);

      return;
    }

    if (this.isRepresentant) {
      this.header.setRepresentantHeader(true);

      return;
    }

    if (this.isConveniat) {
      this.header.setConveniantHeader(true);

      return;
    }

    if (this.isCoordinador) {
      this.header.setCoordinatorHeader(
        this.store.coordinadorNifContribuent,
        true,
      );

      return;
    }
  }

  protected onTabChange(index: number): void {
    this.activeTabIndex = index;
    this.store.tabSelectedInReceiptPage = index;

    this.getTotalPendents();
  }

  protected navigateToCreateDomiciliation(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.DOMICILIATION);
  }

  protected navigateToIdCat(): void {
    window.open('https://idcatmobil.cat/', '_blank');
  }

  protected navigateToUpdateDomiciled(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.MODIFY_DOMICILED);
  }

  // en esta pantalla, si es coordinador o login simple no se muestra pagos ni gestiones
  protected isTabVisible = (): boolean => {
    return this.store.profileUser !== IdentificationType.LOGIN_SIMPLE;
  };

  private getDomiciledVehicleBody(): DomiciledVehiclesRequest {
    return {
      idPersTitular: this.loginService.user.idPersTitular as string,
      provisional: this.specificConfigurationService.isProvisional,
      exercici: this.specificConfigurationService.currentExercise,
      matricula: this.store.licensePlate,
      tipusAccess: this.loginService.user.tipusAccess,
    };
  }

  private getFeatureFlagDomiciliation(): void {
    const body = this.getDomiciledVehicleBody();
    this.domiciliationSrv
      .getFeatureFlagDomiciliation(body)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((response) => {
        if (response?.content) {
          const { alta, modification } = response.content;
          this.featureFlagSrv.registerDomiciliation = alta;
          this.featureFlagSrv.modifyDomiciliation = modification;
        }
      });
  }

  private getTotalPendents(): void {
    const previousYears: string =
      this.specificConfigurationService?.llistaExercicis?.join(',') as string;
    const censusYear: string =
      this.specificConfigurationService?.currentExercise;

    forkJoin({
      previousTotal: this.listPadroEndpointService.getListPadroPendingPayments(
        this.getPadronListRequest(previousYears, true),
      ),
      censusTotal: this.listPadroEndpointService.getListPadroPendingPayments(
        this.getPadronListRequest(censusYear, true),
      ),
    })
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((data) => {
        if (data.previousTotal?.content?.total) {
          this.previousTotalPendent = String(data.previousTotal.content?.total);
          this.featureFlagSrv.pedingPreviousPayments =
            this.previousTotalPendent;
        }

        if (data.censusTotal?.content?.total) {
          this.censusTotalPendent = String(data.censusTotal.content?.total);
          this.featureFlagSrv.pedingCurrentPayments = this.censusTotalPendent;
        }
      });
  }

  private setupVehiclesAlert(): void {
    const censusYear: string =
      this.specificConfigurationService?.currentExercise;

    this.listPadroEndpointService
      .getListPadro(this.getPadronListRequest(censusYear))
      .pipe(take(1))
      .subscribe((response) => {
        const showSimpleLoginAlert =
          ((this.isLoginSimple || this.isConveniat) &&
            response?.content?.total &&
            response?.content?.total > 1) ||
          false;

        this.vehiclesAlert = this.receiptsUtilService.getVehiclesAlert(
          this.isLoginSimple || this.isConveniat,
          showSimpleLoginAlert,
        );
      });
  }

  private getPadronListRequest(
    year: string,
    soloPendent: boolean = false,
  ): ListPadroRequest {
    const user = this.loginService.user;
    const plateValue = this.store.licensePlate || user?.matricula || null;
    const plateLoginSimple =
      (this.isLoginSimple || this.isConveniat) && plateValue
        ? [plateValue]
        : null;

    return {
      filter: {
        tipusAccess: this.getAccessType(),
        nifTitular: null,
        idPersTitular: user.idPersTitular as string,
        quotaDes: -1,
        quotaFins: -1,
        nou: null,
        domicilat: null,
        listMatriculas: plateLoginSimple,
        situacio: null,
        exercici: year || null,
        provisional: soloPendent ? false : this.isProvisional,
        soloTotal: true,
        soloPendent: soloPendent,
      },
      options: {
        first: 0,
        rows: 10,
        sortField: null,
        sortOrder: 0,
      },
    };
  }

  private getAccessType(): IdentificationType {
    return this.loginService.user.tipusAccess || IdentificationType.NOM_PROPI;
  }

  private setupSurvey(): void {
    const eventDetail = {
      familia: this.familyProcedure,
      tramite: this.procedureId,
      querySelector: 'app-receipts-identification', // el selector del componente actual
      produccion: this.production,
    };

    setTimeout(
      () =>
        document.dispatchEvent(
          new CustomEvent('showSurveyEvent', { detail: eventDetail }),
        ),
      10,
    );
  }

  private getInfoMessageProvisional(): string[] {
    this.infoMessageTitle = 'SE_PADRO_CO2.RECEIPTS.CONSULTATION_AND_DEADLINES';

    const limitAlegations = new Date(
      this.specificConfigurationService.dates?.limitAlegacions as string,
    );

    const publicacioPadroDefinitiu = new Date(
      this.specificConfigurationService.dates
        ?.publicacioPadroDefinitiu as string,
    );

    const limitDomiciliacions = new Date(
      this.specificConfigurationService.dates?.limitDomiciliacions as string,
    );

    const fiPeriodeVoluntariPagament = new Date(
      this.specificConfigurationService.dates
        ?.fiPeriodeVoluntariPagament as string,
    );

    const currentLang = this.translateService.store.currentLang;

    return [
      ` <strong>${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.MODIFICATION_DEADLINE',
      )}</strong>${this.translateService.instant(
        this.devSrv.isMobile()
          ? 'SE_PADRO_CO2.RECEIPTS.CHECK_ACCEPTANCE_MOBILE'
          : 'SE_PADRO_CO2.RECEIPTS.CHECK_ACCEPTANCE',
        {
          dayAllegance: limitAlegations.getDate(),
          monthAllegance: limitAlegations.toLocaleString(currentLang, {
            month: 'long',
          }),
          day: publicacioPadroDefinitiu.getDate(),
          month: publicacioPadroDefinitiu.toLocaleString(currentLang, {
            month: 'long',
          }),
          year: limitAlegations.getFullYear(),
        },
      )}`,
      `<strong>${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.DOMICILIATION_DEADLINE',
      )}</strong> ${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.EXERCISE_YEAR_QUOTA',
        {
          day: limitDomiciliacions.getDate(),
          month: limitDomiciliacions.toLocaleString(currentLang, {
            month: 'long',
          }),
          year: limitDomiciliacions.getFullYear(),
        },
      )}`,
      `<strong>${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.NON_DOMICILED',
      )}</strong>${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.PAYMENT_PERIOD',
        {
          startDay: publicacioPadroDefinitiu.getDate(),
          startMonth: publicacioPadroDefinitiu.toLocaleString(currentLang, {
            month: 'long',
          }),
          endDay: fiPeriodeVoluntariPagament.getDate(),
          endMonth: fiPeriodeVoluntariPagament.toLocaleString(currentLang, {
            month: 'long',
          }),
          year: fiPeriodeVoluntariPagament.getFullYear(),
        },
      )}`,
    ];
  }

  private getInfoMessageDefinitive(): string[] {
    this.infoMessageTitle =
      'SE_PADRO_CO2.RECEIPTS.DEFINITIVE_CONSULTATION_AND_DEADLINES';

    const fiPeriodeVoluntariPagament = new Date(
      this.specificConfigurationService.dates
        ?.fiPeriodeVoluntariPagament as string,
    );

    const limitRecurs = new Date(
      this.specificConfigurationService.dates?.fiTerminiRecurs as string,
    );

    const currentLang = this.translateService.store.currentLang;

    return [
      ` <strong>${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.VOLUNTARY_PAYMENT',
      )}</strong>${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.VOLUNTARY_PAYMENT_DATES',
        {
          endDay: fiPeriodeVoluntariPagament.getDate(),
          endMonth: fiPeriodeVoluntariPagament.toLocaleString(currentLang, {
            month: 'long',
          }),
          endYear: fiPeriodeVoluntariPagament.getFullYear(),
        },
      )}`,
      `<strong>${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.DOMICILIATION_CHARGE',
      )}</strong> ${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.DOMICILIATION_CHARGE_DATES',
        {
          endDay: fiPeriodeVoluntariPagament.getDate(),
          endMonth: fiPeriodeVoluntariPagament.toLocaleString(currentLang, {
            month: 'long',
          }),
          endYear: fiPeriodeVoluntariPagament.getFullYear(),
        },
      )}`,
      `<strong>${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.RECURS_SUBMIT',
      )}</strong>${this.translateService.instant(
        'SE_PADRO_CO2.RECEIPTS.RECURS_SUBMIT_DATES',
        {
          endDay: limitRecurs.getDate(),
          endMonth: limitRecurs.toLocaleString(currentLang, {
            month: 'long',
          }),
          endYear: limitRecurs.getFullYear(),
        },
      )}`,
    ];
  }

  private getCensusTabLabel(): string {
    return (
      'SE_PADRO_CO2.RECEIPTS.' +
      (this.provisional ? 'PROVISIONAL' : 'DEFINITIVE')
    );
  }

  protected onCensusTotalPendent($event: number): void {
    this.censusTotalPendent = String($event);
  }

  protected onPreviousTotalPendent($event: number): void {
    this.previousTotalPendent = String($event);
  }
}
