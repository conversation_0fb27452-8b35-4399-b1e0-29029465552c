<div class="vehicle-selection">
  <se-alert
    *ngIf="isAnyNewVehicleSelected && inTimeToBeDomiciled"
    [type]="'warning'"
    [closeButton]="false"
    [title]="'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.ALERT' | translate"
  ></se-alert>

  <mf-contribuent-contact-data
    [data]="contactDataInput"
    (output)="onContactDataOutput($event)"
  ></mf-contribuent-contact-data>

  <div class="row">
    <div class="col-12 col-md-6 vehicle-selection__vehicles-panel">
      <se-panel
        [title]="
          'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.VEHICLES_TO_BE_REGISTERED.TITLE'
            | translate
        "
      >
        <se-table
          *ngIf="data.length; else noDataTable"
          [selectable]="true"
          [showSelectAll]="!isMobile"
          [columns]="vehiclesToDomicileColumns"
          [data]="data"
          [itemsPerPage]="itemsPerPage"
          [cellTemplatePriorityOrder]="'row-column-cell'"
          (onSelectionChange)="onSelfAssessmentSelectionChange($event)"
        ></se-table
      ></se-panel>
    </div>

    <div class="col-12 col-md-6 vehicle-selection__request-direct-debit-panel">
      <app-domiciliation-request
        [showDetail]="!!selectedVehicles.length"
        [alert]="alertDomiciliationRequest"
        [domiciliationDate]="domiciliationDate"
      ></app-domiciliation-request>
    </div>
  </div>

  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button (onClick)="goBack()" [btnTheme]="'secondary'">{{
      'SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate
    }}</se-button>

    <se-button (onClick)="onContinue()" [disabled]="!selectedVehicles.length">{{
      'SE_PADRO_CO2.BUTTONS.CONTINUE' | translate
    }}</se-button>
  </div>
</div>

<ng-template #emptyStateTemplate>
  <div class="p-4">
    <se-empty-state
      [backgroundTheme]="'primary'"
      icon="info"
      [message]="
        'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.REQUEST_DIRECT_DEBIT_PANEL.EMPTY_STATE_MESSAGE'
          | translate
      "
    >
    </se-empty-state>
  </div>
</ng-template>

<ng-template #noDataTable>
  <se-empty-state [icon]="'info'" [backgroundTheme]="'primary'" />
</ng-template>
