import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import {
  SeDocumentsService,
  SeHttpRequest,
  SeHttpService,
  SeMessageService,
  SendEmailResponse,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import {
  RequestDeleteDocuments,
  RequestDeleteFile,
  RequestGetDocuments,
  ResponseDeleteDocuments,
  ResponseDeleteFile,
  ResponseGetDocuments,
} from '../models/end-point.model';

@Injectable({
  providedIn: 'root',
})
export class EndPointService {
  constructor(
    private httpService: SeHttpService,
    private msgService: SeMessageService,
    private docsService: SeDocumentsService,
  ) {}

  // Request: POST > Get padoc documents
  getDocuments = (
    request: RequestGetDocuments,
  ): Observable<ResponseGetDocuments> => {
    this.msgService.resetMessages();

    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/criteria`,
      method: 'post',
      body: request,
    };

    return this.httpService.post(httpRequest);
  };

  // Request: Delete > Delete document
  deleteFile = (request: RequestDeleteFile): Observable<ResponseDeleteFile> => {
    this.msgService.resetMessages();

    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/${request.idDocument}`,
      method: 'delete',
    };
    return this.httpService.delete(httpRequest);
  };

  // Request: Delete > Delete documents
  deleteDocuments = (
    request: RequestDeleteDocuments,
  ): Observable<ResponseDeleteDocuments> => {
    this.msgService.resetMessages();

    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/criteria/delete`,
      method: 'post',
      body: request,
    };
    return this.httpService.post(httpRequest);
  };

  // Request: Send justificant by mail
  sendJustificant = (
    email: string,
    idsDocument: string[],
  ): Observable<SendEmailResponse> => {
    this.msgService.resetMessages();
    return this.docsService.sendEmailDocumentIds(email, idsDocument);
  };
}
