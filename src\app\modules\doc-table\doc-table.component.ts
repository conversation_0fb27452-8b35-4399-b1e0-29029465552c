import { DOCUMENT } from '@angular/common';
import { Component, EventEmitter, Inject, OnDestroy, OnInit, Output, SecurityContext } from '@angular/core';
import { DomSanitizer } from '@angular/platform-browser';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { Subject, takeUntil } from 'rxjs';
import { Column, FlattenedCell, Row } from 'se-ui-components-mf-lib';
import { DocumentsInputData, RequestDeleteDocuments, RequestDeleteFile, RequestGetDocuments } from 'src/app/core/models/end-point.model';
import { DocsService } from 'src/app/core/services/docs.service';
import { EndPointService } from 'src/app/core/services/end-points.service';
import { MfActionCourseService } from 'src/app/core/services/mf-action-course.service';
import { environment } from 'src/environments/environment';
import { AddDocLinkComponent } from './add-doc-link/add-doc-link.component';
import { AddDocModalComponent } from './add-doc-modal/add-doc-modal.component';
import { DocTableData } from 'src/app/core/models/docs.model';

@Component({
  selector: 'app-doc-table',
  templateUrl: './doc-table.component.html',
  styleUrls: ['./doc-table.component.scss'],
})

export class DocTableComponent implements OnDestroy, OnInit {

  private _docData!: DocumentsInputData;
  private _unsubscribe: Subject<void> = new Subject<void>();

  modalDocumentsWcUrlCss = environment.wcUrlDocumentsCss;

  // Table
  columns: Column[] = []
  rows: Row[] = []

  data!: DocTableData;
  @Output() output: EventEmitter<any> = new EventEmitter<any>();

  constructor(
    private modalService: NgbModal,
    private endpointService: EndPointService,
    private mfActionService: MfActionCourseService,
    private docsService: DocsService,
    private sanitizer: DomSanitizer,
    @Inject(DOCUMENT) private document: any
  ) {
    this.setWebComponentStyle(this.modalDocumentsWcUrlCss, 'mf-documents');
  }

  ngOnInit(): void {
    this.data = this.mfActionService.getData().data;
    this._docData = this.data.docData

    // Table
    this.setUpColumns()

    if(this.data?.deleteDocuments) {
      this.deleteDocuments();
    } else {
      this.getDocuments()
    }
    
  }

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }

  setUpColumns(): void {
    this.columns = this.docsService.getTableColumns(this.data)
    this.columns.push(
      {
        header: '',
        key: 'actions',
        size: 20,
        resizable: false,
        cellComponent: AddDocLinkComponent,
        cellConfig: {
          uploadDocumentCallback: (cell: FlattenedCell) => this.openAddDocModal(cell.rowData.sigedaType.value, cell.rowData.subtype.value),
          deleteDocumentCallback: (cell: FlattenedCell) => this.deleteFile(cell.rowData.id.value)
        }
      }
    )
  }

  deleteDocuments() {
    const request: RequestDeleteDocuments = {
      entityId: this._docData.entityId,
      functionalModule: this._docData.idFunctionalModule || this._docData.functionalModule,
      lstCodGTATSigedaType: this._docData?.lstCodGTATSigedaType,
    }

    this.endpointService.deleteDocuments(request)
      .pipe(takeUntil(this._unsubscribe))
      .subscribe(response => {
        if (response.content) {
          this.getDocuments();
        }
      })
  }

  getDocuments() {
    const request: RequestGetDocuments = {
      entityId: this._docData.entityId,
      key: this._docData.key,
      functionalModule: this._docData.idFunctionalModule || this._docData.functionalModule,
      lstCodGTATSigedaType: this._docData?.lstCodGTATSigedaType,
      getDocument: this._docData?.getDocument,
    }

    this.docsService.getDocuments(request).then(res => {
      this.output.emit(res?.content.documentResponseList)
      this.setUpRows(res.content?.documentResponseList, this._docData?.lstCodGTATSigedaTypeTranslations)
    })
  }

  private openAddDocModal(sigedaType: string, subtype: string) {
    // Open modal
    const modalRef = this.modalService.open(
      AddDocModalComponent,
      { size: 'lg', windowClass: '' }
    );

    // Modal input data
    modalRef.componentInstance.idEntity = this._docData.entityId;
    modalRef.componentInstance.functionalModule = this._docData.idFunctionalModule || this._docData.functionalModule;
    modalRef.componentInstance.acceptedFileSize = this.data.acceptedFileSize;
    modalRef.componentInstance.selection = this.data.selection;
    modalRef.componentInstance.documentsSelectList = this.docsService.getSigedaList(sigedaType, this._docData?.lstCodGTATSigedaTypeTranslations);
    modalRef.componentInstance.subtype = subtype

    // Modal output data
    modalRef.componentInstance.modalOutput.pipe(
      takeUntil(this._unsubscribe)
    ).subscribe(
      (output: CustomEvent) => this.handleUploadDocumentOutput(output)
    );
  }

  private handleUploadDocumentOutput(output: CustomEvent) {
    const docData = output.detail?.componentEvent;
    if (docData?.id && docData?.file) {
      this.getDocuments()
    }
  }

  private deleteFile(id: string) {
    const request: RequestDeleteFile = { idDocument: id }
    this.endpointService.deleteFile(request).pipe(
      takeUntil(this._unsubscribe)
    ).subscribe(response => {
      if (response.content) {
        this.getDocuments()
      }
    })
  }

  private setUpRows(docList: any[], translations?: {[key:string]: string}) {//iDocumentPadoct
    this.rows = []
    this._docData.subtypes?.forEach(subtype => {
      const document = docList?.find(doc => doc.codeDescriptionComplementary === subtype);
      const foundDocument = this._docData.sigedaDocumentsDescriptions?.find(
        sigeda => sigeda.subtype === subtype
      ) ?? { type: this._docData.lstCodGTATSigedaType[0], subtype: subtype }
      this.rows.push(this.docsService.setRow(foundDocument, document, translations))
    })
  }

  private setWebComponentStyle = (cssURL: string, webcomponent: string = ''): void => {
    if (this.document.getElementById(`${webcomponent}-style-css`)) return;

    const head = this.document.getElementsByTagName('head')[0];
    const style = this.document.createElement('link');
    style.id = `${webcomponent}-style-css`;
    style.rel = 'stylesheet';
    style.href = `${this.sanitizer.sanitize(SecurityContext.URL, cssURL)}`;
    head.appendChild(style);
  };
}