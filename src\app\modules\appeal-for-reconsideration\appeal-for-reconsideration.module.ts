import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonModule,
  SeLinkModule,
  SePanelModule,
  SeTableModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';

import { ReasonRequestComponent } from './reason-request/reason-request.component';
import { NotificationsComponent } from './notifications/notifications.component';
import { SummaryReconsiderationComponent } from './summary-reconsideration/summary-reconsideration.component';
import { SharedModule } from 'primeng/api';
import {
  AllegationsOptionsModule,
  RequestSummaryModule,
} from '@shared/components';
import { LazyElementsModule } from '@angular-extensions/elements';
import { environment } from '@environments/environment';
import { CivilServantRequiredDocBlockModule } from '@app/shared/components/civil-servant-required-doc-block';
import { NgIconsModule } from '@ng-icons/core';
import { matLaunchOutline } from '@ng-icons/material-icons/outline';
import { AccountNumberSummaryModule } from '@app/shared/components/account-number-summary';
import { NotificationsSummaryModule } from '@app/shared/components/notifications-summary';

const routes: Routes = [
  {
    path: '',
    component: ReasonRequestComponent,
    data: {
      title: 'SE_PADRO_CO2.ALLEGATIONS.TITLE',
      stepId: 'REPOSITION_STEP1',
      stepperId: 'REPOSITION_STEPS',
    },
  },
  {
    path: 'notificacions',
    component: NotificationsComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      stepId: 'REPOSITION_STEP2',
      stepperId: 'REPOSITION_STEPS',
    },
  },
  {
    path: 'resum',
    component: SummaryReconsiderationComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      stepId: 'REPOSITION_STEP3',
      stepperId: 'REPOSITION_STEPS',
    },
  },
];

@NgModule({
  imports: [
    CommonModule,
    SharedModule,
    NgIconsModule.withIcons({ matLaunchOutline }),
    RouterModule.forChild(routes),
    TranslateModule,
    SeButtonModule,
    SePanelModule,
    SeTableModule,
    SeAlertModule,
    SeLinkModule,
    SeAlertModule,
    AccountNumberSummaryModule,
    NotificationsSummaryModule,
    RequestSummaryModule,
    AllegationsOptionsModule,
    CivilServantRequiredDocBlockModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-pagaments-dades-bancaries',
          url: environment.wcUrlPagamentsJs,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
        {
          tag: 'mf-contribuent-notificacions',
          url: environment.wcUrlContribuentJs,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
  ],
  declarations: [
    ReasonRequestComponent,
    NotificationsComponent,
    SummaryReconsiderationComponent,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AppealForReconsiderationModule {}
