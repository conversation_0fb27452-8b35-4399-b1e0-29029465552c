import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonModule,
  SeCheckboxModule,
  SeEmptyStateModule,
  SeInputModule,
  SeLinkModule,
  SeModalModule,
  SePanelModule,
  SeRadioModule,
  SeTableModule,
  SeTagModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';
import { BankDetailsComponent } from './bank-details/bank-details.component';
import { DirectDebitSummaryComponent } from './direct-debit-summary/direct-debit-summary.component';
import { DomiciliationRequestComponent } from './domiciliation-request/domiciliation-request.component';
import { ContinueWithoutDomicilingNewVehiclesModalComponent } from './continue-without-new-vehicles-modal/continue-without-domiciling-new-vehicles.component';
import { ModificationResumeComponent } from './modification-resume/modification-resume.component';
import { ModificationComponent } from './modification/modification.component';
import { VehicleSelectionComponent } from './vehicle-selection/vehicle-selection.component';
import {
  CellsModule,
  ModalVehiclesModule,
  RequestSummaryModule,
  UserDataLoginSimpleFormModule,
} from '@shared/components';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { NoNeSimpleLoginModalComponent } from './no-ne-simple-login-modal/no-ne-simple-login-modal.component';
import { CivilServantRequiredDocBlockModule } from '@app/shared/components/civil-servant-required-doc-block';
import { ModificationBankDetailsModalComponent } from './modification/modification-bank-details-modal/modification-bank-details-modal.component';

const routes: Routes = [
  {
    path: '',
    component: VehicleSelectionComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      stepId: 'DOMICILIATION_STEP1',
      stepperId: 'DOMICILIATION_STEPS',
    },
  },
  {
    path: 'modificacio',
    component: ModificationComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      stepId: 'DOMICILED_STEP_1',
      stepperId: 'DOMICILED_MODIFICATION_STEPS',
    },
  },
  {
    path: 'modificacio/resum',
    component: ModificationResumeComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      stepId: 'DOMICILED_STEP_2',
      stepperId: 'DOMICILED_MODIFICATION_STEPS',
    },
  },
  {
    path: 'dades-bancaries',
    component: BankDetailsComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      stepId: 'DOMICILIATION_STEP2',
      stepperId: 'DOMICILIATION_STEPS',
    },
  },
  {
    path: 'resum',
    component: DirectDebitSummaryComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      stepId: 'DOMICILIATION_STEP3',
      stepperId: 'DOMICILIATION_STEPS',
    },
  },
];

@NgModule({
  imports: [
    CommonModule,
    SePanelModule,
    SeButtonModule,
    SeTableModule,
    SeEmptyStateModule,
    SeAlertModule,
    SeTagModule,
    SeRadioModule,
    SeModalModule,
    SeLinkModule,
    CivilServantRequiredDocBlockModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    CellsModule,
    SeCheckboxModule,
    ReactiveFormsModule,
    FormsModule,
    SeInputModule,
    RequestSummaryModule,
    ModalVehiclesModule,
    UserDataLoginSimpleFormModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-pagaments-dades-bancaries',
          url: environment.wcUrlPagamentsJs,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
        {
          tag: 'mf-contribuent-contact-data',
          url: environment.wcUrlContribuentJs,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  declarations: [
    VehicleSelectionComponent,
    BankDetailsComponent,
    DirectDebitSummaryComponent,
    DomiciliationRequestComponent,
    ContinueWithoutDomicilingNewVehiclesModalComponent,
    ModificationComponent,
    ModificationResumeComponent,
    NoNeSimpleLoginModalComponent,
    ModificationBankDetailsModalComponent,
  ],
  providers: [NgbActiveModal],
})
export class DomiciliationModule {}
