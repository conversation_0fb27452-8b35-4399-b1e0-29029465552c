import { SeHttpResponse } from 'se-ui-components-mf-lib';

export interface IAddressTramitRequest {
  tipusVia: string;
  codTipusVia: string;
  direccio: string;
  num: string;
  escala: string;
  pis: string;
  porta: string;
  lletra: string;
  provincia: string;
  codiProvincia: string;
  municipi: string;
  codPaisIne: string;
  codProvinciaIne: string;
  codMunicipiIne: string;
  codPostal: string;
  pais: string;
  codPais: string;
  comarca: string;
  codiComarca: string;
  codiMunicipi: string;
  tnum: string;
  codiTipusNumeracio: string;
  comaut: string;
  codComaut: string;
  longitud: string;
  latitud: string;
  utmx: string;
  utmy: string;
  idAdrecCens: string;
  compl: string;
  bloc: string;
  portal: string;
  idAnteriorAdreca: string;
  flagAdressNormalitzada: string;
  teExpedient: boolean;
  telephone: string;
  email: string;
  avis: boolean;
  actiuNE: boolean;
  cpYMunicipio: string;
  direNoConsolidat: boolean;
  idAdress: string;
  viaYPortalYPisoYLetra: string;
  idTramitacioOrigen?: string;
  idPersCens?: string;
}

export class AddressTramitRequest implements IAddressTramitRequest {
  tipusVia: string;
  codTipusVia: string;
  direccio: string;
  num: string;
  escala: string;
  pis: string;
  porta: string;
  lletra: string;
  provincia: string;
  codiProvincia: string;
  municipi: string;
  codPaisIne: string;
  codProvinciaIne: string;
  codMunicipiIne: string;
  codPostal: string;
  pais: string;
  codPais: string;
  comarca: string;
  codiComarca: string;
  codiMunicipi: string;
  tnum: string;
  codiTipusNumeracio: string;
  comaut: string;
  codComaut: string;
  longitud: string;
  latitud: string;
  utmx: string;
  utmy: string;
  idAdrecCens: string;
  compl: string;
  bloc: string;
  portal: string;
  idAnteriorAdreca: string;
  flagAdressNormalitzada: string;
  teExpedient: boolean;
  telephone: string;
  email: string;
  avis: boolean;
  actiuNE: boolean;
  cpYMunicipio: string;
  direNoConsolidat: boolean;
  idAdress: string;
  viaYPortalYPisoYLetra: string;
  teJson: boolean;
  idTramitacioOrigen: string;
  idPersCens: string;

  constructor(params: Partial<IAddressTramitRequest>) {
    this.tipusVia = this.checkNull(params.tipusVia);
    this.codTipusVia = this.checkNull(params.codTipusVia);
    this.direccio = this.checkNull(params.direccio);
    this.num = this.checkNull(params.num);
    this.escala = this.checkNull(params.escala);
    this.pis = this.checkNull(params.pis);
    this.porta = this.checkNull(params.porta);
    this.lletra = this.checkNull(params.lletra);
    this.provincia = this.checkNull(params.provincia);
    this.codiProvincia = this.checkNull(params.codiProvincia);
    this.municipi = this.checkNull(params.municipi);
    this.codPaisIne = this.checkNull(params.codPaisIne);
    this.codProvinciaIne = this.checkNull(params.codProvinciaIne);
    this.codMunicipiIne = this.checkNull(params.codMunicipiIne);
    this.codPostal = this.checkNull(params.codPostal);
    this.pais = this.checkNull(params.pais);
    this.codPais = this.checkNull(params.codPais);
    this.comarca = this.checkNull(params.comarca);
    this.codiComarca = this.checkNull(params.codiComarca);
    this.codiMunicipi = this.checkNull(params.codiMunicipi);
    this.tnum = this.checkNull(params.tnum);
    this.codiTipusNumeracio = this.checkNull(params.codiTipusNumeracio);
    this.comaut = this.checkNull(params.comaut);
    this.codComaut = this.checkNull(params.codComaut);
    this.longitud = this.checkNull(params.longitud);
    this.latitud = this.checkNull(params.latitud);
    this.utmx = this.checkNull(params.utmx);
    this.utmy = this.checkNull(params.utmy);
    this.idAdrecCens = this.checkNull(params.idAdrecCens);
    this.compl = this.checkNull(params.compl);
    this.bloc = this.checkNull(params.bloc);
    this.portal = this.checkNull(params.portal);
    this.idAnteriorAdreca = this.checkNull(params.idAnteriorAdreca);
    this.flagAdressNormalitzada = '';
    this.teExpedient = this.checkBooleanNull(params.teExpedient);
    this.telephone = this.checkNull(params.telephone);
    this.email = this.checkNull(params.email);
    this.avis = this.checkBooleanNull(params.avis);
    this.actiuNE = this.checkBooleanNull(params.actiuNE);
    this.cpYMunicipio = this.checkNull(params.cpYMunicipio);
    this.direNoConsolidat = this.checkBooleanNull(params.direNoConsolidat);
    this.idAdress = this.checkNull(params.idAdress);
    this.viaYPortalYPisoYLetra = this.checkNull(params.viaYPortalYPisoYLetra);
    this.teJson = true;
    this.idTramitacioOrigen = this.checkNull(params.idTramitacioOrigen);
    this.idPersCens = this.checkNull(params.idPersCens);
  }

  checkNull(data: string | undefined): string {
    return data ?? '';
  }

  checkBooleanNull(data: boolean | undefined): boolean {
    return data ?? false;
  }
}

export interface AddressTramitResponse extends SeHttpResponse {
  content: AddressTramit;
}

export interface AddressTramit {
  idDoc: string;
  csv: string;
  tipus: TipusTramitT;
}

export enum TipusTramit {
  ADDRESS_NOTIFICACION = 'ADDRESS_NOTIFICACION',
  ADDRESS_FISCAL = 'ADDRESS_FISCAL',
  NE = 'NE',
  CO2 = 'CO2',
  REPRESENTANT = 'REPRESENTANT',
  REPRESENTAT = 'REPRESENTAT',
}

export type TipusTramitT = keyof typeof TipusTramit;
