@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

.info-message {
  border: solid 3px var(--color-blue-300);
  border-radius: 8px;

  .text-list {
    line-height: var(--line-base);
    size: 16px;
  }

  svg,
  ng-icon {
    position: absolute;
  }

  span {
    white-space: pre-line;
  }

  .title {
    font-size: var(--text-base);
    font-weight: var(--font-semibold);
    line-height: var(--line-base);
  }

  &__left-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .calendar-icon {
      color: var(--color-blue-600);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      &.img {
        width: 80px;
        height: 40px;
      }
    }
  }

  @include media-breakpoint-down(md) {
    &__left-content {
      display: none;
    }
  }

  &__content {
    justify-content: center;
    display: flex;
    flex-direction: column;
  }

  &__list {
    list-style-type: disc;
  }

  &__img {
    width: 80px;
    height: 40px;
  }
}
