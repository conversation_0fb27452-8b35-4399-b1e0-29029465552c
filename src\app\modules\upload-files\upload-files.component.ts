import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import {
  RequestDeleteDocuments,
  RequestDeleteFile,
  RequestGetFiles,
  ResponseGetFiles,
  TemplateFile,
} from '@app/shared/models';
import {
  UploadFilesEndpointsService,
  UploadFilesService,
} from '@app/shared/services';
import { TranslateService } from '@ngx-translate/core';
import { Observable, Subject, take, takeUntil } from 'rxjs';

import {
  AttachFile,
  Column,
  FileFormatsSeparation,
  iDocumentPadoct,
  RequestDownloadFile,
  SeAlertMessage,
  SeButton,
  SeDocumentsService,
  SeModal,
  SeModalService,
  SeSpinnerService,
  type Nullable,
} from 'se-ui-components-mf-lib';
import {
  FunctionalModuleEnumT,
  SeDocument,
} from 'src/app/core/models/docs.model';
import { UploadModalComponent } from './upload-modal/upload-modal.component';

@Component({
  selector: 'app-upload-file',
  templateUrl: './upload-files.component.html',
  styleUrls: ['./upload-files.component.scss'],
})
export class UploadFilesComponent implements OnDestroy {
  @Input() id?: string;
  @Input() uploadFilesSynch = true;
  @Input() set deleteAllTableFilesByWcComponentId$(value: Observable<string>) {
    if (!this._deleteAllTableFilesByComponentId$ && value) {
      this._deleteAllTableFilesByComponentId$ = value;

      this._deleteAllTableFilesByComponentId$
        .pipe(takeUntil(this.unsubscribe))
        .subscribe({
          next: (subscriptionWebComponentId) => {
            this._subscriptionWebComponentId = subscriptionWebComponentId;
            this.deleteFiles();
          },
          complete: () => this.deleteFiles(),
        });
    }
  }

  private _deleteAllTableFilesByComponentId$?: Observable<string>;
  private _subscriptionWebComponentId?: string;

  @Input() set deleteFileByDocId$(value: Observable<string>) {
    if (!this._deleteFileByDocId$ && value) {
      this._deleteFileByDocId$ = value;

      this._deleteFileByDocId$.pipe(takeUntil(this.unsubscribe)).subscribe({
        next: (idDocument) => this.onDeleteFile(idDocument),
      });
    }
  }

  private _deleteFileByDocId$?: Observable<string>;

  @Input() statusesToExclude: string[] | undefined;

  @Input() set loadFiles$(value: Observable<string>) {
    if (!this._loadFiles$ && value) {
      this._loadFiles$ = value;

      this._loadFiles$.pipe(takeUntil(this.unsubscribe)).subscribe({
        next: () => this.loadFiles(),
      });
    }
  }

  private _loadFiles$?: Observable<string>;

  @Input() hasActions = true;

  private _idEntity!: string;

  @Input({ required: true }) set idEntity(newIdEntity: string) {
    if (newIdEntity === this.idEntity) return;
    this._idEntity = newIdEntity;
    this.loadFiles();
  }

  get idEntity(): string {
    return this._idEntity;
  }

  private _sigedaDescriptions: SeDocument[] = [];

  @Input() set sigedaDescriptions(newSigedaDescriptions: SeDocument[]) {
    const areEquals =
      (!Array.isArray(newSigedaDescriptions) &&
        !Array.isArray(this.sigedaDescriptions)) ||
      (newSigedaDescriptions?.length === this.sigedaDescriptions?.length &&
        newSigedaDescriptions?.every(({ type: newType }) =>
          this.sigedaDescriptions?.some(({ type }) => newType === type),
        ));

    if (areEquals) return;
    this._sigedaDescriptions = newSigedaDescriptions;
    this.loadFiles();
  }

  get sigedaDescriptions(): SeDocument[] {
    return this._sigedaDescriptions;
  }

  private _key!: string;

  @Input() set key(newKey: string) {
    if (newKey === this.key) return;
    this._key = newKey;
    this.loadFiles();
  }

  get key(): string {
    return this._key;
  }

  @Input() tableColumns: Column[] = [
    {
      header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
      key: 'name',
      cellConfig: { tooltip: true, ellipsis: true },
      cellComponentName: 'defaultCellComponent',
      resizable: false,
    },
    {
      header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
      key: 'size',
      size: 10,
      cellComponentName: 'defaultCellComponent',
      resizable: false,
    },
    {
      header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
      key: 'description',
      cellComponentName: 'defaultCellComponent',
      resizable: false,
    },
  ];

  @Input() modalTableColumns: Column[] = [
    {
      header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
      key: 'name',
      cellComponentName: 'defaultCellComponent',
      cellConfig: { tooltip: true, ellipsis: true },
      resizable: true,
    },
    {
      header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
      key: 'size',
      size: 10,
      cellComponentName: 'defaultCellComponent',
      resizable: false,
    },
    {
      header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
      key: 'description',
      cellComponentName: 'inputCellComponent',
      resizable: false,
      cellConfig: {
        id: 'description',
        label: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
        disabled: false,
        showClear: false,
        editable: false,
        readOnly: false,
        filter: false,
        optionLabel: 'label',
        optionValue: 'id',
        placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
        required: true,
      },
    },
  ];

  @Input() info: string = '';

  @Input() hasModal: boolean = true;

  @Input() showInput: boolean = true;

  @Input() disabled: boolean = false;

  @Input() title: string = '';

  @Input() panelMode: boolean = false;

  @Input() panelDescription!: string;

  @Input() accept: string[] = ['*'];

  @Input() docType!: string;

  @Input() multiple: boolean = true;

  @Input() dropAreaTitlePreLinkText: string | undefined;
  @Input() dropAreaTitleLinkText: string | undefined;
  @Input() dropAreaTitlePostLinkText: string | undefined;

  @Input() fileFormatSeparation: FileFormatsSeparation =
    FileFormatsSeparation.SLASH;

  @Input() infoModalAlert: SeAlertMessage | undefined;

  @Input() infoPanelAlert: SeAlertMessage | undefined;

  @Input() useFileNameAsDescription: boolean = false;

  /**
   * Size limit per file using Kbs
   * @default 500
   */
  @Input() sizeLimitPerFile: number = 5000;

  /**
   * Size limit for the sum of all the files using Kbs
   * @default 250000
   */

  @Input() groupSizeLimit: number = 25000;

  @Input() maxFiles: number = 25;

  @Input() set documentTemplates(value: TemplateFile[]) {
    this._documentTemplates = value.map((data: TemplateFile) =>
      this.uploadService.createEmptyFile(data),
    );

    this.loadFiles();
  }

  private _documentTemplates: AttachFile[] = [];
  private _idFunctionalModule: Nullable<FunctionalModuleEnumT>;

  @Input({ required: true }) set idFunctionalModule(
    newIdFunctionalModule: Nullable<FunctionalModuleEnumT>,
  ) {
    if (newIdFunctionalModule === this.idFunctionalModule) return;
    this._idFunctionalModule = newIdFunctionalModule;
    this.loadFiles();
  }

  get idFunctionalModule(): Nullable<FunctionalModuleEnumT> {
    return this._idFunctionalModule;
  }

  @Input() subtitleText: string | undefined;

  @Input() set required(value: boolean) {
    this._required = value;
    this.cdr.detectChanges();
  }

  _required = true;

  get required(): boolean {
    return this._required;
  }

  get hasFiles(): boolean {
    return !!this.files && this.files.length > 0;
  }

  @Input() set uploadCustomFile(file: iDocumentPadoct) {
    if (file) {
      this.uploadFilesEndpointsService
        .uploadFile(this.uploadService.mapIdDocumentPadocDocToRequest(file))
        .pipe(takeUntil(this.unsubscribe))
        .subscribe({
          next: () => {
            this.loadFiles();
          },
        });
    }
  }

  @Output() addedFiles: EventEmitter<iDocumentPadoct[]> = new EventEmitter<
    iDocumentPadoct[]
  >();

  @Output() addedFilesFiltered: EventEmitter<iDocumentPadoct[]> =
    new EventEmitter<iDocumentPadoct[]>();

  files: AttachFile[] = [];

  protected openModalButton: SeButton = {
    label: this.translate.instant('SE_DOCUMENTS_MF.TABLE_UPLOAD_FILES.UPLOAD'),
    disabled: false,
  };

  private unsubscribe: Subject<void> = new Subject<void>();

  constructor(
    private uploadService: UploadFilesService,
    private uploadFilesEndpointsService: UploadFilesEndpointsService,
    private seDocumentsService: SeDocumentsService,
    private spinnerService: SeSpinnerService,
    private cdr: ChangeDetectorRef,
    private translate: TranslateService,
    private modalService: SeModalService,
  ) {
    console.log(
      'Webcomponent: SE Documents > UploadFilesComponentMf > constructor',
    );
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  protected onFilesLoaded(files: AttachFile[]): void {
    if (this.uploadFilesSynch) {
      this.uploadSyncFiles(files);
    } else {
      this.uploadAsyncFiles(files);
    }
  }

  private uploadSyncFiles(files: AttachFile[]): void {
    if (!this.idEntity || !this.idFunctionalModule) return;

    this.spinnerService.enableSpinnerManualStopMode();

    this.uploadService
      .uploadSyncFiles(
        files,
        this.idEntity,
        this.key,
        this.idFunctionalModule,
        this.docType,
        this.sigedaDescriptions,
      )
      .pipe(takeUntil(this.unsubscribe))
      .subscribe({
        complete: () => {
          this.spinnerService.stopAndDisableManualStopMode();

          this.loadFiles();
        },
      });
  }

  private uploadAsyncFiles(files: AttachFile[]): void {
    if (!this.idEntity || !this.idFunctionalModule) return;

    this.uploadService
      .uploadFiles(
        files,
        this.idEntity,
        this.key,
        this.idFunctionalModule,
        this.docType,
        this.sigedaDescriptions,
      )
      .pipe(takeUntil(this.unsubscribe))
      .subscribe({ complete: () => this.loadFiles() });
  }

  private deleteFiles(): void {
    const docIds =
      this.files?.map((file) => file.id || '').filter(Boolean) || [];

    if (docIds.length && this._subscriptionWebComponentId === this.id) {
      const request: RequestDeleteDocuments = { idsBBDD: docIds };

      this.uploadFilesEndpointsService
        .deleteFiles(request)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe({ complete: () => this.loadFiles() });
    }
  }

  onDeleteFile(idDocument: string): void {
    if (idDocument) {
      const request: RequestDeleteFile = { idDocument };
      this.uploadFilesEndpointsService
        .deleteFile(request)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe({ complete: () => this.loadFiles() });
    }
  }

  onDownloadFile(idDocument: string): void {
    if (idDocument) {
      const request: RequestDownloadFile = { id: idDocument };
      const file = this.files?.find((file) => file.id === idDocument);
      this.seDocumentsService.downloadFile(request, file?.name ?? 'file');
    }
  }

  private loadFiles(): void {
    if (!this.idEntity || !this.idFunctionalModule) return;

    const request: RequestGetFiles = {
      entityId: this.idEntity,
      idFunctionalModule: this.idFunctionalModule,
      lstCodGTATSigedaType: this.sigedaDescriptions.map((el) => el.type),
      getDocument: false,
      statusesToExclude: this.statusesToExclude,
    };

    this.uploadFilesEndpointsService
      .getFiles(request)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe({
        next: (response: ResponseGetFiles) => {
          if (response?.content) {
            this.files = [
              ...this.uploadService.formatDocumentResponseList(
                response?.content?.documentResponseList,
                this.idEntity,
                this.key,
                this.sigedaDescriptions,
              ),
            ];
            const loadedTemplates = this.getLoadedTemplates(this.files);
            this.files = [...this.files, ...loadedTemplates];

            this.addedFiles.emit(response?.content?.documentResponseList);
            this.addedFilesFiltered.emit(
              this.uploadService.filterDocumentsByEntityAndKey(
                response?.content?.documentResponseList,
                this.idEntity,
                this.key,
              ),
            );
          } else {
            this.files = [...(this.files ?? [])];
          }
        },
        error: () => {
          this.files = [...(this.files ?? [])];
        },
      });
  }

  private getLoadedTemplates(files: AttachFile[]): AttachFile[] {
    const loadedTemplates = this._documentTemplates.filter(
      (_loadedTemplates) =>
        !files?.some((file) => file.idType === _loadedTemplates.idType),
    );

    this.openModalButton.disabled =
      (this._documentTemplates.length > 0 && loadedTemplates.length === 0) ||
      this.files.length >= this.maxFiles;

    return loadedTemplates;
  }

  protected openModal(): void {
    const modalCsv: SeModal = {
      component: UploadModalComponent,
      size: 'xl',
      centered: true,
      titleTextWeight: 'regular',
      backdrop: 'static',
    };

    const modalRef = this.modalService.openModal(modalCsv).componentInstance;

    modalRef.tableColumns = this.tableColumns;
    modalRef.modalTableColumns = this.modalTableColumns;
    modalRef.hasActions = this.hasActions;
    modalRef.hasModal = this.hasModal;
    modalRef.info = this.info;
    modalRef.title = this.translate.instant(
      'SE_DOCUMENTS_MF.TABLE_UPLOAD_FILES.MODAL.DESCRIPTION',
    );
    modalRef.subtitleText = this.subtitleText;
    modalRef.accept = this.accept;
    modalRef.docType = this.docType;
    modalRef.multiple = this.multiple;
    modalRef.required = this.required;
    modalRef.disabled = this.disabled;
    modalRef.infoModalAlert = this.infoModalAlert;
    modalRef.sizeLimitPerFile = this.sizeLimitPerFile;
    modalRef.groupSizeLimit = this.groupSizeLimit;
    modalRef.maxFiles = this.maxFiles;
    modalRef.dropAreaTitlePreLinkText = this.dropAreaTitlePreLinkText;
    modalRef.dropAreaTitleLinkText = this.dropAreaTitleLinkText;
    modalRef.dropAreaTitlePostLinkText = this.dropAreaTitlePostLinkText;
    modalRef.fileFormatSeparation = this.fileFormatSeparation;

    modalRef.closeModalOutput
      .pipe(take(1))
      .subscribe((result: { files: AttachFile[] | string }) => {
        const { files } = result;
        if (files && typeof files !== 'string') {
          this.onFilesLoaded(files);
        }
      });
  }
}
