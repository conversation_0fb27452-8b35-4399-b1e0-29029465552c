import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import {
  AppRoutes,
  FunctionalModuleEnum,
  IdentificationType,
  ResourceProcessDocument,
} from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  StorageService,
} from '@core/services';
import { Subject, takeUntil } from 'rxjs';
import {
  Column,
  iDocumentPadoct,
  Nullable,
  Row,
} from 'se-ui-components-mf-lib';
import { RequestSummary } from '@shared/components';
import { ProcessNameToDisplayInPresentationReceipt } from '../../presentation-receipt/presentation-receipt.model';
import {
  AllegationPresentationRequest,
  AllegationPresentationResponse,
  AllegationResumResponse,
  ProcedureSavedAllegation,
} from './models';
import {
  ResumAllegacionsEndpointService,
  ResumAllegationService,
} from './services';
import { Motius, VehiclesSelectedInfo } from '@app/shared/models';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';

@Component({
  selector: 'app-resum-allegation',
  templateUrl: './resum-allegation.component.html',
  styleUrls: ['./resum-allegation.component.scss'],
})
export class ResumAllegationComponent implements OnInit, OnDestroy {
  data: RequestSummary | undefined;
  allegacions: Nullable<Motius[]>;
  idTramit: Nullable<string>;
  vehiclesInfo: Nullable<VehiclesSelectedInfo>;

  allegacionsColumns: Column[] = [];
  allegacionsRows: Row[] = [];

  plates: string[] = [];

  get isLoginSimple(): boolean {
    return this.loginSrv.user.tipusAccess === IdentificationType.LOGIN_SIMPLE;
  }

  get idFunctionalModule(): FunctionalModuleEnum {
    return FunctionalModuleEnum.ALLEGATIONS;
  }

  private unsubscribe: Subject<void> = new Subject();

  protected loginSimpleUserForm: FormGroup | undefined;
  protected disableContinueButton: boolean = false;

  constructor(
    private resumAllegationsService: ResumAllegationService,
    private allegationsService: ResumAllegacionsEndpointService,
    private storageData: StorageService,
    private customRouter: CustomRouterService,
    private loginSrv: LoginResponseService,
    private procedureHeaderService: ProceduresHeaderService,
  ) {
    // Intencionadamente vacío
  }

  ngOnInit(): void {
    if (this.isLoginSimple) {
      this.buildLoginSimpleUserForm();
    }

    this.idTramit = this.storageData.getIdTramit();
    this.vehiclesInfo = this.storageData.getVehiclesSelected();
    this.plates = this.vehiclesInfo?.matriculas || [];

    this.procedureHeaderService.setupAlegationsHeader(
      this.vehiclesInfo?.matriculas,
    );

    this.getAllegationVehicles();
  }

  private buildLoginSimpleUserForm(): void {
    this.loginSimpleUserForm = new FormGroup({
      name: new FormControl('', [Validators.required]),
      surname: new FormControl('', [Validators.required]),
    });
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private getAllegacionsColumns(): void {
    this.allegacionsColumns =
      this.resumAllegationsService.getAllegacionsColumns();
  }

  private getAllegacionsRows(): void {
    //TODO: Una vez tenga los datos del back, restructurar mapeo
    this.allegacionsRows = this.resumAllegationsService.getAllegacionsRows(
      this.allegacions,
    );
  }

  private getAllegationVehicles(): void {
    if (this.idTramit) {
      this.allegationsService
        .getAllegationVehicles(this.idTramit)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result: AllegationResumResponse) => {
          if (result?.content) {
            this.data =
              this.resumAllegationsService.setDataResponseInDataObject(
                result.content,
                this.vehiclesInfo,
                this.isLoginSimple,
              );
            this.allegacions = result?.content.motius;
            this.getAllegacionsColumns();
            this.getAllegacionsRows();
          }
        });
    }
  }

  onGoBackButtonClick(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.ALLEGATIONS);
  }

  onContinueButtonClick(): void {
    if (this.idTramit) {
      this.allegationsService
        .setAllegationPresentation(
          this.setAllegationPresentationRequest(this.idTramit),
        )
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result: AllegationPresentationResponse) => {
          if (result?.content) {
            this.storageData.clearReasonsSelected();
            this.storageData.clearIdTramit();
            this.setDataToShowThemInPresentationReceipt(result.content);
            this.customRouter.navigateByBaseUrl(AppRoutes.PRESENTATION_RECEIPT);
          }
        });
    }
  }

  private setAllegationPresentationRequest(
    idTramit: string,
  ): AllegationPresentationRequest {
    return {
      idAllegacio: idTramit,
      nom: this.loginSimpleUserForm?.get('name')?.value || null,
      cognoms: this.loginSimpleUserForm?.get('surname')?.value || null,
    };
  }

  private setDataToShowThemInPresentationReceipt(
    response: ProcedureSavedAllegation,
  ): void {
    this.storageData.setResourceProcessDocumentData({
      receipt: {
        idPadoct: response.idJustificant,
        nom: response.fileName,
      } as iDocumentPadoct,
      idFunctionalModule: ProcessNameToDisplayInPresentationReceipt.Allegation,
    } as ResourceProcessDocument);
    this.storageData.processNameToDisplayPresentationReceipt =
      ProcessNameToDisplayInPresentationReceipt.Allegation;
  }

  protected onDisableContinueButton($event: boolean): void {
    this.disableContinueButton = $event;
  }
}
