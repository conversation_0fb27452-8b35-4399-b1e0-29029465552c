import { SeHttpResponse } from 'se-ui-components-mf-lib';

export interface DetallPadroRequest {
  matricula: string;
  exercici: string;
  provisional: boolean;
  idPersTitular: string;
  tipusAccess: string;
  situacio: string;
  codiSituacio: string;
}

export interface DetallPadro {
  nifTitular: string;
  nomTitular: string;
  cognom1Titular: string;
  cognom2Titular: string;
  municipiTitular: string;
  municipiVehicle: string;
  codiSituacio: string;
  percentajeTitular: number;
  calcul: number;
  baseImpostable: string;
  quota: number;
  bonificacio: number;
  total: number;
  nou: boolean;
  domicilat: boolean;
  iban: string;
  domicilatSeguents: boolean;
  ibanSeguents: string;
  categoria: string;
  matricula: string;
  marca: string;
  model: string;
  co2: number;
  periode: number;
  situacio: string;
  exercici: string;
  provisional: boolean;
  exempt: boolean;
  desDe: string[];
  finsA: string[];
  numeroDies: number[];
  totalDies: number;
  popupEmissions: PopupEmmisions;
  tipusBonificacio: string;
  tipusExempcio: string;
  codiCombustible: CodiCombustible;
  combustible: string;
  cilindrada: string;
  potenciaFiscal: string;
  potenciaNeta: string;
  massaMaxima: string;
  massaOrdreMarxa: string;
  tara: string;
  dataPrimeraMatriculacio: string;
  antiguitat: string;
  hibridElectric: boolean;
}

export enum PopupEmmisions {
  FORMULA_FUEL = 'FM',
  FORMULA_REST = 'FR',
  NO_FORMULA = 'NF',
}

export enum CodiCombustible {
  DIESEL = '2',
  GASOLINA = '1',
}

export enum CodiCategory {
  M1 = 'M1',
  N1 = 'N1',
  L3E = 'L3E',
  L4E = 'L4E',
  L5E = 'L5E',
  L7E = 'L7E',
}

export interface DetallPadroResponse extends SeHttpResponse {
  content: DetallPadro;
}
