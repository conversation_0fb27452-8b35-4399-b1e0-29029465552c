import { DocumentsInputData } from './end-point.model';

export enum FunctionalModuleEnum {
  RECURS = 'RECURS',
  ALLEGATIONS = 'ALLEGATIONS',
  GARANTIA = 'GARANTIA',
  PAGAMENTS = 'PAGAMENTS',
  TAX600 = 'TAX600',
  ASSEGURANCES_DE_VIDA = 'ASSEGURANCES_DE_VIDA',
  INSPECTION_SIGNATURE = 'INSPECTION_SIGNATURE',
}
export type FunctionalModuleEnumT = keyof typeof FunctionalModuleEnum;

export interface CheckBoxOption {
  label: string;
  tooltip: boolean;
  tooltipText: string;
  id: string;
  isMultiSelectable: boolean;
  formControlName: string;
  documents: SeDocument[];
}

export interface DocTableData {
  docData: DocumentsInputData;
  deleteDocuments?: boolean;
  acceptedFileSize?: number;
  selection?: CheckBoxOption[];
}

export interface SeDocument {
  type: string;
  subtype: string;
  name?: string;
  description: string;
  required?: boolean;
  allowedFiles?: string[];
  allowedSize?: number;
}
