import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { map, Observable } from 'rxjs';
import {
  Nullable,
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import {
  CalculatePaymentRequest,
  CalculatePaymentResponse,
  GetPendingPaymentsRequest,
} from './payments-endpoints.model';

@Injectable({
  providedIn: 'root',
})
export class PaymentsEndpointsService {
  constructor(private httpService: SeHttpService) {}

  public setPayment(
    request: CalculatePaymentRequest,
  ): Observable<Nullable<CalculatePaymentResponse>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `pagaments/calculate-payment`,
      method: 'post',
      body: request,
    };
    return this.httpService
      .post<SeHttpResponse<CalculatePaymentResponse>>(httpRequest)
      .pipe(
        map(
          (response: SeHttpResponse<CalculatePaymentResponse>) =>
            response.content,
        ),
      );
  }

  public getPendingPayments(
    request: GetPendingPaymentsRequest,
  ): Observable<Nullable<boolean>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `revisar-pagaments-pendents`,
      method: 'post',
      body: request,
    };
    return this.httpService
      .post<SeHttpResponse<boolean>>(httpRequest)
      .pipe(map((response: SeHttpResponse<boolean>) => response.content));
  }
}
