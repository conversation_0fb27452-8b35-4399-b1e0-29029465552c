import { Injectable } from '@angular/core';
import {
  ConveniatVerificationResponse,
  Co2User,
  ContentSimpleLogin,
  IdentificationType,
  LoginRequest,
  LoginResponse,
  LoginUser,
  SimpleLoginResponse,
} from '@core/models';
import { Observable, tap } from 'rxjs';
import { SeDataStorageService, SeAuthService } from 'se-ui-components-mf-lib';
import { LoginResponseEndPointService } from './login-endpoint.service';
import { StorageService } from '../storage';

export const NAME_USER_CO2_STORAGE = 'se-padro-co2-user';

@Injectable({
  providedIn: 'root',
})
export class LoginResponseService {
  get user(): Co2User {
    return this.dataStorageService.getItem(NAME_USER_CO2_STORAGE) as Co2User;
  }

  constructor(
    private endpointService: LoginResponseEndPointService,
    private dataStorageService: SeDataStorageService,
    private seAuthSrv: SeAuthService,
    private store: StorageService,
  ) {}

  login(request: LoginRequest): Observable<LoginResponse> {
    return this.endpointService.login(request).pipe(
      tap((res) => {
        if (res.content) {
          const user = res.content;
          if (
            user.tipusAccess === IdentificationType.NOM_PROPI ||
            user.tipusAccess === IdentificationType.LOGIN_SIMPLE
          ) {
            const sessionUser = this.seAuthSrv.getSessionStorageUser();
            request = {
              nifTitular: sessionUser?.esJuridico
                ? sessionUser?.companyId
                : sessionUser?.nif,
              nom: sessionUser?.nombreCompleto,
            };
          }
          this.setUserDataInStorage(user, request);
        }
      }),
    );
  }

  getSmsLoginSimple(
    request: LoginRequest,
    recaptcha: string,
  ): Observable<LoginResponse> {
    return this.endpointService.getSmsLoginSimple(request, recaptcha).pipe(
      tap((res) => {
        if (res.content) this.setUserDataInStorage(res.content, request);
      }),
    );
  }

  confirmSMSCode(
    request: LoginRequest,
    recaptcha: string,
  ): Observable<SimpleLoginResponse> {
    return this.endpointService.confirmSMSCode(request, recaptcha).pipe(
      tap((res) => {
        const simpleLogin: ContentSimpleLogin | undefined = res.content;

        this.seAuthSrv.deleteSession();

        if (simpleLogin?.usuario && simpleLogin?.loginOk) {
          this.dataStorageService.setItem(NAME_USER_CO2_STORAGE, {
            ...this.user,
            ...simpleLogin,
          });

          this.seAuthSrv.setSession(simpleLogin.usuario);
        }
      }),
    );
  }

  confirmRelationNifPlate(
    nif: string,
    plate: string,
  ): Observable<ConveniatVerificationResponse> {
    return this.endpointService.confirmRelationNifPlate(nif, plate).pipe();
  }

  validateScoring(
    nif: string,
    name: string,
  ): Observable<{ match: boolean; fullName: string }> {
    return this.endpointService.getIsTaxpayerNIFRelatedToName(nif, name).pipe();
  }

  // para el key de idPersTitular en los request
  getUserId(): string {
    const user = this.seAuthSrv.getSessionStorageUser();
    return this.user?.idPersTitular || user?.idPersCensPresentador;
  }

  getPresenterId(): string {
    const user = this.seAuthSrv.getSessionStorageUser();
    return user?.idPersCensPresentador;
  }

  getNifTitular(): string {
    return this.user.nifTitular ?? this.store.taxpayerNIF;
  }

  isLegalPerson(): boolean {
    const user = this.seAuthSrv.getSessionStorageUser();
    return this.user.esTitularJuridic ?? user.esJuridico;
  }

  resetUser(): void {
    this.dataStorageService.deleteItem(NAME_USER_CO2_STORAGE);
  }

  private setUserDataInStorage = (
    user: LoginUser,
    request: LoginRequest,
  ): void => {
    this.dataStorageService.setItem(NAME_USER_CO2_STORAGE, {
      ...user,
      ...request,
    });
  };
}
