import { Component, Input, OnInit } from '@angular/core';
import {
  CellComponent,
  CellConfig,
  Column,
  FlattenedCell,
  FlattenedRow
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-add-doc-link',
  template: `
    <se-link
      (onClick)="onLinkClick()"
      target="_self"
      linkTheme="secondary"
      [disabled]="false"
    >
      <div class="doc-table-link-container">
        <i>
          <ng-icon 
            class="text-md" 
            [ngClass]="{'doc-table-link-upload-icon': !isDeleteLink}"
            [name]="linkIcon">
          </ng-icon>
        </i>
        <span>{{linkLabel | translate}}</span>
      </div>
    </se-link>
  `,
  styles: [`
    ::ng-deep se-link {
      .doc-table-link {
        &-container {
          display: inline-flex;
          gap: 8px;
        }

        &-upload-icon {
          transform: rotate(-45deg);
        }
      }
    }
  `]
})
export class AddDocLinkComponent implements CellComponent, OnInit {
  @Input() value: any;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  isDeleteLink!: boolean;
  linkLabel!: string;
  linkIcon!: string;

  ngOnInit(): void {
    this.isDeleteLink = this.row.cells.map(row => row.value).filter(Boolean).length > 2;
    this.linkLabel = this.isDeleteLink ? 'SE_DOCUMENTS_MF.DOC_TABLE.DELETE_DOCUMENT' : 'SE_DOCUMENTS_MF.DOC_TABLE.ADD_DOCUMENT';
    this.linkIcon = this.isDeleteLink ? 'matDeleteOutline' : 'matAttachmentOutline';
  }

  onLinkClick() {
    if (this.isDeleteLink) {
      this.cellConfig['deleteDocumentCallback'](this.cell);
    } else {
      this.cellConfig['uploadDocumentCallback'](this.cell);
    }
  }
}