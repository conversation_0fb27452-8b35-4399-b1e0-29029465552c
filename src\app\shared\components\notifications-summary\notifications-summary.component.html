<se-panel [title]="title | translate">
  <div
    *ngIf="
      notificationsData?.thirdPersonNotificationsMsg && !showOnlyPostalAddress;
      else notificationsDataTemplate
    "
  >
    {{ notificationsData?.thirdPersonNotificationsMsg }}
  </div>
</se-panel>

<ng-template #notificationsDataTemplate>
  <div
    *ngIf="notificationsData?.canal && !showOnlyPostalAddress"
    class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
  >
    <span class="col-md-3 col-12 fw-bold">{{
      'SE_PADRO_CO2.LABELS.CANAL' | translate
    }}</span>
    <span class="col-md-6 col-12">{{ notificationsData?.canal }}</span>
  </div>
  <div
    *ngIf="
      (!notificationsData?.address?.actiuNE || showOnlyPostalAddress) &&
      notificationsData?.postalAddress
    "
    class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
  >
    <span class="col-md-3 col-12 fw-bold">{{
      'SE_PADRO_CO2.LABELS.ADRESS' | translate
    }}</span>
    <span class="col-md-6 col-12">{{ notificationsData?.postalAddress }}</span>
  </div>

  <ng-container
    *ngIf="
      notificationsData?.address?.actiuNE &&
      notificationsData?.address?.avis &&
      !showOnlyPostalAddress
    "
  >
    <div
      *ngIf="notificationsData?.address?.email"
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        'SE_PADRO_CO2.LABELS.EMAIL' | translate
      }}</span>
      <span class="col-md-6 col-12">{{
        notificationsData?.address?.email
      }}</span>
    </div>
    <div
      *ngIf="notificationsData?.address?.telephone"
      class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
    >
      <span class="col-md-3 col-12 fw-bold">{{
        'SE_PADRO_CO2.LABELS.TELEFON' | translate
      }}</span>
      <span class="col-md-6 col-12">{{
        notificationsData?.address?.telephone
      }}</span>
    </div>
  </ng-container>
</ng-template>
