import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeButtonDropdownModule,
  SeButtonModule,
} from 'se-ui-components-mf-lib';

import { DocumentActionsComponent } from './document-actions.component';
import { DownloadDocumentModule } from '../download-document';
import { SendDocumentsButtonModule } from '../send-documents-button/send-documents-button.module';

@NgModule({
  declarations: [DocumentActionsComponent],
  imports: [
    CommonModule,
    SeButtonModule,
    SeButtonDropdownModule,
    TranslateModule,
    DownloadDocumentModule,
    SendDocumentsButtonModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [DocumentActionsComponent],
})
export class DocumentActionsModule {}
