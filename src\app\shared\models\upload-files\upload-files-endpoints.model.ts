import {
  FileUploader<PERSON>rror,
  iDocumentPadoct,
  SeHttpResponse,
} from 'se-ui-components-mf-lib';
import type { FunctionalModuleEnumT } from 'src/app/core/models';

/**
 * DOWNLOAD FILE
 */

// Request
export interface RequestDownloadFile {
  id: string;
}

// Response
export interface ResponseDownloadFile extends SeHttpResponse {
  content: iDocumentPadoct;
}

/**
 * UPLOAD FILE
 */

// Request
export interface RequestUploadFile {
  fileBase64: string;
  idFunctionalModule: FunctionalModuleEnumT;
  idEntity: string;
  key?: string;
  format: string;
  nom: string;
  codGTATSigedaType: string;
  description: string;
  csvRequired: boolean;
  origenCiutada: boolean;
  codeDescriptionComplementary?: string;
}

// Response
export interface ResponseUploadFile extends SeHttpResponse {
  content: iDocumentPadoct;
}

export interface ResponseUploadFiles extends SeHttpResponse {
  content: iDocumentPadoct[];
}
/**
 * DELETE FILE
 */

// Request
export interface RequestDeleteFile {
  idDocument: string;
}

// Response
export interface ResponseDeleteFile extends SeHttpResponse {
  content: {
    error: boolean;
  };
}

export interface RequestDeleteDocuments {
  idsBBDD: string[];
}

export interface ResponseDeleteDocuments extends SeHttpResponse {
  content: number;
}

/**
 * GET FILES
 */

// Request
export interface RequestGetFiles {
  entityId: string;
  idFunctionalModule: FunctionalModuleEnumT;
  lstCodGTATSigedaType: string[];
  getDocument?: boolean;
  statusesToExclude?: string[];
}

// Response
export interface ResponseGetFiles extends SeHttpResponse {
  content: {
    documentResponseList: iDocumentPadoct[];
    error: boolean;
  };
}

/**
 * GET LIST FILES
 */

// Request
export interface RequestGetDetailDocuments {
  filter: { entityId: string };
}

// Response
export interface ResponseGetDetailDocuments extends SeHttpResponse {
  content: iContentDetailDocuments;
}

// Retry document upload: Response
export interface IResponseRetryDocumentUpload extends SeHttpResponse {
  content: boolean;
}

export interface iContentDetailDocuments {
  idPadoctEncripted: string;
  id: string;
  idPadoct: string;
  idEntity: string;
  idFunctionalModule: FunctionalModuleEnumT;
  sigedaType: string;
  codSigedaType: string;
  nomSigedaType: string;
  documentType: string;
  format: string;
  nom: string;
  csv: string;
  indActivo: boolean;
  description: string;
  mongoParentId: string;
  evidenceType: string;
  version: string;
  requestMetadades: iMetadatos[];
  status: string | unknown;
  tries: number;
  locked: boolean;
  errors: FileUploaderError[];
}

export interface iMetadatos {
  id: string;
  valor: string;
}
