<div class="app-identification" *ngIf="isRepresentative !== undefined">
  <ng-container [ngSwitch]="currentCase">
    <!-- Sin perfil permitido por defecto -->
    <ng-template ngSwitchDefault>
      <se-alert
        [title]="'SE_PADRO_CO2.ERRORS.NO_PROFILE_ALLOWED' | translate"
        [type]="'error'"
        [closeButton]="false"
      ></se-alert>
    </ng-template>
    <ng-template [ngSwitchCase]="LOGIN_CASES.REPRESENTATIVE">
      <app-personal-identification></app-personal-identification>
    </ng-template>
    <ng-template [ngSwitchCase]="LOGIN_CASES.CONVENIAT">
      <app-conveniat-identification
        [showRepresentative]="canviarContribuent"
      ></app-conveniat-identification>
    </ng-template>
    <ng-template [ngSwitchCase]="LOGIN_CASES.CIVIL_SERVANT">
      <app-civil-servant-identification></app-civil-servant-identification>
    </ng-template>
    <ng-template [ngSwitchCase]="LOGIN_CASES.COORDINADOR">
      <app-coordinator-identification></app-coordinator-identification>
    </ng-template>
  </ng-container>
</div>
