import { Injectable } from '@angular/core';
import { MF_CO2_CONFIGURATION_STORAGE } from '@app/core/models';
import { lastValueFrom } from 'rxjs';
import { SeDataStorageService } from 'se-ui-components-mf-lib';
import { ConfigEndpointsService } from './config-endpoints.service';
import { SpecificConfigurationService } from '../specific-configuration';

@Injectable({
  providedIn: 'root',
})
export class ConfigService {
  constructor(
    private dataStorage: SeDataStorageService,
    private endpointService: ConfigEndpointsService,
    private specificConfigurationService: SpecificConfigurationService,
  ) {}

  async setCo2Configuration(): Promise<boolean> {
    const response = await lastValueFrom(this.endpointService.getCO2Config());
    this.dataStorage.setItem(MF_CO2_CONFIGURATION_STORAGE, response?.content);
    this.specificConfigurationService.setConfig();

    return true;
  }
}
