import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { PadroListEndPointService } from '@app/shared/services';
import { LoginResponseService } from '@core/services';
import { Solicitud, SolicitudResponse } from '@shared/models';
import { take } from 'rxjs';
import {
  SeAuthService,
  SeTagTheme,
  SeTagThemeEnum,
  SeUser,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-requests-made-table',
  templateUrl: './requests-made.component.html',
  styleUrls: ['./requests-made.component.scss'],
})
export class RequestsMadeComponent implements OnInit {
  filterForm: FormGroup = new FormGroup({
    from: new FormControl(''),
    to: new FormControl(''),
  });

  today = new Date();

  get theme(): SeTagTheme {
    // TODO update to based on response
    return SeTagThemeEnum.PRIMARY;
  }

  private _requests: Solicitud[] = [];

  set requests(requests: Solicitud[]) {
    if (!requests) return;
    this._requests = requests;
  }

  get requests(): Solicitud[] {
    return this._requests;
  }

  private user: SeUser | undefined;

  constructor(
    private padroListEndPointService: PadroListEndPointService,
    private loginService: LoginResponseService,
    private seAuthService: SeAuthService,
  ) {}

  ngOnInit(): void {
    this.user = this.seAuthService.getSessionStorageUser();
    this.getRequests();
  }

  getExercise(dataHora: string): string {
    return dataHora.slice(0, 4);
  }

  getRequests(): void {
    const user = this.loginService.user;
    this.padroListEndPointService
      .getListSolicitudes({
        nifTitular: (user?.nifTitular as string) || (this.user?.nif as string),
        tipusTramitacio: '',
        tipusActuacio: '',
      })
      .pipe(take(1))
      .subscribe((data: SolicitudResponse) => {
        this.requests = data.content ?? [];
      });
  }
}
