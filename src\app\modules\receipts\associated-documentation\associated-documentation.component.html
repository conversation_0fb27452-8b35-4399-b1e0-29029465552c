<se-table
  *ngIf="data.length; else noData"
  [selectable]="false"
  [columns]="tableColumns"
  [data]="data"
  [resizable]="true"
  [cellTemplatePriorityOrder]="'row-column-cell'"
  [currentPage]="currentPage"
  [itemsPerPage]="itemsPerPage"
  [rowsPerPageOptions]="rowsPerPageOptions"
  [showPagination]="true"
  [showEmptyState]="true"
></se-table>

<ng-template #noData>
  <div class="mt-3">
    <se-empty-state [icon]="'info'" [backgroundTheme]="'primary'" />
  </div>
</ng-template>
