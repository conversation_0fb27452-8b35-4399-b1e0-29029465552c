export interface Allegation {
  vehicle?: string;
  titular?: string;
  categoria?: string;
  co2?: number;
  quota?: number;
  motiu?: string;
  submotiu?: string;
  document?: string;
  motiusAllegacio?: Motius[];
}

export interface Motius {
  motiu: string | number;
  submotiu: string | number;
  documents: DocumentsUpload[];
  descripcio: string;
  checkbox?: boolean | string;
}

export interface DocumentsUpload {
  id: string;
  filename: string;
  documentTypeId: string;
  description?: string;
  documentType?: string;
}
