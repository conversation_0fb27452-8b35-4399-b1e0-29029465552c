import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PreviousExercisesFiltersModalComponent } from './previous-exercises-filters-modal.component';
import { PreviousExercisesFiltersComponent } from './previous-exercises-filters.component';
import { PreviousExercisesTableComponent } from './previous-exercises.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeDropdownModule,
  SeModalModule,
  SeInputModule,
  SeRangeFilterModule,
  SeButtonModule,
  SeButtonDropdownModule,
  SeTableModule,
  SeAlertModule,
  SeDropdownFilterModule,
  SeEmptyStateModule,
} from 'se-ui-components-mf-lib';

@NgModule({
  declarations: [
    PreviousExercisesTableComponent,
    PreviousExercisesFiltersComponent,
    PreviousExercisesFiltersModalComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    SeDropdownModule,
    SeModalModule,
    SeInputModule,
    SeRangeFilterModule,
    SeButtonModule,
    SeButtonDropdownModule,
    SeTableModule,
    SeAlertModule,
    SeDropdownFilterModule,
    SeEmptyStateModule,
  ],
  exports: [
    PreviousExercisesTableComponent,
    PreviousExercisesFiltersComponent,
    PreviousExercisesFiltersModalComponent,
  ],
})
export class PreviousExercisesModule {}
