/* eslint-disable @typescript-eslint/no-explicit-any */
import { Component, Injectable } from '@angular/core';
import {
  TestBed,
  waitForAsync,
  type ComponentFixture,
} from '@angular/core/testing';
import {
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import { By } from '@angular/platform-browser';
import { TranslateModule } from '@ngx-translate/core';
import { of, type Observable } from 'rxjs';

import { IdentificationType, type Co2User } from '@app/core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SituationsStoreService,
  SpecificConfigurationService,
  StorageService,
} from '@app/core/services';
import { FeatureFlagService } from '@app/core/services/feature-flag';
import { TagColorService } from '@app/core/services/tag-color';
import type {
  ListPadroResponse,
  VehicleSelected,
  VehiclesSelectedInfo,
} from '@app/shared/models';
import {
  PadroListEndPointService,
  PaginationService,
  PaymentsService,
} from '@app/shared/services';
import {
  Row,
  SeAuthService,
  SeButtonDropdownModule,
  SeButtonModule,
  SeDeviceService,
  SeEmptyStateModule,
  SeInputModule,
  SeModalService,
  SeTableModule,
  type SeButton,
  type SeUser,
} from 'se-ui-components-mf-lib';
import { ReceiptsUtilsService } from '../receipts-utils.service';
import { CensusTableComponent } from './census-table.component';
import { CensusTableService } from './census-table.service';

@Component({
  selector: 'app-census-table-filters',
  template: '',
})
class CensusTableFiltersMockComponent {}

@Injectable()
class ReceiptsUtilsMockService {
  getCommonFormGroup(): FormGroup {
    return new FormGroup({
      plate: new FormControl('', Validators.maxLength(8)),
      rangeFilter: new FormControl({ from: '', to: '' }),
      domiciled: new FormControl(''),
    });
  }

  getOtherDropdownOptions(): SeButton {
    return {
      icon: 'matKeyboardArrowDownOutline',
      iconPosition: 'right',
      iconSize: '1rem',
      disabled: true,
    };
  }

  /* eslint-disable */
  getApplyButton(): void {
    // Intencionadament buit
  }
  getCensusTableColumns(): void {
    // Intencionadament buit
  }
  getDownloadButton(): void {
    // Intencionadament buit
  }
  getRangeFilterOptions(): void {
    // Intencionadament buit
  }
  getResetButton(): void {
    // Intencionadament buit
  }
  getYesOrNoOptions(): void {
    // Intencionadament buit
  }
  getVehiclesSelectedByRows(): void {
    // Intencionadament buit
  }
  getVehiclesSelectedInfoByVehicles(): void {
    // Intencionadament buit
  }
}

@Injectable()
class StorageMockService {
  profileUser = IdentificationType.NOM_PROPI;
}

describe('CensusTableComponent', () => {
  let component: CensusTableComponent;
  let fixture: ComponentFixture<CensusTableComponent>;
  let storageMockService: StorageService;
  let censusTableServiceSpy: jasmine.SpyObj<CensusTableService>;
  let receiptsUtilsServiceSpy: jasmine.SpyObj<ReceiptsUtilsService>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [CensusTableComponent, CensusTableFiltersMockComponent],
      imports: [
        SeButtonDropdownModule,
        SeButtonModule,
        SeInputModule,
        SeTableModule,
        SeEmptyStateModule,
        FormsModule,
        ReactiveFormsModule,
        TranslateModule.forRoot(),
      ],
      providers: [
        CensusTableService,
        { provide: TagColorService, useValue: {} },
        {
          provide: SpecificConfigurationService,
          useValue: { isProvisional: false },
        },
        {
          provide: PadroListEndPointService,
          useValue: {
            getListPadro: (): Observable<ListPadroResponse> =>
              of({ content: { results: [], total: 1, size: 0 } }),
          },
        },
        {
          provide: SeAuthService,
          useValue: {
            getSessionStorageUser: (): SeUser => ({}) as SeUser,
          },
        },
        { provide: ReceiptsUtilsService, useClass: ReceiptsUtilsMockService },
        {
          provide: LoginResponseService,
          useValue: { user: (): Co2User => ({}) as Co2User },
        },
        { provide: StorageService, useClass: StorageMockService },
        { provide: PaymentsService, useValue: {} },
        { provide: CustomRouterService, useValue: {} },
        { provide: SeModalService, useValue: {} },
        {
          provide: PaginationService,
          useValue: {
            getItemsPerPage: (): void => {},
            getRowsPerPageOptions: (): void => {},
          },
        },
        { provide: FeatureFlagService, useValue: {} },
        {
          provide: SeDeviceService,
          useValue: { isMobile: (): boolean => false },
        },
        {
          provide: SituationsStoreService,
          useValue: { situations$: of([]) },
        },
      ],
    }).compileComponents();

    storageMockService = TestBed.inject(StorageService);
    fixture = TestBed.createComponent(CensusTableComponent);
    component = fixture.componentInstance;

    censusTableServiceSpy = TestBed.inject(
      CensusTableService,
    ) as jasmine.SpyObj<CensusTableService>;
    receiptsUtilsServiceSpy = TestBed.inject(
      ReceiptsUtilsService,
    ) as jasmine.SpyObj<ReceiptsUtilsService>;
  }));

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });

  describe('"Altres accions" dropdown', () => {
    it('should be hidden if user profile is LOGIN_SIMPLE', () => {
      storageMockService.profileUser = IdentificationType.LOGIN_SIMPLE;
      fixture.detectChanges();
      const buttonDropdown = fixture.debugElement.query(
        By.css('se-button-dropdown'),
      );
      expect(buttonDropdown).toBeFalsy();
    });

    it('should be hidden if user profile is COORDINADOR', () => {
      storageMockService.profileUser = IdentificationType.COORDINADOR;
      fixture.detectChanges();
      const buttonDropdown = fixture.debugElement.query(
        By.css('se-button-dropdown'),
      );
      expect(buttonDropdown).toBeFalsy();
    });

    it('should be disabled if no vehicles are selected', () => {
      storageMockService.profileUser = IdentificationType.NOM_PROPI;
      fixture.detectChanges();
      const buttonDropdown = fixture.debugElement.query(
        By.css('se-button-dropdown .se-button'),
      ).nativeElement;
      expect(buttonDropdown.disabled).toBeTrue();
    });

    it('should be visible and active if some vehicles are selected and user profile is not LOGIN_SIMPLE or COORDINADOR', () => {
      storageMockService.profileUser = IdentificationType.NOM_PROPI;
      component['isLoginSimple'] = false;
      component['isProvisional'] = false;
      fixture.detectChanges();

      component.onSelectionChange([{ data: {} }]); // selecciona un vehicle

      fixture.detectChanges();

      const buttonDropdown = fixture.debugElement.query(
        By.css('se-button-dropdown .se-button'),
      );

      expect(component['selectedRows'].length).toBe(1);
      expect(
        component['otherActionDropdownButtonOptions'].disabled,
      ).toBeFalse();
      expect(buttonDropdown.nativeElement.disabled).toBeFalse();
    });
  });
  describe('getSelectedVehicles', () => {
    beforeEach(() => {
      component['selectedRows'] = [
        { data: { plate: { value: '1234ABC' } } } as Row,
      ];
    });
    it('should call getVehiclesSelectedByRows with selectedRows', () => {
      const spy = spyOn(
        receiptsUtilsServiceSpy,
        'getVehiclesSelectedByRows',
      ).and.callThrough();
      spyOn(
        receiptsUtilsServiceSpy,
        'getVehiclesSelectedInfoByVehicles',
      ).and.returnValue({} as any);

      component['getSelectedVehicles']();

      expect(spy).toHaveBeenCalledWith([
        { data: { plate: { value: '1234ABC' } } } as Row,
      ]);
    });

    it('should call getVehiclesSelectedInfoByVehicles with vehicles, isProvisional and exercise', () => {
      const vehicles: VehicleSelected[] = [
        { matricula: '1234ABC', exercici: '2024' },
      ];
      spyOn(
        receiptsUtilsServiceSpy,
        'getVehiclesSelectedByRows',
      ).and.returnValue(vehicles);
      const spy = spyOn(
        receiptsUtilsServiceSpy,
        'getVehiclesSelectedInfoByVehicles',
      ).and.returnValue({} as any);
      component['isProvisional'] = true;
      (component as any).exercise = '2024';

      component['getSelectedVehicles']();

      expect(spy).toHaveBeenCalledWith(vehicles, true, '2024');
    });

    it('should return the result of getVehiclesSelectedInfoByVehicles', () => {
      const vehicles: VehicleSelected[] = [
        { matricula: '1234ABC', exercici: '2024' },
      ];
      const expected: VehiclesSelectedInfo = {
        provisional: true,
        matriculas: ['1234ABC'],
        vehicles: [{ matricula: '1234ABC', exercici: '2024' }],
        exercici: '2024',
        idPersTitular: '12345678A',
        tipusAccess: 'NOM_PROPI',
      };
      spyOn(
        receiptsUtilsServiceSpy,
        'getVehiclesSelectedByRows',
      ).and.returnValue(vehicles);
      spyOn(
        receiptsUtilsServiceSpy,
        'getVehiclesSelectedInfoByVehicles',
      ).and.returnValue(expected);

      const result = component['getSelectedVehicles']();

      expect(result).toBe(expected);
    });
  });
});
