import { Injectable } from '@angular/core';
import { SeDropdownOption } from 'se-ui-components-mf-lib';
import { MastersEndpointService } from './masters-endpoint.service';
import { BehaviorSubject, take } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class SituationsStoreService {
  private _situations: SeDropdownOption[] | undefined;
  private _situationsSubject = new BehaviorSubject<
    SeDropdownOption[] | undefined
  >([]);
  public situations$ = this._situationsSubject.asObservable();

  get situations(): SeDropdownOption[] | undefined {
    return this._situations;
  }

  set situations(value: SeDropdownOption[] | undefined) {
    this._situations = value;
    this._situationsSubject.next(value);
  }

  constructor(private masterEndpointService: MastersEndpointService) {
    this.loadSituations();
  }

  private loadSituations(): void {
    this.masterEndpointService
      .getSituations()
      .pipe(take(1))
      .subscribe((situations) => {
        this.situations = situations;
      });
  }

  getSituationCode(situation: string): string {
    return this.situations?.find((sit) => sit.label === situation)
      ?.id as string;
  }
}
