import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SeCheckbox } from 'se-ui-components-mf-lib/lib/components/checkbox/checkbox.model';
import { ReceiptsUtilsService } from '../receipts-utils.service';
import { FormGroup } from '@angular/forms';
import { SituationsStoreService } from '@app/core/services';
import { DeviceService, SeButton } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-previous-exercises-filters',
  styleUrls: ['./previous-exercises.component.scss'],
  template: `
    <form
      *ngIf="filterForm"
      class="search-filter px-4 pt-3 pb-3 flex-nowrap"
      [formGroup]="filterForm"
    >
      <div class="row">
        <div class="col-md-auto col-12">
          <se-range-filter
            formControlName="rangeFilter"
            [fromInput]="rangeFilter.fromInput"
            [toInput]="rangeFilter.toInput"
            [showClear]="true"
            [closeFilterOnResetButton]="true"
            [applyButton]="getButtonAttributes(rangeFilter.applyButton)"
            [resetButton]="getButtonAttributes(rangeFilter.resetButton)"
            (onApply)="onApplyFilter()"
          ></se-range-filter>
        </div>

        <div class="col-md-auto col-12 ps-md-0 mt-3 mt-md-0">
          <se-dropdown-filter
            [placeholder]="'SE_PADRO_CO2.LABELS.SITUATION' | translate"
            [formControlName]="'situation'"
            [options]="situationOptions"
            [applyButton]="getButtonAttributes(applyButton)"
            [resetButton]="getButtonAttributes(resetButton)"
            (onApply)="onApplyFilter()"
          ></se-dropdown-filter>
        </div>

        <div class="col-md-auto col-12 ps-md-0 mt-3 mt-md-0">
          <se-dropdown-filter
            [placeholder]="'SE_PADRO_CO2.LABELS.DOMICILED' | translate"
            [formControlName]="'domiciled'"
            [options]="yesOrNoOptions"
            [showSelectedOptions]="true"
            [applyButton]="getButtonAttributes(applyButton)"
            [resetButton]="getButtonAttributes(resetButton)"
            (onApply)="onApplyFilter()"
          ></se-dropdown-filter>
        </div>
      </div>
    </form>
  `,
})
export class PreviousExercisesFiltersComponent {
  protected applyButton = this.receiptsSrv.getApplyButton();
  protected resetButton = this.receiptsSrv.getResetButton();
  protected rangeFilter = this.receiptsSrv.getRangeFilterOptions();
  protected situationOptions = this.situationsStoreService.situations;
  protected yesOrNoOptions: SeCheckbox[] = this.receiptsSrv.getYesOrNoOptions();

  @Input() filterForm: FormGroup | undefined;
  @Output() handleApplyFilter: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private receiptsSrv: ReceiptsUtilsService,
    private situationsStoreService: SituationsStoreService,
    private deviceSrv: DeviceService,
  ) {}

  protected onApplyFilter = (): void => {
    this.handleApplyFilter.emit();
  };

  protected getButtonAttributes(value: SeButton): SeButton | undefined {
    return this.deviceSrv.isDesktop() ? value : undefined;
  }
}
