<div class="d-flex flex-column flex-md-row justify-content-between mb-2">
  <!-- TITLE WITH TOOLTIP OR LINK -->
  <div class="title text-ellipsis">
    <span [ngClass]="{ bold }">
      {{ title ?? '' | translate }}
    </span>
    <ng-icon
      *ngIf="tooltipText"
      [pTooltipAccessible]="tooltipText | translate"
      tooltipStyleClass="tooltip-position-personalized"
      [name]="'matInfoOutline'"
      class="tooltip-icon"
    ></ng-icon>
    <se-link
      *ngIf="linkText"
      [disabled]="false"
      [size]="'regular'"
      [linkTheme]="'secondary'"
      (onClick)="onLinkClick()"
    >
      {{ linkText | translate }}
    </se-link>
  </div>

  <span class="separator" [ngClass]="{ bold }"></span>

  <!-- CONTENT -->
  <div class="mb-2 mb-md-0">
    <span [ngClass]="{ bold }" *ngIf="description" class="description">
      {{ description | translate }}
    </span>
  </div>
</div>
