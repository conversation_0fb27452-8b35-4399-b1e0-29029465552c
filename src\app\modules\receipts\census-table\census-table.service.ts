import { Injectable } from '@angular/core';
import { AppRoutes, IdentificationType, StatusCodes } from '@app/core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@app/core/services';
import { TagColorService } from '@app/core/services/tag-color';
import { VechilesRowValue } from '@app/shared/components';
import {
  PadroItem,
  VehicleSelected,
  VehiclesSelectedInfo,
} from '@app/shared/models';
import { TranslateService } from '@ngx-translate/core';
import { MenuItemCommandEvent, MenuItem } from 'primeng/api';
import {
  IconActionsCellConfig,
  Row,
  SeDropdownOption,
  type SeButton,
} from 'se-ui-components-mf-lib';
import { ReceiptsUtilsService } from '../receipts-utils.service';
import { PaymentsService } from '@app/shared/services';

@Injectable({
  providedIn: 'root',
})
export class CensusTableService {
  constructor(
    private readonly translateService: TranslateService,
    private readonly specificConfigurationService: SpecificConfigurationService,
    private readonly storageService: StorageService,
    private readonly tagColorService: TagColorService,
    private readonly receiptsUtilsService: ReceiptsUtilsService,
    private readonly paymentService: PaymentsService,
    private readonly customRouter: CustomRouterService,
    private readonly loginService: LoginResponseService,
    private readonly storageData: StorageService,
  ) {}

  getTableRowPadroData(
    data: PadroItem[],
    situationOptions: SeDropdownOption[],
  ): Row[] {
    // const isProvisional = this.specificConfigurationService.isProvisional;

    return data?.map((item: PadroItem): Row => {
      const situacio =
        situationOptions.find((situ) => situ.id === item.codiSituacio)?.label ||
        item.situacio;

      return {
        data: {
          plate: {
            value: item.matricula,
            cellConfig: {
              tagCell: {
                tagTheme: this.tagColorService.getPlateTagColor(
                  item.nou,
                  item.modificat,
                ),
              },
              tagValue: this.tagColorService.getPlateTagValue(
                item.nou,
                item.modificat,
              ),
            },
          },
          carType: { value: item.vehicle },
          domiciled: { value: item.domicilat },
          situation: {
            value: situacio,
            cellConfig: {
              tagCell: {
                tagTheme: this.tagColorService.getStatusCodeColor(
                  item.codiSituacio,
                ),
              },
            },
            cellComponentName: 'tagCellComponent',
          },
          codiSituacio: { value: item.codiSituacio },
          shares: { value: item.quota },
          nou: { value: item.nou },
          modificat: { value: item.modificat },
          idDeute: { value: item.idDeute },
          actions: {
            value: '',
            cellComponentName: 'iconActionsCellComponent',
            cellConfig: this.getActionCellConfig(item),
          },
        },
      };
    });
  }

  private getActionCellConfig(item: PadroItem): IconActionsCellConfig {
    const cellConfig: IconActionsCellConfig = {
      ellipsis: true,
      iconActions: {
        icons: [
          {
            title: 'Euro',
            label: 'SE_PADRO_CO2.BUTTONS.PAY',
            name: 'matEuroOutline',
          },
        ],
        button: {
          btnTheme: 'onlyText',
          icon: 'matMoreVertSharp',
          size: 'small',
          ariaLabel: 'SE_PADRO_CO2.BUTTONS.MORE_ACTIONS',
        },
        buttonActions: [
          {
            label: this.translateService.instant('SE_PADRO_CO2.LABELS.DETAIL'),
          },
        ],
      },
    };

    return this.getIconActions(cellConfig, item);
  }

  getIconActions(
    cellConfig: IconActionsCellConfig,
    item: PadroItem,
  ): IconActionsCellConfig {
    const [
      payLabel,
      viewLabel,
      allegationLabel,
      submitRecursLabel,
      submitReasLabel,
      submitDeferralLabel,
    ] = Object.values<string>(
      this.translateService.instant([
        'SE_PADRO_CO2.BUTTONS.PAY',
        'SE_PADRO_CO2.BUTTONS.DETAIL',
        'SE_PADRO_CO2.BUTTONS.SUBMIT_APPEAL',
        'SE_PADRO_CO2.BUTTONS.SUBMIT_RECURS',
        'SE_PADRO_CO2.BUTTONS.SUBMIT_REAS',
        'SE_PADRO_CO2.BUTTONS.SUBMIT_DEFERRAL',
      ]),
    );

    cellConfig.iconActions.icons = [
      {
        title: viewLabel,
        label: viewLabel,
        ariaLabel: viewLabel,
        name: 'matRemoveRedEyeOutline',
        command: (row: VechilesRowValue): void => this.goToDetail(row),
      },
    ];

    cellConfig.iconActions.buttonActions = [
      {
        label: viewLabel,
        command: (response: MenuItemCommandEvent): void =>
          this.goToDetail(
            (response?.item as MenuItem)['data'] as VechilesRowValue,
          ),
      },
    ];

    //appHideOnCoordinator
    if (this.storageService.profileUser !== IdentificationType.COORDINADOR) {
      // si està pendent o en execució, afegeix acció de pagament
      if (this.showPagamentAction(item)) {
        cellConfig.iconActions.buttonActions.push({
          label: payLabel,
          command: (response: MenuItemCommandEvent): void =>
            this.goToPayment(
              (response?.item as MenuItem)['data'] as VechilesRowValue,
            ),
        });

        cellConfig.iconActions.icons.push({
          title: payLabel,
          label: payLabel,
          name: 'matEuroOutline',
          command: (row: VechilesRowValue): void => this.goToPayment(row),
        });
      }

      if (this.showAllegationsAction()) {
        cellConfig.iconActions.buttonActions.push({
          label: allegationLabel,
          command: (response: MenuItemCommandEvent): void => {
            this.setRequestToGoAllegations(response);
          },
        });
      }

      // si NO es coordinador && si NO es login simple && si es padró definitiu
      if (this.showRecursOrDeferralActions()) {
        cellConfig.iconActions.buttonActions.push(
          // afegeix acció de presentar recurs
          {
            label: submitRecursLabel,
            command: (response: MenuItemCommandEvent): void => {
              const vehiclesSelectedInfo =
                this.receiptsUtilsService.getSelectedPlates([
                  response.item as Row,
                ]);
              this.receiptsUtilsService.goToResurs(vehiclesSelectedInfo);
            },
          },
        );

        if (this.specificConfigurationService.inTimeToBeDeferred) {
          cellConfig.iconActions.buttonActions.push(
            // afegeix acció de presentar ajornament
            {
              label: submitDeferralLabel,
              command: (response: MenuItemCommandEvent): void => {
                this.goToDeferralPayment(
                  (response?.item as MenuItem)['data'] as VechilesRowValue,
                );
              },
            },
          );
        }
      }

      if (this.showReasAction()) {
        cellConfig.iconActions.buttonActions.push(
          // afegeix acció de presentar reas
          {
            label: submitReasLabel,
            command: (response: MenuItemCommandEvent): void => {
              const vehiclesSelectedInfo =
                this.receiptsUtilsService.getSelectedPlates([
                  response.item as Row,
                ]);
              this.receiptsUtilsService.goToReas(vehiclesSelectedInfo);
            },
          },
        );
      }
    }

    return cellConfig;
  }

  private showPagamentAction = (item: PadroItem): boolean =>
    item.codiSituacio === StatusCodes.Pending ||
    item.codiSituacio === StatusCodes.Executive;

  private showRecursOrDeferralActions = (): boolean =>
    this.storageService.profileUser !== IdentificationType.LOGIN_SIMPLE &&
    !this.specificConfigurationService.isProvisional;

  private showAllegationsAction = (): boolean =>
    this.specificConfigurationService.inTimeToBeAlleged &&
    this.specificConfigurationService.isProvisional;

  private showReasAction = (): boolean =>
    (this.storageService.profileUser === IdentificationType.REPRESENTATIVE ||
      this.storageService.profileUser === IdentificationType.CONVENIAT) &&
    !this.specificConfigurationService.isProvisional;

  updateOtherActionsDropdownButtonConfig(
    button: SeButton,
    rows: Row[],
    vehiclesInfo: VehiclesSelectedInfo,
  ): { button: SeButton; actions: MenuItem[] } {
    const dropdownConfig = {
      button: { ...button, disabled: rows.length === 0 },
      actions: [] as MenuItem[],
    };

    if (rows.length === 0) return dropdownConfig;

    if (
      this.storageService.profileUser !== IdentificationType.LOGIN_SIMPLE &&
      this.storageService.profileUser !== IdentificationType.COORDINADOR
    ) {
      dropdownConfig.actions.push(this.createSubmitRecursMenuItem(rows));

      if (this.specificConfigurationService.inTimeToBeDeferred) {
        dropdownConfig.actions.push(
          this.createSubmitDeferralPaymentMenuItem(vehiclesInfo),
        );
      }
    }

    if (
      this.storageService.profileUser === IdentificationType.REPRESENTATIVE ||
      this.storageService.profileUser === IdentificationType.CONVENIAT
    ) {
      dropdownConfig.actions.push(this.createSubmitReasMenuItem(rows));
    }

    dropdownConfig.button.disabled = dropdownConfig.actions.length === 0;

    return dropdownConfig;
  }

  private createSubmitRecursMenuItem(rows: Row[]): MenuItem {
    return {
      label: this.translateService.instant(
        'SE_PADRO_CO2.BUTTONS.SUBMIT_RECURS',
      ),
      command: (): void => {
        const selectedPlates =
          this.receiptsUtilsService.getSelectedPlates(rows);
        this.receiptsUtilsService.goToResurs(selectedPlates);
      },
    };
  }

  private createSubmitDeferralPaymentMenuItem(
    vehiclesInfo: VehiclesSelectedInfo,
  ): MenuItem {
    return {
      label: this.translateService.instant(
        'SE_PADRO_CO2.BUTTONS.SUBMIT_DEFERRAL',
      ),
      command: (): void => {
        this.receiptsUtilsService.goToDeferralPayment(vehiclesInfo);
      },
    };
  }

  private createSubmitReasMenuItem(rows: Row[]): MenuItem {
    return {
      label: this.translateService.instant('SE_PADRO_CO2.BUTTONS.SUBMIT_REAS'),
      command: (): void => {
        const selectedPlates =
          this.receiptsUtilsService.getSelectedPlates(rows);
        this.receiptsUtilsService.goToReas(selectedPlates);
      },
    };
  }

  private getVehicleSelectedByRow(
    row: VechilesRowValue,
  ): VehiclesSelectedInfo | null {
    if (row.plate.value && row.idDeute.value) {
      const vehicle: VehicleSelected = {
        matricula: row.plate.value.toString(),
        exercici: this.specificConfigurationService.currentExercise,
        idDeute: row.idDeute.value.toString(),
        quota: Number(row.shares.value) ?? 0,
      };
      const vehiclesInfo: VehiclesSelectedInfo =
        this.receiptsUtilsService.getVehiclesSelectedInfoByVehicles(
          [vehicle],
          this.specificConfigurationService.isProvisional,
          this.specificConfigurationService.currentExercise,
        );
      return vehiclesInfo;
    }
    return null;
  }

  private goToPayment(row: VechilesRowValue): void {
    const vehicleInfo: VehiclesSelectedInfo | null =
      this.getVehicleSelectedByRow(row);
    if (vehicleInfo) {
      this.paymentService.initPayment(vehicleInfo);
    }
  }

  goToDetail(row: VechilesRowValue): void {
    const { situation, plate, codiSituacio } = row;
    this.customRouter.navigateByBaseUrl(AppRoutes.DETAIL, {
      queryParams: {
        idPersTitular: this.loginService.user.idPersTitular,
        matricula: plate.value,
        exercici: this.specificConfigurationService.currentExercise,
        provisional: this.specificConfigurationService.isProvisional,
        situacio: situation.value,
        tipusAccess: this.loginService.user.tipusAccess,
        codiSituacio: codiSituacio.value,
      },
    });
  }

  private setRequestToGoAllegations = (menu: MenuItemCommandEvent): void => {
    if (menu?.item) {
      const row = menu?.item as MenuItem['data'] as Row;

      this.goToAllegations([row.data['plate']?.value?.toString() ?? '']);
    }
  };

  private goToAllegations(plates: string[]): void {
    const vehiclesInfo: VehiclesSelectedInfo =
      this.receiptsUtilsService.getVehiclesSelectedInfoByPlates(
        plates,
        this.specificConfigurationService.isProvisional,
        this.specificConfigurationService.currentExercise,
      );
    this.storageData.setVehiclesSelected(vehiclesInfo);
    this.storageData.clearIdTramit();
    this.customRouter.navigateByBaseUrl(AppRoutes.ALLEGATIONS);
  }

  private goToDeferralPayment(row: VechilesRowValue): void {
    const vehicleInfo: VehiclesSelectedInfo | null =
      this.getVehicleSelectedByRow(row);
    if (vehicleInfo) {
      this.receiptsUtilsService.goToDeferralPayment(vehicleInfo);
    }
  }
}
