import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  Column,
  DownloadCallbackReturn,
  FlattenedCell,
  FlattenedRow,
  RequestDownloadFile,
  SeDocumentsService,
} from 'se-ui-components-mf-lib';

const translateKey = 'SE_PADRO_CO2.RECEIPTS.GESTIONS.TABLE.';

@Injectable({
  providedIn: 'root',
})
export class GestionsService {
  constructor(
    private translateService: TranslateService,
    private documentsService: SeDocumentsService,
  ) {}

  getProcessColumns(): Column[] {
    const downloadCallback = (
      _row: FlattenedRow,
      cell: FlattenedCell,
    ): DownloadCallbackReturn | Promise<DownloadCallbackReturn> => {
      return new Promise(() => {
        const request: RequestDownloadFile = {
          id: cell.rowData['idPadoc'].value,
        };
        this.documentsService.downloadFile(
          request,
          cell.rowData['idPadoc'].value,
        );
      });
    };
    return [
      {
        key: 'descripcioActuacio',
        header: this.translateKeyColumn('PROCESS_TYPE'),
        resizable: false,
        size: 40,
        sortable: true,
      },
      {
        key: 'dataPresentacio',
        header: this.translateKeyColumn('DATE_AND_TIME'),
        resizable: false,
        cellConfig: {
          dateFormat: 'dd/MM/yyyy HH:mm',
        },
        sortable: true,
        size: 15,
      },
      {
        key: 'nomPresentador',
        header: this.translateKeyColumn('PRESENTER'),
        resizable: false,
        sortable: true,
      },
      {
        key: 'descripcioEstat',
        header: this.translateKeyColumn('STATE'),
        resizable: false,
        size: 16,
        sortable: true,
      },
      {
        key: 'downloadDocument',
        header: this.translateKeyColumn('ACTIONS'),
        size: 7,
        resizable: false,
        cellComponentName: 'actionsCellComponent',
        cellConfig: {
          hasDownload: true,
          hasConfirmation: true,
          hasDelete: false,
          hasEdit: false,
          hasShow: false,
          hideDownloadOnNoValue: true,
          downloadCallback,
        },
      },
    ];
  }

  private translateKeyColumn(key: string): string {
    return this.translateService.instant(`${translateKey}${key}`);
  }
}
