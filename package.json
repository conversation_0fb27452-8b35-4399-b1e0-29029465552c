{"name": "se-padro-co2-mf", "version": "0.98.0-snapshot", "publishConfig": {"registry": "https://slmaven.indra.es/nexus/repository/ATCMP_Npm/"}, "scripts": {"ng": "ng", "start:dev": "ng serve --configuration dev --port 4228 --disable-host-check", "start:int": "ng serve --configuration int --port 4228 --disable-host-check", "build": "ng build", "watch": "ng build --watch --configuration dev", "test": "ng test --watch --code-coverage", "test:prod": "ng test --no-watch --browsers=ChromeHeadless", "build:prod": "ng build --configuration production", "elements": "node elements-build.js", "build:elements": "ng build --configuration production --base-href /mf/se-padro-co2-mf/ && node elements-build.js", "bundle": "ng build -c production --output-hashing=none --base-href /mf/se-padro-co2-mf/ && bash -c \"cat ./dist/se-padro-co2-mf/*.js > ./dist/se-padro-co2-mf/app-micro.js\"", "lint": "ng lint", "lint:styles": "stylelint src/**/*.{css,scss}", "format": "prettier . --write", "format:ci": "prettier . --check", "prepare": "husky"}, "private": false, "dependencies": {"@angular-extensions/elements": "^16.0.0", "@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/elements": "^16.2.4", "@angular/forms": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@ng-bootstrap/ng-bootstrap": "^15.1.1", "@ng-icons/core": "^25.3.1", "@ng-icons/material-icons": "^25.3.1", "@ngx-translate/core": "^15.0.0", "bootstrap": "^5.3.2", "crypto-js": "^4.1.1", "deepmerge-ts": "^5.1.0", "ng-recaptcha": "^12.0.2", "ngx-cookie-service": "^16.0.1", "ngx-translate-multi-http-loader": "^16.0.1", "primeng": "^16.9.1", "rxjs": "~7.8.0", "se-ui-components-mf-lib": "0.176.0", "tslib": "^2.3.0", "uuid": "^8.3.2", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^16.0.1", "@angular-devkit/build-angular": "^16.2.0", "@angular-eslint/builder": "16.3.1", "@angular-eslint/eslint-plugin": "16.3.1", "@angular-eslint/eslint-plugin-template": "16.3.1", "@angular-eslint/schematics": "16.3.1", "@angular-eslint/template-parser": "16.3.1", "@angular/cli": "~16.2.0", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "@types/uuid": "^9.0.4", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "@webcomponents/custom-elements": "^1.6.0", "@webcomponents/webcomponentsjs": "^2.8.0", "concat": "^1.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "10.1.8", "husky": "^9.1.1", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lint-staged": "^15.2.7", "ng-lint-staged": "^12.0.4", "prettier": "3.3.3", "stylelint": "16.7.0", "stylelint-config-standard-scss": "13.1.0", "typescript": "~5.1.3"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{html,ts}": ["eslint --fix", "prettier --write --ignore-unknown"], "src/**/*.{css,scss}": ["stylelint --fix", "prettier --write --ignore-unknown"], "src/**/*[^.ts|^.html|^.css|^.scss]": ["prettier --write --ignore-unknown"]}}