import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable, map } from 'rxjs';
import {
  Nullable,
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import {
  CalculateDeferralPaymentRequest,
  CalculateDeferralPaymentResponse,
  ResumDeferralPaymentResponse,
} from './deferral-payments-endpoints.model';

@Injectable({
  providedIn: 'root',
})
export class DeferralPaymentsEndpointsService {
  constructor(private httpService: SeHttpService) {}

  public calculateDeferral(
    request: CalculateDeferralPaymentRequest,
  ): Observable<Nullable<CalculateDeferralPaymentResponse>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `ajornament-fraccionament/calculate-ajornament-fraccionament`,
      method: 'post',
      body: request,
    };

    return this.httpService
      .post<SeHttpResponse<CalculateDeferralPaymentResponse>>(httpRequest)
      .pipe(
        map(
          (response: SeHttpResponse<CalculateDeferralPaymentResponse>) =>
            response.content,
        ),
      );
  }

  public getResumDeferralPayments(
    id: string,
  ): Observable<Nullable<ResumDeferralPaymentResponse>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `ajornament-fraccionament/resum-ajornament-fraccionament/${id}`,
      method: 'get',
    };

    return this.httpService
      .get<SeHttpResponse<ResumDeferralPaymentResponse>>(httpRequest)
      .pipe(
        map(
          (response: SeHttpResponse<ResumDeferralPaymentResponse>) =>
            response.content || null,
        ),
      );
  }
}
