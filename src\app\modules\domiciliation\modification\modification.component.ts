import { ChangeDetectorR<PERSON>, Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import {
  AppRoutes,
  DomiciledVehicle,
  DomiciledVehiclesRequest,
  IdentificationType,
} from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import {
  DomiciliationEndpointService,
  PaymentsService,
} from '@shared/services';
import {
  Column,
  DeviceService,
  FileFormatsSeparation,
  Nullable,
  Row,
} from 'se-ui-components-mf-lib';
import { BankDetailInput } from '../bank-details/model/bank-details.model';
import { RadioValues, TABLE_COLUMNS } from './model/modification.model';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { ProceduresHeaderService } from '@shared/services';
import { ModificationBankDetailsService } from './modification-bank-details-modal/modification-bank-details.service';

@Component({
  selector: 'app-domiciled-modification',
  templateUrl: './modification.component.html',
  styleUrls: ['./modification.component.scss'],
})
export class ModificationComponent implements OnInit, OnDestroy {
  private vehiclesToBeUpdated: DomiciledVehicle[] = [];

  protected form: FormGroup = new FormGroup({
    radioValue: new FormControl({ value: '', disabled: true }),
  });

  protected radioValues = RadioValues;

  protected itemsPerPage = this.calculateItemsPerPage();

  protected bankDetailInput: Nullable<BankDetailInput>;

  protected data: Row[] = [];

  protected isMobile = this.deviceSrv.isMobile();

  private isIbanInvalid = true;

  fileFormatSeparation: FileFormatsSeparation = FileFormatsSeparation.COMMA;

  get isNextButtonDisabled(): boolean {
    const value = this.form.get('radioValue')?.value;
    return value === this.radioValues.UPDATE ? this.isIbanInvalid : !value;
  }

  get isConveniat(): boolean {
    return this.storageService.profileUser === IdentificationType.CONVENIAT;
  }

  get isRepresentant(): boolean {
    return (
      this.storageService.profileUser === IdentificationType.REPRESENTATIVE
    );
  }

  get isCivilServant(): boolean {
    return this.storageService.profileUser === IdentificationType.CIVIL_SERVANT;
  }

  get isSelfPerson(): boolean {
    return this.storageService.profileUser === IdentificationType.NOM_PROPI;
  }

  protected tableColumns: Column[] = TABLE_COLUMNS;
  protected selectedVehicles: Row[] = [];

  private _unsubscribe: Subject<void> = new Subject();

  constructor(
    private domiciliationEndpointSrv: DomiciliationEndpointService,
    private loginSrv: LoginResponseService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private customRouter: CustomRouterService,
    private storageService: StorageService,
    private cdr: ChangeDetectorRef,
    private paymentsSrv: PaymentsService,
    private deviceSrv: DeviceService,
    private headerService: ProceduresHeaderService,
    private modificationBankDetailsSrv: ModificationBankDetailsService,
  ) {}

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }

  ngOnInit(): void {
    this.headerService.setupDomiciliationHeader();
    this.getDomiciledVehicles();
    this.subscribeToRadioButtonFormValueChanges();
  }

  private subscribeToRadioButtonFormValueChanges(): void {
    this.form
      .get('radioValue')
      ?.valueChanges.pipe(takeUntil(this._unsubscribe), debounceTime(500))
      .subscribe(
        (value) => (this.storageService.modifyAccountRadioButton = value),
      );
  }

  protected onSelectChange(list: Row[], update = true): void {
    if (list.length > 0) {
      this.form.get('radioValue')?.enable();
    } else {
      this.form.reset();
      this.form.get('radioValue')?.disable();
    }
    this.selectedVehicles = list;

    if (update) {
      this.storageService.vehiclesToBeUpdated = this.vehiclesToBeUpdated.filter(
        (vehicle: DomiciledVehicle) => {
          return this.selectedVehicles?.some((selected: Row) => {
            return selected.data['plate'].value === vehicle.matricula;
          });
        },
      ) as DomiciledVehicle[];
    }
  }

  protected goBack(): void {
    this.storageService.clearModificationData();

    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  protected onContinue(): void {
    if (
      this.form.get('radioValue')?.value === this.radioValues.UPDATE &&
      this.specificConfigurationSrv.outOfTimeToBeDomiciled &&
      !this.specificConfigurationSrv.outOfTimeToModifyAccount
    ) {
      //Si l’acció es realitza entre el 15/7 (no inclòs, últim dia per domiciliar l’exercici en curs, parametritzable)
      // i el 13/11 (inclòs, últim dia per a canvis de compte de domiciliació de l’exercici en curs)
      this.modificationBankDetailsSrv
        .checkAlertModalModificationBankDetails()
        .subscribe(() => {
          this.navigateToSecondStep();
        });
    } else {
      this.navigateToSecondStep();
    }
  }

  protected onBankDetailChange(event: Event): void {
    const customEvent = event as CustomEvent<FormGroup>;
    if (customEvent.detail.value.iban) {
      this.isIbanInvalid = customEvent.detail.invalid;
      this.storageService.ibanDomiciliationDR = customEvent.detail.value.DR;
      if (!this.isNextButtonDisabled) {
        this.storageService.ibanDomiciliation = customEvent.detail.value.iban;
        this.cdr.detectChanges();
      }
    }
  }

  private setDomiciledVehicles(vehicles: DomiciledVehicle[]): void {
    this.data = vehicles?.map((vehicle) => ({
      data: {
        plate: { value: vehicle.matricula },
        iban: { value: vehicle.iban },
        model: { value: vehicle.model },
        brand: { value: vehicle.marca },
        vehicle: { value: `${vehicle.marca} ${vehicle.model}` },
        checkbox: {
          value: this.storageService.vehiclesToBeUpdated?.find(
            (storageVehicle) => storageVehicle.matricula === vehicle.matricula,
          ),
        },
      },
    }));

    this.selectTableRowsfromStorageData();
    this.setRadioButtonStorageData();
  }

  private selectTableRowsfromStorageData(): void {
    this.onSelectChange(
      this.data.filter((row) => row.data['checkbox'].value),
      false,
    );
  }

  private setRadioButtonStorageData(): void {
    this.form
      .get('radioValue')
      ?.setValue(this.storageService.modifyAccountRadioButton);
    this.form.updateValueAndValidity();

    if (this.storageService.modifyAccountRadioButton === RadioValues.UPDATE)
      this.setBankDetails();
  }

  private getDomiciledVehicles(): void {
    this.domiciliationEndpointSrv
      .getDomiciledVehicles(this.getDomiciledVehicleBody())
      .subscribe((response) => {
        this.setDomiciledVehicles(response.content as DomiciledVehicle[]);
        this.vehiclesToBeUpdated = response.content as DomiciledVehicle[];
      });
  }

  private getDomiciledVehicleBody(): DomiciledVehiclesRequest {
    return {
      idPersTitular: this.loginSrv.user.idPersTitular as string,
      provisional: this.specificConfigurationSrv.isProvisional,
      exercici: this.specificConfigurationSrv.currentExercise,
      matricula: this.storageService.licensePlate,
      tipusAccess: this.loginSrv.user.tipusAccess,
    };
  }

  protected setBankDetails(): void {
    this.bankDetailInput = this.paymentsSrv.getBankDetailInput({
      iban: this.storageService.ibanDomiciliation,
      dr: this.storageService.ibanDomiciliationDR,
    });
  }

  private navigateToSecondStep(): void {
    this.storageService.isUnsubscribe =
      this.form.get('radioValue')?.value === this.radioValues.UNSUBSCRIBE;
    this.customRouter.navigateByBaseUrl(AppRoutes.MODIFY_DOMICILED_RESUME);
  }

  protected isValidateRepresentative = (): boolean =>
    this.paymentsSrv.isValidateRepresentative();

  private calculateItemsPerPage(): number {
    return this.deviceSrv.isMobile() ? 5 : 10;
  }
}
