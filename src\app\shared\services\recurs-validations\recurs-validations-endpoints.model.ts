import { AtesaPhoneData, IdentificationType } from '@core/models';
import { VehicleRecurs } from '@shared/models';
import type { Nullable } from 'se-ui-components-mf-lib';

export interface StartRecursRequest {
  idPerssCens: string;
  tipusAcces: IdentificationType;
  exercici: number;
  matricules: string[];
  csvNotificacio?: Nullable<string>; //pendentNotificacio
  advertimentTerminiAcceptat: boolean;
  nomesVehiclesValids: boolean;
  trucadaTelefonica?: Nullable<AtesaPhoneData>;
  nifRepresentant?: Nullable<string>;
}

export interface SelectedVehiclesValidationResponse {
  recursId: string;
  validation: ValidationVehicles;
  advertimentTerminiAcceptat?: Nullable<boolean>;
  nomesVehiclesValids?: Nullable<boolean>;
  csvNotificacio?: Nullable<string>;
  showReasAlert?: boolean;
}

export interface SelectedVehiclesReasValidationResponse {
  id: string;
  validation: ValidationVehicles;
}

export interface ValidationVehicles {
  vehiclesRecurribles: VehicleRecurs[];
  vehiclesAdvertirTermini: boolean;
  previRea: boolean;
  previRecurs: boolean;
  vehiclesForaTermini: boolean;
  csvNotificacioNecessari: boolean;
  allCsvNotificacio: boolean;
}
