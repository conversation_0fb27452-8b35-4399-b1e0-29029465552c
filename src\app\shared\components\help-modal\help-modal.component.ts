import { Component, Input } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';

import { SpecificConfigurationService } from '@app/core/services';
import {
  HelpModalOption,
  HelpModalOptionEnum,
} from '@shared/components/help-modal';
import { SeModal } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-help-modal',
  templateUrl: './help-modal.component.html',
  styleUrls: ['./help-modal.component.scss'],
})
export class HelpModalComponent {
  @Input() data: SeModal | undefined;

  private _selectedDropdownOption: HelpModalOption =
    HelpModalOptionEnum.DOMICILED;
  @Input() set selectedDropdownOption(
    newSelectedDropdownOption: HelpModalOption,
  ) {
    const valueControl = this.form.get('value') as FormControl<string>;
    if (valueControl.value === this.OPTIONS[newSelectedDropdownOption]) return;
    valueControl.setValue(this.OPTIONS[newSelectedDropdownOption]);
    this._selectedDropdownOption = newSelectedDropdownOption;
  }
  get selectedDropdownOption(): HelpModalOption {
    return this._selectedDropdownOption;
  }

  OPTIONS = {
    DOMICILED: 'SE_PADRO_CO2.MODAL.HELP_MODAL.OPTIONS.DOMICILED',
    PAYMENTS: 'SE_PADRO_CO2.MODAL.HELP_MODAL.OPTIONS.PAYMENTS',
    RECURS_Y_REA: 'SE_PADRO_CO2.MODAL.HELP_MODAL.OPTIONS.RECURS_Y_REA',
  };

  form: FormGroup = new FormGroup({
    value: new FormControl(this.OPTIONS.DOMICILED),
  });

  options = [
    { id: this.OPTIONS.DOMICILED, label: this.OPTIONS.DOMICILED },
    { id: this.OPTIONS.PAYMENTS, label: this.OPTIONS.PAYMENTS },
    { id: this.OPTIONS.RECURS_Y_REA, label: this.OPTIONS.RECURS_Y_REA },
  ];

  get fiPeriodeVoluntariPagament(): Date {
    return new Date(
      this.specificConfigurationService.dates
        ?.fiPeriodeVoluntariPagament as string,
    );
  }

  get fiTerminiRecurs(): Date {
    return new Date(
      this.specificConfigurationService.dates?.fiTerminiRecurs as string,
    );
  }

  currentLang = this.translateService.store.currentLang;

  constructor(
    private activatedModalService: NgbActiveModal,
    private translateService: TranslateService,
    public specificConfigurationService: SpecificConfigurationService,
  ) {}

  closeModal(): void {
    this.activatedModalService.close();
  }
}
