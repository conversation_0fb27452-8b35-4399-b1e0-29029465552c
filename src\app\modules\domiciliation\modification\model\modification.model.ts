import { Column } from 'se-ui-components-mf-lib';

export enum TableKeys {
  Plate = 'plate',
  Vehicle = 'vehicle',
  Iban = 'iban',
}

export enum RadioValues {
  UPDATE = 'update',
  UNSUBSCRIBE = 'unsubscribe',
}

export const TABLE_COLUMNS: Column[] = [
  {
    header:
      'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.VEHICLES_TO_BE_REGISTERED.TABLE.CAR_REGISTRATION',
    key: TableKeys.Plate,
    cellComponentName: 'defaultCellComponent',
    resizable: true,
    size: 20,
    cellConfig: {
      ellipsis: true,
      tooltip: false,
    },
  },
  {
    header:
      'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.VEHICLES_TO_BE_REGISTERED.TABLE.VEHICLE',
    key: TableKeys.Vehicle,
    cellComponentName: 'defaultCellComponent',
    resizable: true,
    size: 35,
    cellConfig: {
      ellipsis: true,
      tooltip: false,
    },
  },
  {
    header:
      'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.CURRENT_DIRECT_DEBIT_BANK_ACCOUNT',
    key: TableKeys.Iban,
    cellComponentName: 'defaultCellComponent',
    resizable: true,
    cellConfig: {
      ellipsis: true,
      tooltip: false,
    },
  },
];
