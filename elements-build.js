const fs = require('fs-extra');  
const concat = require('concat');
var pjson = require('./package.json');

(async function build() {
    // Reset previous version
    await fs.emptyDir('./elements');

    // Concatenate Webcomponent JS files
    const files = [];

    //passsing directoryPath and callback function
    const directory = './dist/se-documents-mf/';

    await fs.ensureDir('./dist/se-documents-mf');
    const files1 = await fs.readdir('./dist/se-documents-mf/');
    files1.filter(file => file.match(/(\w*)\.js$/)).forEach(function (file) {
        // Do whatever you want to do with the file
        //console.log(file)
        files.push('./dist/se-documents-mf/'+ file)
    });

    await fs.ensureDir('./elements');
    await concat(files, './elements/se-documents.js');

    // Copy styles
    await fs.copyFile('./dist/se-documents-mf/styles.css', './elements/styles.css');

    // Copy assets
    await fs.ensureDir('./elements/assets');
    await fs.copy('./dist/se-documents-mf/assets', './elements/assets');

    var buildtime = new Date();
    var versionJson = "{\"build\":{\"version\":\""
        + pjson.version + "\",\"name\":\""
        + pjson.name + "\",\"time\":\""
        + buildtime.toISOString() + "\"}}";
    fs.writeFileSync('./elements/version.json', versionJson);
})();