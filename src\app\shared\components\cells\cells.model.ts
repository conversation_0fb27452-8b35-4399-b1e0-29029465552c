import { MenuItem } from 'primeng/api';
import { CellConfig, SeButton } from 'se-ui-components-mf-lib';

export interface IconActions extends CellConfig {
  iconActions: {
    icons: Icon[];
    button: SeButton;
    buttonActions: MenuItem[];
  };
}

export interface Icon {
  name: string;
  title: string;
  command: null | ((value: RowData) => void);
}

export interface RowData {
  data: {
    [key: string]: RowValue;
  };
}

export interface VechilesRowValue {
  plate: RowValue;
  carType: RowValue;
  domiciled: RowValue;
  exercise: RowValue;
  situation: RowValue;
  codiSituacio: RowValue;
  shares: RowValue;
  now: RowValue;
  idDeute: RowValue;
}

export interface DocumentsRowData {
  name: RowValue;
  size: RowValue;
  documentType: RowValue;
  exercise: RowValue;
  ids: RowValue;
}

export interface RowValue {
  value: string | number | boolean | null | undefined | string[];
}
