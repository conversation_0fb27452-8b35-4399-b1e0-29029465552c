/* You can add global styles to this file, and also import other style files */
@import "bootstrap/scss/functions";
@import "bootstrap/scss/variables";
@import "bootstrap/scss/mixins/breakpoints";

:root {
  font-size: 16px !important;
}

body,
p {
  font-family: 'Open Sans', sans-serif !important;
  font-size: 16px;
}

// RESPONSIVE

/* XL - 1200px */
// @media (width >= 1200px) {
@include media-breakpoint-up(xl) {
  .container {
    max-width: 1180px !important;
  }
}
