<ng-container *ngIf="panelMode; else seUploadFiles">
  <se-panel
    class="panel-upload"
    [id]="'tableUploadFiles'"
    [title]="title || 'SE_DOCUMENTS_MF.TABLE_UPLOAD_FILES.TITLE' | translate"
    [actionButton]="openModalButton"
    (actionButtonClick)="openModal()"
  >
    <p class="description-upload">
      {{ panelDescription | translate }}
    </p>

    <div *ngIf="infoPanelAlert" class="m-4">
      <se-alert
        [title]="infoPanelAlert.title"
        [type]="infoPanelAlert.type"
        [list]="infoPanelAlert.list"
        [filterButton]="infoPanelAlert.filterButton"
        [filtered]="infoPanelAlert.filtered || false"
        [alertClass]="infoPanelAlert.alertClass || ''"
        [contentClass]="infoPanelAlert.contentClass || ''"
        [minFilteredListLength]="infoPanelAlert.minFilteredListLength || 0"
        [closeButton]="false"
      >
      </se-alert>
    </div>

    <hr *ngIf="hasFiles" class="m-0" />

    <ng-container *ngTemplateOutlet="seUploadFiles"></ng-container>
  </se-panel>
</ng-container>
<ng-template #seUploadFiles>
  <se-upload-files
    [ngClass]="{ 'table-upload': panelMode }"
    [useFileNameAsDescription]="useFileNameAsDescription"
    [hasActions]="hasActions"
    [tableColumns]="tableColumns"
    [modalTableColumns]="modalTableColumns"
    [hasModal]="!panelMode ? hasModal : false"
    [showInput]="!panelMode ? showInput : false"
    [info]="info"
    [title]="!panelMode ? title : ''"
    [subtitle]="true"
    [subtitleText]="!panelMode ? subtitleText : ''"
    [accept]="accept"
    [multiple]="multiple"
    [sizeLimitPerFile]="sizeLimitPerFile"
    [groupSizeLimit]="groupSizeLimit"
    [maxFiles]="maxFiles"
    [disabled]="disabled"
    [dropAreaTitlePreLinkText]="dropAreaTitlePreLinkText"
    [fileFormatSeparation]="fileFormatSeparation"
    [dropAreaTitleLinkText]="dropAreaTitleLinkText"
    [dropAreaTitlePostLinkText]="dropAreaTitlePostLinkText"
    [files]="files"
    [required]="required"
    [onlyTable]="panelMode"
    (filesLoaded)="onFilesLoaded($event)"
    (deleteFile)="onDeleteFile($event)"
    (downloadFile)="onDownloadFile($event)"
  >
  </se-upload-files>
</ng-template>
