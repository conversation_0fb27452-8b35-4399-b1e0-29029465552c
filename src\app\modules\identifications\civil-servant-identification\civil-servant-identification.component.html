<form class="mt-2 d-flex flex-column" [formGroup]="civilServantForm">
  <se-panel [title]="'SE_PADRO_CO2.PUBLIC_SERVANT.TITLE' | translate">
    <!-- TODO condition ngif when endpoint response -->
    <se-alert
      *ngIf="false"
      [title]="'SE_PADRO_CO2.ERRORS.NIF_NOT_FOUND' | translate"
      [type]="'error'"
      [closeButton]="false"
    >
    </se-alert>
    <!-- DESCRIPTION -->
    <p>
      {{ 'SE_PADRO_CO2.PUBLIC_SERVANT.SUBTITLE' | translate }}
    </p>
    <!-- nifs y boton de validar -->
    <div class="d-flex flex-sm-row flex-column gap-4 align-items-start">
      <se-input
        class="w-xs-100"
        formControlName="nif"
        [label]="'SE_PADRO_CO2.PUBLIC_SERVANT.NIF' | translate"
        [type]="'text'"
        [id]="'nif-id'"
      ></se-input>
      <se-input
        class="w-xs-100"
        formControlName="representativeNif"
        [label]="'SE_PADRO_CO2.PUBLIC_SERVANT.REPRESENTATIVE_NIF' | translate"
        [type]="'text'"
        [id]="'representativeNif-id'"
      ></se-input>
      <se-button
        class="mt-button-input w-xs-100"
        type="btn"
        [disabled]="disableValidateBothNIFsButton"
        [btnTheme]="'primary'"
        (click)="validateNIFs()"
      >
        {{ 'SE_PADRO_CO2.BUTTONS.VALIDATE' | translate }}
      </se-button>
    </div>
    <!-- checkbox de aceptar declaracion responsable -->
    <se-checkbox
      *ngIf="showResponsibleDeclarationCheckbox"
      class="col-12"
      formControlName="declaration"
      [label]="
        'SE_PADRO_CO2.PUBLIC_SERVANT.RESPONSIBLE_DECLARATION' | translate
      "
      [id]="'checkbox-id'"
      [tooltip]="false"
      (onClick)="updateDeclarationForm($event)"
    ></se-checkbox>
    <hr class="mt-4 mb-4" />
    <p>
      {{ 'SE_PADRO_CO2.PUBLIC_SERVANT.ATTENTION_TYPE' | translate }}
    </p>
    <!-- tipo de atencion bloque -->
    <se-radio
      class="col-auto"
      id="radio-1"
      name="identification"
      formControlName="identificationType"
      [value]="identificationTypes.PERSONAL"
      [label]="'SE_PADRO_CO2.RADIO.IN_PERSON' | translate"
    >
    </se-radio>
    <se-radio
      class="col-auto"
      id="radio-2"
      name="identification"
      formControlName="identificationType"
      [value]="identificationTypes.PHONE"
      [label]="'SE_PADRO_CO2.RADIO.TELEPHONE' | translate"
    >
    </se-radio>
    <!-- contenedores de atencion -->
    <div class="row mt-2" *ngIf="showPhoneForm">
      <se-input
        class="col-12 col-lg-2 col-md-3"
        formControlName="phone"
        [label]="'SE_PADRO_CO2.LABELS.PHONE_NUMBER' | translate"
        [disabled]="false"
        [type]="'text'"
        [id]="'phone-id'"
      ></se-input>
      <se-input
        class="col-12 col-lg-2 col-md-3"
        formControlName="date"
        [disabled]="true"
        [label]="'SE_PADRO_CO2.LABELS.DATE' | translate"
        [type]="'text'"
        [id]="'date-id'"
      ></se-input>
      <se-input
        class="col-12 col-lg-2 col-md-3"
        formControlName="hour"
        [label]="'SE_PADRO_CO2.LABELS.HOUR' | translate"
        [disabled]="true"
        [type]="'text'"
        [id]="'hour-id'"
      ></se-input>
    </div>
  </se-panel>

  <!-- boton de continuar -->
  <div class="d-flex flex-column flex-sm-row justify-content-sm-end mt-4">
    <se-button
      type="btn"
      [btnTheme]="'primary'"
      [disabled]="isDisabledSubmitButton"
      (click)="onSubmit()"
    >
      {{ 'UI_COMPONENTS.BUTTONS.CONTINUE' | translate }}
    </se-button>
  </div>
</form>
