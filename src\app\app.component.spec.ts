import {
  ComponentFixture,
  discardPeriodicTasks,
  fakeAsync,
  TestBed,
  tick,
} from '@angular/core/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { AppComponent } from './app.component';
import { of } from 'rxjs';
import {
  SeUser,
  SeLoginResponse,
  LoginCertificates,
  SeAuthService,
  SeLoginService,
  SeModalService,
  SePageLayoutService,
  SeHeaderInfoModule,
  SeExceptionViewerModule,
  SeUserService,
} from 'se-ui-components-mf-lib';
import {
  StepperService,
  HeaderInfoService,
  ConfigService,
} from '@core/services';
import { TranslateModule } from '@ngx-translate/core';

const mockUser: SeUser = {
  certificateType: LoginCertificates.JUR_PERSON,
  companyName: 'Test Company',
  companyId: '12345678',
  nombreCompleto: 'Test User',
  nif: '87654321',
  apellido1: 'Apellido1',
  apellido2: 'Apellido2',
  email: '<EMAIL>',
  environment: 'test',
  fechaCreacion: '25-12-2021',
  fechaUltimaConexion: '25-12-2021',
  fechaLogin: '25-12-2021',
  idCompanyCens: 'idCompanyCens',
  idPersCens: 'idPersCens',
  token: 'token',
  idPersCensPresentador: 'idPersCensPresentador',
  indActivo: true,
  method: 'method',
  nombre: 'nombre',
  nombrePresentador: 'nombrePresentador',
  nifPresentador: 'nifPresentador',
  accessMode: 'accessMode',
  accessToken: 'accessToken',
  assuranceLevel: 'assuranceLevel',
  codiTipusPersona: 'codiTipusPersona',
  es012: true,
  esAtesa: true,
  esEmpleadoPublico: true,
  esJuridico: true,
  identifierType: 'identifierType',
  indUsuarioSimulado: true,
  nifReal: 'nifReal',
  nombreSimplePresentador: 'nombreSimplePresentador',
  nombreCompletoReal: 'nombreCompletoReal',
  rol: 'rol',
  origin_id: 'GICAR',
  idPersCensNomPropi: 'idPersCensNomPropi',
  nifNomPropi: 'nifNomPropi',
  nomNomPropi: 'nomNomPropi',
  nombreSimpleNomPropi: 'nombreSimpleNomPropi',
  apellido1NomPropi: 'Apellido1NomPropi',
  apellido2NomPropi: 'Apellido2NomPropi',
};

describe('AppComponent', () => {
  let component: AppComponent;
  let fixture: ComponentFixture<AppComponent>;
  // let router: Router;
  let pageLayoutService: SePageLayoutService;
  let loginService: SeLoginService;
  let userService: SeUserService;

  const mockSeAuthService = {
    getSessionStorageUser: jasmine
      .createSpy('getSessionStorageUser')
      .and.returnValue({}),
  };

  const mockSeModalService = {
    openModal: jasmine.createSpy('openModal').and.returnValue(null),
  };

  const mockSeLoginService = {
    login: jasmine.createSpy('login').and.returnValue({}),
  };

  const mockSePageLayoutService = {
    getCurrentSteps: jasmine.createSpy('getCurrentSteps').and.returnValue([]),
  };

  const mockUserService = {
    getName: jasmine.createSpy('getName').and.returnValue('Test Company'),
    getNIF: jasmine.createSpy('getNIF').and.returnValue('12345678'),
  };

  beforeEach(() => {
    TestBed.configureTestingModule({
      imports: [
        RouterTestingModule,
        TranslateModule.forRoot(),
        HttpClientTestingModule,
        SeHeaderInfoModule,
        SeExceptionViewerModule,
      ],
      declarations: [AppComponent],
      providers: [
        { provide: SePageLayoutService, mockSePageLayoutService },
        { provide: SeLoginService, useValue: mockSeLoginService },
        { provide: SeAuthService, useValue: mockSeAuthService },
        { provide: SeModalService, useValue: mockSeModalService },
        { provide: SeUserService, useValue: mockUserService },
        StepperService,
        HeaderInfoService,
        ConfigService,
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(AppComponent);
    component = fixture.componentInstance;
    // router = TestBed.inject(Router);
    pageLayoutService = TestBed.inject(SePageLayoutService);
    loginService = TestBed.inject(SeLoginService);
    userService = TestBed.inject(SeUserService);
  });

  it('should create the app', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize steps$', () => {
    const stepsMock = [
      {
        id: 'ALLEGATION_STEP1',
        label: 'REASON_ALLEGATION',
      },
      {
        id: 'ALLEGATION_STEP2',
        label: 'RESUM_ALLEGATION',
      },
    ];
    fixture.detectChanges();
    pageLayoutService.getCurrentSteps = jasmine
      .createSpy('getCurrentSteps')
      .and.returnValue(of(stepsMock));
    component.steps$.subscribe((steps) => {
      expect(steps).toEqual(stepsMock);
    });
  });

  it('should set user data correctly', () => {
    component['setUserData'](mockUser);
    expect(component.presenterName).toBe('Test Company');
    expect(component.presenterNif).toBe('12345678');
  });

  it('should call loginService.login and set user data on secured', fakeAsync(() => {
    const response: SeLoginResponse = {
      content: {
        tokenJwt: 'token',
        tokenExpiration: '25-12-2025',
        usuario: mockUser,
      },
    };
    loginService.login = jasmine
      .createSpy('login')
      .and.returnValue(new Promise((resolve) => resolve(response)));
    spyOn(component as never, 'setUserData').and.callThrough();
    //SECURED
    component['secured']();
    expect(loginService.login).toHaveBeenCalled();
    tick();
    expect(component['setUserData']).toHaveBeenCalledWith(mockUser);
    fixture.detectChanges();
    expect(userService.getNIF).toHaveBeenCalled();
    expect(userService.getName).toHaveBeenCalled();
    expect(component.presenterName).toBe('Test Company');
    expect(component.presenterNif).toBe('12345678');
    discardPeriodicTasks();
  }));
});
