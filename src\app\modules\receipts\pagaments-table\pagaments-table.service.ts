import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  SeDocumentsService,
  Column,
  FlattenedRow,
  FlattenedCell,
  DownloadCallbackReturn,
  RequestDownloadFile,
} from 'se-ui-components-mf-lib';

const translateKey = 'SE_PADRO_CO2.RECEIPTS.PAGAMENTS.TABLE.';

@Injectable({
  providedIn: 'root',
})
export class PagamentsTableService {
  constructor(
    private translateService: TranslateService,
    private documentsService: SeDocumentsService,
  ) {}

  getPaymentColumns(): Column[] {
    const downloadCallback = (
      _row: FlattenedRow,
      cell: FlattenedCell,
    ): DownloadCallbackReturn | Promise<DownloadCallbackReturn> => {
      return new Promise(() => {
        const request: RequestDownloadFile = {
          id: cell.rowData['idPadoc'].value,
        };
        this.documentsService.downloadFile(
          request,
          cell.rowData['idPadoc'].value,
        );
      });
    };
    return [
      {
        key: 'numReferencia',
        header: this.translateKeyColumn('RECEIPT_NUMBER'),
        resizable: false,
        sortable: true,
        size: 15,
      },
      {
        key: 'descripcioConcepte',
        header: this.translateKeyColumn('CONCEPT'),
        resizable: false,
        size: 35,
        sortable: true,
      },
      {
        key: 'dataPagament',
        header: this.translateKeyColumn('DATE_AND_TIME'),
        resizable: false,
        sortable: true,
        size: 10,
        cellConfig: {
          dateFormat: 'dd/MM/yyyy HH:mm',
        },
      },
      {
        key: 'importPagat',
        header: this.translateKeyColumn('SUM_PAID'),
        resizable: false,
        sortable: true,
        size: 15,
        cellConfig: {
          currencyCode: 'EUR',
          currencySymbol: '€',
          align: 'right',
        },
      },
      {
        key: 'downloadDocument',
        header: this.translateKeyColumn('ACTIONS'),
        size: 7,
        resizable: false,
        cellComponentName: 'actionsCellComponent',
        cellConfig: {
          hasDownload: true,
          hasConfirmation: true,
          hasDelete: false,
          hasEdit: false,
          hasShow: false,
          hideDownloadOnNoValue: true,
          downloadCallback,
          align: 'right',
        },
      },
    ];
  }

  private translateKeyColumn(key: string): string {
    return this.translateService.instant(`${translateKey}${key}`);
  }
}
