@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

:host {
  ::ng-deep {
    th:last-of-type {
      justify-items: end !important;
    }
  }
}

.search-filter {
  background-color: var(--color-gray-200);
  border: 1px solid var(--color-gray-300);

  &--box {
    width: 100%;

    // @media (min-width: 768px) {
    @include media-breakpoint-up(md) {
      width: 14rem;
    }
  }
}

.btn-100,
.btn-100 ::ng-deep button {
  width: 100%;

  @include media-breakpoint-up(md) {
    width: auto;
  }
}
