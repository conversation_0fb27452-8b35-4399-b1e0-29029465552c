{"name": "se-padro-co2-mf", "version": "0.98.0-snapshot", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "se-padro-co2-mf", "version": "0.98.0-snapshot", "dependencies": {"@angular-extensions/elements": "^16.0.0", "@angular/animations": "^16.2.0", "@angular/common": "^16.2.0", "@angular/compiler": "^16.2.0", "@angular/core": "^16.2.0", "@angular/elements": "^16.2.4", "@angular/forms": "^16.2.0", "@angular/platform-browser": "^16.2.0", "@angular/platform-browser-dynamic": "^16.2.0", "@angular/router": "^16.2.0", "@ng-bootstrap/ng-bootstrap": "^15.1.1", "@ng-icons/core": "^25.3.1", "@ng-icons/material-icons": "^25.3.1", "@ngx-translate/core": "^15.0.0", "bootstrap": "^5.3.2", "crypto-js": "^4.1.1", "deepmerge-ts": "^5.1.0", "ng-recaptcha": "^12.0.2", "ngx-cookie-service": "^16.0.1", "ngx-translate-multi-http-loader": "^16.0.1", "primeng": "^16.9.1", "rxjs": "~7.8.0", "se-ui-components-mf-lib": "^0.176.0-snapshot.20250904132330", "tslib": "^2.3.0", "uuid": "^8.3.2", "zone.js": "~0.13.0"}, "devDependencies": {"@angular-builders/custom-webpack": "^16.0.1", "@angular-devkit/build-angular": "^16.2.0", "@angular-eslint/builder": "16.3.1", "@angular-eslint/eslint-plugin": "16.3.1", "@angular-eslint/eslint-plugin-template": "16.3.1", "@angular-eslint/schematics": "16.3.1", "@angular-eslint/template-parser": "16.3.1", "@angular/cli": "~16.2.0", "@angular/compiler-cli": "^16.2.0", "@types/jasmine": "~4.3.0", "@types/uuid": "^9.0.4", "@typescript-eslint/eslint-plugin": "^7.4.0", "@typescript-eslint/parser": "^7.4.0", "@webcomponents/custom-elements": "^1.6.0", "@webcomponents/webcomponentsjs": "^2.8.0", "concat": "^1.0.3", "eslint": "^8.57.0", "eslint-config-prettier": "10.1.8", "husky": "^9.1.1", "jasmine-core": "~4.6.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "lint-staged": "^15.2.7", "ng-lint-staged": "^12.0.4", "prettier": "3.3.3", "stylelint": "16.7.0", "stylelint-config-standard-scss": "13.1.0", "typescript": "~5.1.3"}}, "node_modules/@ampproject/remapping": {"version": "2.2.1", "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.0", "@jridgewell/trace-mapping": "^0.3.9"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@angular-builders/custom-webpack": {"version": "16.0.1", "dev": true, "license": "MIT", "dependencies": {"@angular-devkit/architect": ">=0.1600.0 < 0.1700.0", "@angular-devkit/build-angular": "^16.0.0", "@angular-devkit/core": "^16.0.0", "lodash": "^4.17.15", "ts-node": "^10.0.0", "tsconfig-paths": "^4.1.0", "webpack-merge": "^5.7.3"}, "engines": {"node": "^14.20.0 || ^16.13.0 || >=18.10.0"}, "peerDependencies": {"@angular/compiler-cli": "^16.0.0"}}, "node_modules/@angular-devkit/architect": {"version": "0.1602.16", "dev": true, "license": "MIT", "dependencies": {"@angular-devkit/core": "16.2.16", "rxjs": "7.8.1"}, "engines": {"node": "^16.14.0 || >=18.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}, "node_modules/@angular-devkit/build-angular": {"version": "16.2.16", "dev": true, "license": "MIT", "dependencies": {"@ampproject/remapping": "2.2.1", "@angular-devkit/architect": "0.1602.16", "@angular-devkit/build-webpack": "0.1602.16", "@angular-devkit/core": "16.2.16", "@babel/core": "7.22.9", "@babel/generator": "7.22.9", "@babel/helper-annotate-as-pure": "7.22.5", "@babel/helper-split-export-declaration": "7.22.6", "@babel/plugin-proposal-async-generator-functions": "7.20.7", "@babel/plugin-transform-async-to-generator": "7.22.5", "@babel/plugin-transform-runtime": "7.22.9", "@babel/preset-env": "7.22.9", "@babel/runtime": "7.22.6", "@babel/template": "7.22.5", "@discoveryjs/json-ext": "0.5.7", "@ngtools/webpack": "16.2.16", "@vitejs/plugin-basic-ssl": "1.0.1", "ansi-colors": "4.1.3", "autoprefixer": "10.4.14", "babel-loader": "9.1.3", "babel-plugin-istanbul": "6.1.1", "browserslist": "^4.21.5", "chokidar": "3.5.3", "copy-webpack-plugin": "11.0.0", "critters": "0.0.20", "css-loader": "6.8.1", "esbuild-wasm": "0.18.17", "fast-glob": "3.3.1", "guess-parser": "0.4.22", "https-proxy-agent": "5.0.1", "inquirer": "8.2.4", "jsonc-parser": "3.2.0", "karma-source-map-support": "1.4.0", "less": "4.1.3", "less-loader": "11.1.0", "license-webpack-plugin": "4.0.2", "loader-utils": "3.2.1", "magic-string": "0.30.1", "mini-css-extract-plugin": "2.7.6", "mrmime": "1.0.1", "open": "8.4.2", "ora": "5.4.1", "parse5-html-rewriting-stream": "7.0.0", "picomatch": "2.3.1", "piscina": "4.0.0", "postcss": "8.4.31", "postcss-loader": "7.3.3", "resolve-url-loader": "5.0.0", "rxjs": "7.8.1", "sass": "1.64.1", "sass-loader": "13.3.2", "semver": "7.5.4", "source-map-loader": "4.0.1", "source-map-support": "0.5.21", "terser": "5.19.2", "text-table": "0.2.0", "tree-kill": "1.2.2", "tslib": "2.6.1", "vite": "4.5.5", "webpack": "5.94.0", "webpack-dev-middleware": "6.1.2", "webpack-dev-server": "4.15.1", "webpack-merge": "5.9.0", "webpack-subresource-integrity": "5.1.0"}, "engines": {"node": "^16.14.0 || >=18.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "optionalDependencies": {"esbuild": "0.18.17"}, "peerDependencies": {"@angular/compiler-cli": "^16.0.0", "@angular/localize": "^16.0.0", "@angular/platform-server": "^16.0.0", "@angular/service-worker": "^16.0.0", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "karma": "^6.3.0", "ng-packagr": "^16.0.0", "protractor": "^7.0.0", "tailwindcss": "^2.0.0 || ^3.0.0", "typescript": ">=4.9.3 <5.2"}, "peerDependenciesMeta": {"@angular/localize": {"optional": true}, "@angular/platform-server": {"optional": true}, "@angular/service-worker": {"optional": true}, "jest": {"optional": true}, "jest-environment-jsdom": {"optional": true}, "karma": {"optional": true}, "ng-packagr": {"optional": true}, "protractor": {"optional": true}, "tailwindcss": {"optional": true}}}, "node_modules/@angular-devkit/build-angular/node_modules/tslib": {"version": "2.6.1", "dev": true, "license": "0BSD"}, "node_modules/@angular-devkit/build-angular/node_modules/webpack-merge": {"version": "5.9.0", "dev": true, "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "wildcard": "^2.0.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/@angular-devkit/build-webpack": {"version": "0.1602.16", "dev": true, "license": "MIT", "dependencies": {"@angular-devkit/architect": "0.1602.16", "rxjs": "7.8.1"}, "engines": {"node": "^16.14.0 || >=18.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "peerDependencies": {"webpack": "^5.30.0", "webpack-dev-server": "^4.0.0"}}, "node_modules/@angular-devkit/core": {"version": "16.2.16", "dev": true, "license": "MIT", "dependencies": {"ajv": "8.12.0", "ajv-formats": "2.1.1", "jsonc-parser": "3.2.0", "picomatch": "2.3.1", "rxjs": "7.8.1", "source-map": "0.7.4"}, "engines": {"node": "^16.14.0 || >=18.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "peerDependencies": {"chokidar": "^3.5.2"}, "peerDependenciesMeta": {"chokidar": {"optional": true}}}, "node_modules/@angular-devkit/schematics": {"version": "16.2.16", "dev": true, "license": "MIT", "dependencies": {"@angular-devkit/core": "16.2.16", "jsonc-parser": "3.2.0", "magic-string": "0.30.1", "ora": "5.4.1", "rxjs": "7.8.1"}, "engines": {"node": "^16.14.0 || >=18.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}, "node_modules/@angular-eslint/builder": {"version": "16.3.1", "dev": true, "license": "MIT", "dependencies": {"@nx/devkit": "16.5.1", "nx": "16.5.1"}, "peerDependencies": {"eslint": "^7.20.0 || ^8.0.0", "typescript": "*"}}, "node_modules/@angular-eslint/bundled-angular-compiler": {"version": "16.3.1", "dev": true, "license": "MIT"}, "node_modules/@angular-eslint/eslint-plugin": {"version": "16.3.1", "dev": true, "license": "MIT", "dependencies": {"@angular-eslint/utils": "16.3.1", "@typescript-eslint/utils": "5.62.0"}, "peerDependencies": {"eslint": "^7.20.0 || ^8.0.0", "typescript": "*"}}, "node_modules/@angular-eslint/eslint-plugin-template": {"version": "16.3.1", "dev": true, "license": "MIT", "dependencies": {"@angular-eslint/bundled-angular-compiler": "16.3.1", "@angular-eslint/utils": "16.3.1", "@typescript-eslint/type-utils": "5.62.0", "@typescript-eslint/utils": "5.62.0", "aria-query": "5.3.0", "axobject-query": "4.0.0"}, "peerDependencies": {"eslint": "^7.20.0 || ^8.0.0", "typescript": "*"}}, "node_modules/@angular-eslint/eslint-plugin-template/node_modules/@typescript-eslint/scope-manager": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@angular-eslint/eslint-plugin-template/node_modules/@typescript-eslint/type-utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "5.62.0", "@typescript-eslint/utils": "5.62.0", "debug": "^4.3.4", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@angular-eslint/eslint-plugin-template/node_modules/@typescript-eslint/types": {"version": "5.62.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@angular-eslint/eslint-plugin-template/node_modules/@typescript-eslint/typescript-estree": {"version": "5.62.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@angular-eslint/eslint-plugin-template/node_modules/@typescript-eslint/utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@angular-eslint/eslint-plugin-template/node_modules/@typescript-eslint/visitor-keys": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@angular-eslint/eslint-plugin-template/node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@angular-eslint/eslint-plugin-template/node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/@angular-eslint/eslint-plugin/node_modules/@typescript-eslint/scope-manager": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@angular-eslint/eslint-plugin/node_modules/@typescript-eslint/types": {"version": "5.62.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@angular-eslint/eslint-plugin/node_modules/@typescript-eslint/typescript-estree": {"version": "5.62.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@angular-eslint/eslint-plugin/node_modules/@typescript-eslint/utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@angular-eslint/eslint-plugin/node_modules/@typescript-eslint/visitor-keys": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@angular-eslint/eslint-plugin/node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@angular-eslint/eslint-plugin/node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/@angular-eslint/schematics": {"version": "16.3.1", "dev": true, "license": "MIT", "dependencies": {"@angular-eslint/eslint-plugin": "16.3.1", "@angular-eslint/eslint-plugin-template": "16.3.1", "@nx/devkit": "16.5.1", "ignore": "5.2.4", "nx": "16.5.1", "strip-json-comments": "3.1.1", "tmp": "0.2.1"}, "peerDependencies": {"@angular/cli": ">= 16.0.0 < 17.0.0"}}, "node_modules/@angular-eslint/schematics/node_modules/ignore": {"version": "5.2.4", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@angular-eslint/schematics/node_modules/tmp": {"version": "0.2.1", "dev": true, "license": "MIT", "dependencies": {"rimraf": "^3.0.0"}, "engines": {"node": ">=8.17.0"}}, "node_modules/@angular-eslint/template-parser": {"version": "16.3.1", "dev": true, "license": "MIT", "dependencies": {"@angular-eslint/bundled-angular-compiler": "16.3.1", "eslint-scope": "^7.0.0"}, "peerDependencies": {"eslint": "^7.20.0 || ^8.0.0", "typescript": "*"}}, "node_modules/@angular-eslint/utils": {"version": "16.3.1", "dev": true, "license": "MIT", "dependencies": {"@angular-eslint/bundled-angular-compiler": "16.3.1", "@typescript-eslint/utils": "5.62.0"}, "peerDependencies": {"eslint": "^7.20.0 || ^8.0.0", "typescript": "*"}}, "node_modules/@angular-eslint/utils/node_modules/@typescript-eslint/scope-manager": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@angular-eslint/utils/node_modules/@typescript-eslint/types": {"version": "5.62.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@angular-eslint/utils/node_modules/@typescript-eslint/typescript-estree": {"version": "5.62.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@angular-eslint/utils/node_modules/@typescript-eslint/utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/@angular-eslint/utils/node_modules/@typescript-eslint/visitor-keys": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@angular-eslint/utils/node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/@angular-eslint/utils/node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/@angular-extensions/elements": {"version": "16.0.0", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": ">=15", "@angular/core": ">=15", "rxjs": ">=7.5.0"}}, "node_modules/@angular/animations": {"version": "16.2.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/core": "16.2.12"}}, "node_modules/@angular/cli": {"version": "16.2.16", "dev": true, "license": "MIT", "dependencies": {"@angular-devkit/architect": "0.1602.16", "@angular-devkit/core": "16.2.16", "@angular-devkit/schematics": "16.2.16", "@schematics/angular": "16.2.16", "@yarnpkg/lockfile": "1.1.0", "ansi-colors": "4.1.3", "ini": "4.1.1", "inquirer": "8.2.4", "jsonc-parser": "3.2.0", "npm-package-arg": "10.1.0", "npm-pick-manifest": "8.0.1", "open": "8.4.2", "ora": "5.4.1", "pacote": "15.2.0", "resolve": "1.22.2", "semver": "7.5.4", "symbol-observable": "4.0.0", "yargs": "17.7.2"}, "bin": {"ng": "bin/ng.js"}, "engines": {"node": "^16.14.0 || >=18.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}, "node_modules/@angular/common": {"version": "16.2.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/core": "16.2.12", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/compiler": {"version": "16.2.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/core": "16.2.12"}, "peerDependenciesMeta": {"@angular/core": {"optional": true}}}, "node_modules/@angular/compiler-cli": {"version": "16.2.12", "license": "MIT", "dependencies": {"@babel/core": "7.23.2", "@jridgewell/sourcemap-codec": "^1.4.14", "chokidar": "^3.0.0", "convert-source-map": "^1.5.1", "reflect-metadata": "^0.1.2", "semver": "^7.0.0", "tslib": "^2.3.0", "yargs": "^17.2.1"}, "bin": {"ng-xi18n": "bundles/src/bin/ng_xi18n.js", "ngc": "bundles/src/bin/ngc.js", "ngcc": "bundles/ngcc/index.js"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/compiler": "16.2.12", "typescript": ">=4.9.3 <5.2"}}, "node_modules/@angular/compiler-cli/node_modules/@babel/core": {"version": "7.23.2", "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.22.13", "@babel/generator": "^7.23.0", "@babel/helper-compilation-targets": "^7.22.15", "@babel/helper-module-transforms": "^7.23.0", "@babel/helpers": "^7.23.2", "@babel/parser": "^7.23.0", "@babel/template": "^7.22.15", "@babel/traverse": "^7.23.2", "@babel/types": "^7.23.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@angular/compiler-cli/node_modules/@babel/core/node_modules/convert-source-map": {"version": "2.0.0", "license": "MIT"}, "node_modules/@angular/compiler-cli/node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@angular/compiler-cli/node_modules/@babel/generator": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/parser": "^7.26.0", "@babel/types": "^7.26.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@angular/compiler-cli/node_modules/@babel/template": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@angular/compiler-cli/node_modules/jsesc": {"version": "3.0.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/@angular/core": {"version": "16.2.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"rxjs": "^6.5.3 || ^7.4.0", "zone.js": "~0.13.0"}}, "node_modules/@angular/elements": {"version": "16.2.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/core": "16.2.12", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/forms": {"version": "16.2.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/common": "16.2.12", "@angular/core": "16.2.12", "@angular/platform-browser": "16.2.12", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@angular/localize": {"version": "16.2.12", "license": "MIT", "peer": true, "dependencies": {"@babel/core": "7.23.2", "fast-glob": "3.3.0", "yargs": "^17.2.1"}, "bin": {"localize-extract": "tools/bundles/src/extract/cli.js", "localize-migrate": "tools/bundles/src/migrate/cli.js", "localize-translate": "tools/bundles/src/translate/cli.js"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/compiler": "16.2.12", "@angular/compiler-cli": "16.2.12"}}, "node_modules/@angular/localize/node_modules/@babel/core": {"version": "7.23.2", "license": "MIT", "peer": true, "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.22.13", "@babel/generator": "^7.23.0", "@babel/helper-compilation-targets": "^7.22.15", "@babel/helper-module-transforms": "^7.23.0", "@babel/helpers": "^7.23.2", "@babel/parser": "^7.23.0", "@babel/template": "^7.22.15", "@babel/traverse": "^7.23.2", "@babel/types": "^7.23.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@angular/localize/node_modules/@babel/generator": {"version": "7.26.0", "license": "MIT", "peer": true, "dependencies": {"@babel/parser": "^7.26.0", "@babel/types": "^7.26.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@angular/localize/node_modules/@babel/template": {"version": "7.25.9", "license": "MIT", "peer": true, "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@angular/localize/node_modules/convert-source-map": {"version": "2.0.0", "license": "MIT", "peer": true}, "node_modules/@angular/localize/node_modules/fast-glob": {"version": "3.3.0", "license": "MIT", "peer": true, "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/@angular/localize/node_modules/jsesc": {"version": "3.0.2", "license": "MIT", "peer": true, "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/@angular/localize/node_modules/semver": {"version": "6.3.1", "license": "ISC", "peer": true, "bin": {"semver": "bin/semver.js"}}, "node_modules/@angular/platform-browser": {"version": "16.2.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/animations": "16.2.12", "@angular/common": "16.2.12", "@angular/core": "16.2.12"}, "peerDependenciesMeta": {"@angular/animations": {"optional": true}}}, "node_modules/@angular/platform-browser-dynamic": {"version": "16.2.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/common": "16.2.12", "@angular/compiler": "16.2.12", "@angular/core": "16.2.12", "@angular/platform-browser": "16.2.12"}}, "node_modules/@angular/router": {"version": "16.2.12", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "engines": {"node": "^16.14.0 || >=18.10.0"}, "peerDependencies": {"@angular/common": "16.2.12", "@angular/core": "16.2.12", "@angular/platform-browser": "16.2.12", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@assemblyscript/loader": {"version": "0.10.1", "dev": true, "license": "Apache-2.0"}, "node_modules/@babel/code-frame": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.25.9", "js-tokens": "^4.0.0", "picocolors": "^1.0.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.26.0", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.22.9", "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.22.5", "@babel/generator": "^7.22.9", "@babel/helper-compilation-targets": "^7.22.9", "@babel/helper-module-transforms": "^7.22.9", "@babel/helpers": "^7.22.6", "@babel/parser": "^7.22.7", "@babel/template": "^7.22.5", "@babel/traverse": "^7.22.8", "@babel/types": "^7.22.5", "convert-source-map": "^1.7.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.2", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/core/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/generator": {"version": "7.22.9", "license": "MIT", "dependencies": {"@babel/types": "^7.22.5", "@jridgewell/gen-mapping": "^0.3.2", "@jridgewell/trace-mapping": "^0.3.17", "jsesc": "^2.5.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-annotate-as-pure": {"version": "7.22.5", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-builder-binary-assignment-operator-visitor": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.25.9", "@babel/helper-validator-option": "^7.25.9", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-create-class-features-plugin": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-member-expression-to-functions": "^7.25.9", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/helper-replace-supers": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/traverse": "^7.25.9", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-create-class-features-plugin/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "regexpu-core": "^6.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-create-regexp-features-plugin/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/helper-define-polyfill-provider": {"version": "0.6.2", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-plugin-utils": "^7.22.5", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/helper-environment-visitor": {"version": "7.24.7", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.24.7"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-member-expression-to-functions": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-optimise-call-expression": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.25.9", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-remap-async-to-generator": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-wrap-function": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-remap-async-to-generator/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-replace-supers": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.25.9", "@babel/helper-optimise-call-expression": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-simple-access": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-split-export-declaration": {"version": "7.22.6", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.25.9", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-wrap-function": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.25.9", "@babel/traverse": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-wrap-function/node_modules/@babel/template": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/template": "^7.25.9", "@babel/types": "^7.26.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers/node_modules/@babel/template": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.26.1", "license": "MIT", "dependencies": {"@babel/types": "^7.26.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9", "@babel/plugin-transform-optional-chaining": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.13.0"}}, "node_modules/@babel/plugin-proposal-async-generator-functions": {"version": "7.20.7", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-environment-visitor": "^7.18.9", "@babel/helper-plugin-utils": "^7.20.2", "@babel/helper-remap-async-to-generator": "^7.18.9", "@babel/plugin-syntax-async-generators": "^7.8.4"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-private-property-in-object": {"version": "7.21.0-placeholder-for-preset-env.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-proposal-unicode-property-regex": {"version": "7.18.6", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-dynamic-import": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-export-namespace-from": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.3"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-assertions": {"version": "7.26.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.26.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-unicode-sets-regex": {"version": "7.18.6", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-generator-functions": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-remap-async-to-generator": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.22.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-remap-async-to-generator": "^7.22.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-block-scoping": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-class-properties": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-class-static-block": {"version": "7.26.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.12.0"}}, "node_modules/@babel/plugin-transform-classes": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-compilation-targets": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-replace-supers": "^7.25.9", "@babel/traverse": "^7.25.9", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-classes/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-computed-properties": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/template": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-computed-properties/node_modules/@babel/template": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-destructuring": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-dynamic-import": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-builder-binary-assignment-operator-visitor": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-export-namespace-from": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-for-of": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-function-name": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-json-strings": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-literals": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-logical-assignment-operators": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-member-expression-literals": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-amd": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-simple-access": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9", "@babel/traverse": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-modules-umd": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/plugin-transform-new-target": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-numeric-separator": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-rest-spread": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9", "@babel/plugin-transform-parameters": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-object-super": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-replace-supers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-catch-binding": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-optional-chaining": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-parameters": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-methods": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-property-in-object": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.25.9", "@babel/helper-create-class-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-private-property-in-object/node_modules/@babel/helper-annotate-as-pure": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/plugin-transform-property-literals": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-regenerator": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "regenerator-transform": "^0.15.2"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-reserved-words": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime": {"version": "7.22.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.22.5", "@babel/helper-plugin-utils": "^7.22.5", "babel-plugin-polyfill-corejs2": "^0.4.4", "babel-plugin-polyfill-corejs3": "^0.8.2", "babel-plugin-polyfill-regenerator": "^0.5.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-runtime/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-spread": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9", "@babel/helper-skip-transparent-expression-wrappers": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-template-literals": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-escapes": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-property-regex": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-transform-unicode-sets-regex": {"version": "7.25.9", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.25.9", "@babel/helper-plugin-utils": "^7.25.9"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/preset-env": {"version": "7.22.9", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.22.9", "@babel/helper-compilation-targets": "^7.22.9", "@babel/helper-plugin-utils": "^7.22.5", "@babel/helper-validator-option": "^7.22.5", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.22.5", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.22.5", "@babel/plugin-proposal-private-property-in-object": "7.21.0-placeholder-for-preset-env.2", "@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-export-namespace-from": "^7.8.3", "@babel/plugin-syntax-import-assertions": "^7.22.5", "@babel/plugin-syntax-import-attributes": "^7.22.5", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5", "@babel/plugin-syntax-unicode-sets-regex": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.22.5", "@babel/plugin-transform-async-generator-functions": "^7.22.7", "@babel/plugin-transform-async-to-generator": "^7.22.5", "@babel/plugin-transform-block-scoped-functions": "^7.22.5", "@babel/plugin-transform-block-scoping": "^7.22.5", "@babel/plugin-transform-class-properties": "^7.22.5", "@babel/plugin-transform-class-static-block": "^7.22.5", "@babel/plugin-transform-classes": "^7.22.6", "@babel/plugin-transform-computed-properties": "^7.22.5", "@babel/plugin-transform-destructuring": "^7.22.5", "@babel/plugin-transform-dotall-regex": "^7.22.5", "@babel/plugin-transform-duplicate-keys": "^7.22.5", "@babel/plugin-transform-dynamic-import": "^7.22.5", "@babel/plugin-transform-exponentiation-operator": "^7.22.5", "@babel/plugin-transform-export-namespace-from": "^7.22.5", "@babel/plugin-transform-for-of": "^7.22.5", "@babel/plugin-transform-function-name": "^7.22.5", "@babel/plugin-transform-json-strings": "^7.22.5", "@babel/plugin-transform-literals": "^7.22.5", "@babel/plugin-transform-logical-assignment-operators": "^7.22.5", "@babel/plugin-transform-member-expression-literals": "^7.22.5", "@babel/plugin-transform-modules-amd": "^7.22.5", "@babel/plugin-transform-modules-commonjs": "^7.22.5", "@babel/plugin-transform-modules-systemjs": "^7.22.5", "@babel/plugin-transform-modules-umd": "^7.22.5", "@babel/plugin-transform-named-capturing-groups-regex": "^7.22.5", "@babel/plugin-transform-new-target": "^7.22.5", "@babel/plugin-transform-nullish-coalescing-operator": "^7.22.5", "@babel/plugin-transform-numeric-separator": "^7.22.5", "@babel/plugin-transform-object-rest-spread": "^7.22.5", "@babel/plugin-transform-object-super": "^7.22.5", "@babel/plugin-transform-optional-catch-binding": "^7.22.5", "@babel/plugin-transform-optional-chaining": "^7.22.6", "@babel/plugin-transform-parameters": "^7.22.5", "@babel/plugin-transform-private-methods": "^7.22.5", "@babel/plugin-transform-private-property-in-object": "^7.22.5", "@babel/plugin-transform-property-literals": "^7.22.5", "@babel/plugin-transform-regenerator": "^7.22.5", "@babel/plugin-transform-reserved-words": "^7.22.5", "@babel/plugin-transform-shorthand-properties": "^7.22.5", "@babel/plugin-transform-spread": "^7.22.5", "@babel/plugin-transform-sticky-regex": "^7.22.5", "@babel/plugin-transform-template-literals": "^7.22.5", "@babel/plugin-transform-typeof-symbol": "^7.22.5", "@babel/plugin-transform-unicode-escapes": "^7.22.5", "@babel/plugin-transform-unicode-property-regex": "^7.22.5", "@babel/plugin-transform-unicode-regex": "^7.22.5", "@babel/plugin-transform-unicode-sets-regex": "^7.22.5", "@babel/preset-modules": "^0.1.5", "@babel/types": "^7.22.5", "babel-plugin-polyfill-corejs2": "^0.4.4", "babel-plugin-polyfill-corejs3": "^0.8.2", "babel-plugin-polyfill-regenerator": "^0.5.1", "core-js-compat": "^3.31.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/preset-env/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/@babel/preset-modules": {"version": "0.1.6", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-proposal-unicode-property-regex": "^7.4.4", "@babel/plugin-transform-dotall-regex": "^7.4.4", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^8.0.0-0 <8.0.0"}}, "node_modules/@babel/runtime": {"version": "7.22.6", "dev": true, "license": "MIT", "dependencies": {"regenerator-runtime": "^0.13.11"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/template": {"version": "7.22.5", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.22.5", "@babel/parser": "^7.22.5", "@babel/types": "^7.22.5"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/generator": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/template": "^7.25.9", "@babel/types": "^7.25.9", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/@babel/generator": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/parser": "^7.26.0", "@babel/types": "^7.26.0", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/@babel/template": {"version": "7.25.9", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.25.9", "@babel/parser": "^7.25.9", "@babel/types": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse/node_modules/jsesc": {"version": "3.0.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/@babel/types": {"version": "7.26.0", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.25.9", "@babel/helper-validator-identifier": "^7.25.9"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@colors/colors": {"version": "1.5.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.1.90"}}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@csstools/css-parser-algorithms": {"version": "2.7.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": "^14 || ^16 || >=18"}, "peerDependencies": {"@csstools/css-tokenizer": "^2.4.1"}}, "node_modules/@csstools/css-tokenizer": {"version": "2.4.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": "^14 || ^16 || >=18"}}, "node_modules/@csstools/media-query-list-parser": {"version": "2.1.13", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": "^14 || ^16 || >=18"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^2.7.1", "@csstools/css-tokenizer": "^2.4.1"}}, "node_modules/@csstools/selector-specificity": {"version": "3.1.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT-0", "engines": {"node": "^14 || ^16 || >=18"}, "peerDependencies": {"postcss-selector-parser": "^6.0.13"}}, "node_modules/@discoveryjs/json-ext": {"version": "0.5.7", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/@dual-bundle/import-meta-resolve": {"version": "4.1.0", "dev": true, "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/@esbuild/win32-x64": {"version": "0.18.17", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">=12"}}, "node_modules/@eslint-community/eslint-utils": {"version": "4.4.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "node_modules/@eslint-community/regexpp": {"version": "4.12.1", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "node_modules/@eslint/eslintrc": {"version": "2.1.4", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/@eslint/eslintrc/node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/@eslint/eslintrc/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/@eslint/eslintrc/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@eslint/eslintrc/node_modules/globals": {"version": "13.24.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/eslintrc/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@eslint/eslintrc/node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/@eslint/eslintrc/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@eslint/eslintrc/node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@eslint/js": {"version": "8.57.1", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "node_modules/@gar/promisify": {"version": "1.1.3", "dev": true, "license": "MIT"}, "node_modules/@humanwhocodes/config-array": {"version": "0.13.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "node_modules/@humanwhocodes/config-array/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@humanwhocodes/config-array/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@isaacs/cliui": {"version": "8.0.2", "dev": true, "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "dev": true, "license": "MIT"}, "node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "dev": true, "license": "ISC", "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.5", "license": "MIT", "dependencies": {"@jridgewell/set-array": "^1.2.1", "@jridgewell/sourcemap-codec": "^1.4.10", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/set-array": {"version": "1.2.1", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/source-map": {"version": "0.3.6", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.0", "license": "MIT"}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.25", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@leichtgewicht/ip-codec": {"version": "2.0.5", "dev": true, "license": "MIT"}, "node_modules/@ng-bootstrap/ng-bootstrap": {"version": "15.1.2", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": "^16.0.0", "@angular/core": "^16.0.0", "@angular/forms": "^16.0.0", "@angular/localize": "^16.0.0", "@popperjs/core": "^2.11.6", "rxjs": "^6.5.3 || ^7.4.0"}}, "node_modules/@ng-icons/core": {"version": "25.6.1", "license": "MIT", "dependencies": {"tslib": "^2.2.0"}}, "node_modules/@ng-icons/material-icons": {"version": "25.6.1", "license": "MIT", "dependencies": {"tslib": "^2.2.0"}}, "node_modules/@ngtools/webpack": {"version": "16.2.16", "dev": true, "license": "MIT", "engines": {"node": "^16.14.0 || >=18.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}, "peerDependencies": {"@angular/compiler-cli": "^16.0.0", "typescript": ">=4.9.3 <5.2", "webpack": "^5.54.0"}}, "node_modules/@ngx-translate/core": {"version": "15.0.0", "license": "SEE LICENSE IN LICENSE", "engines": {"node": "^16.13.0 || >=18.10.0"}, "peerDependencies": {"@angular/common": ">=16.0.0", "@angular/core": ">=16.0.0", "rxjs": "^6.5.5 || ^7.4.0"}}, "node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.stat": {"version": "2.0.5", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/@nodelib/fs.walk": {"version": "1.2.8", "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "node_modules/@npmcli/fs": {"version": "3.1.1", "dev": true, "license": "ISC", "dependencies": {"semver": "^7.3.5"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/git": {"version": "4.1.0", "dev": true, "license": "ISC", "dependencies": {"@npmcli/promise-spawn": "^6.0.0", "lru-cache": "^7.4.4", "npm-pick-manifest": "^8.0.0", "proc-log": "^3.0.0", "promise-inflight": "^1.0.1", "promise-retry": "^2.0.1", "semver": "^7.3.5", "which": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/git/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/@npmcli/git/node_modules/which": {"version": "3.0.1", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/installed-package-contents": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"npm-bundled": "^3.0.0", "npm-normalize-package-bin": "^3.0.0"}, "bin": {"installed-package-contents": "bin/index.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/move-file": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"mkdirp": "^1.0.4", "rimraf": "^3.0.2"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/@npmcli/move-file/node_modules/mkdirp": {"version": "1.0.4", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/@npmcli/node-gyp": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/promise-spawn": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"which": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/promise-spawn/node_modules/which": {"version": "3.0.1", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/run-script": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"@npmcli/node-gyp": "^3.0.0", "@npmcli/promise-spawn": "^6.0.0", "node-gyp": "^9.0.0", "read-package-json-fast": "^3.0.0", "which": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@npmcli/run-script/node_modules/which": {"version": "3.0.1", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/which.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@nrwl/devkit": {"version": "16.5.1", "dev": true, "license": "MIT", "dependencies": {"@nx/devkit": "16.5.1"}}, "node_modules/@nrwl/tao": {"version": "16.5.1", "dev": true, "license": "MIT", "dependencies": {"nx": "16.5.1"}, "bin": {"tao": "index.js"}}, "node_modules/@nx/devkit": {"version": "16.5.1", "dev": true, "license": "MIT", "dependencies": {"@nrwl/devkit": "16.5.1", "ejs": "^3.1.7", "ignore": "^5.0.4", "semver": "7.5.3", "tmp": "~0.2.1", "tslib": "^2.3.0"}, "peerDependencies": {"nx": ">= 15 <= 17"}}, "node_modules/@nx/devkit/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@nx/devkit/node_modules/semver": {"version": "7.5.3", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@nx/devkit/node_modules/tmp": {"version": "0.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/@nx/devkit/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/@nx/nx-win32-x64-msvc": {"version": "16.5.1", "cpu": ["x64"], "dev": true, "license": "MIT", "optional": true, "os": ["win32"], "engines": {"node": ">= 10"}}, "node_modules/@parcel/watcher": {"version": "2.0.4", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"node-addon-api": "^3.2.1", "node-gyp-build": "^4.3.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "node_modules/@pkgjs/parseargs": {"version": "0.11.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "node_modules/@popperjs/core": {"version": "2.11.8", "license": "MIT", "peer": true, "funding": {"type": "opencollective", "url": "https://opencollective.com/popperjs"}}, "node_modules/@schematics/angular": {"version": "16.2.16", "dev": true, "license": "MIT", "dependencies": {"@angular-devkit/core": "16.2.16", "@angular-devkit/schematics": "16.2.16", "jsonc-parser": "3.2.0"}, "engines": {"node": "^16.14.0 || >=18.10.0", "npm": "^6.11.0 || ^7.5.6 || >=8.0.0", "yarn": ">= 1.13.0"}}, "node_modules/@sigstore/bundle": {"version": "1.1.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@sigstore/protobuf-specs": "^0.2.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@sigstore/protobuf-specs": {"version": "0.2.1", "dev": true, "license": "Apache-2.0", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@sigstore/sign": {"version": "1.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@sigstore/bundle": "^1.1.0", "@sigstore/protobuf-specs": "^0.2.0", "make-fetch-happen": "^11.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@sigstore/sign/node_modules/@tootallnate/once": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/@sigstore/sign/node_modules/http-proxy-agent": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/@sigstore/sign/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/@sigstore/sign/node_modules/make-fetch-happen": {"version": "11.1.1", "dev": true, "license": "ISC", "dependencies": {"agentkeepalive": "^4.2.1", "cacache": "^17.0.0", "http-cache-semantics": "^4.1.1", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "minipass": "^5.0.0", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "socks-proxy-agent": "^7.0.0", "ssri": "^10.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@sigstore/sign/node_modules/minipass-fetch": {"version": "3.0.5", "dev": true, "license": "MIT", "dependencies": {"minipass": "^7.0.3", "minipass-sized": "^1.0.3", "minizlib": "^2.1.2"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "optionalDependencies": {"encoding": "^0.1.13"}}, "node_modules/@sigstore/sign/node_modules/minipass-fetch/node_modules/minipass": {"version": "7.1.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/@sigstore/tuf": {"version": "1.0.3", "dev": true, "license": "Apache-2.0", "dependencies": {"@sigstore/protobuf-specs": "^0.2.0", "tuf-js": "^1.1.7"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@socket.io/component-emitter": {"version": "3.1.2", "dev": true, "license": "MIT"}, "node_modules/@tootallnate/once": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 6"}}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "dev": true, "license": "MIT"}, "node_modules/@tufjs/canonical-json": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@tufjs/models": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"@tufjs/canonical-json": "1.0.0", "minimatch": "^9.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/@types/body-parser": {"version": "1.19.5", "dev": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "node_modules/@types/bonjour": {"version": "3.5.13", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect": {"version": "3.4.38", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/connect-history-api-fallback": {"version": "1.5.4", "dev": true, "license": "MIT", "dependencies": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "node_modules/@types/cookie": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/@types/cors": {"version": "2.8.17", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/estree": {"version": "1.0.6", "dev": true, "license": "MIT"}, "node_modules/@types/express": {"version": "4.17.21", "dev": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "node_modules/@types/express-serve-static-core": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/express/node_modules/@types/express-serve-static-core": {"version": "4.19.6", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "node_modules/@types/grecaptcha": {"version": "3.0.9", "license": "MIT"}, "node_modules/@types/http-errors": {"version": "2.0.4", "dev": true, "license": "MIT"}, "node_modules/@types/http-proxy": {"version": "1.17.15", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/jasmine": {"version": "4.3.6", "dev": true, "license": "MIT"}, "node_modules/@types/json-schema": {"version": "7.0.15", "dev": true, "license": "MIT"}, "node_modules/@types/mime": {"version": "1.3.5", "dev": true, "license": "MIT"}, "node_modules/@types/node": {"version": "22.8.4", "dev": true, "license": "MIT", "dependencies": {"undici-types": "~6.19.8"}}, "node_modules/@types/node-forge": {"version": "1.3.11", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/qs": {"version": "6.9.16", "dev": true, "license": "MIT"}, "node_modules/@types/range-parser": {"version": "1.2.7", "dev": true, "license": "MIT"}, "node_modules/@types/retry": {"version": "0.12.0", "dev": true, "license": "MIT"}, "node_modules/@types/semver": {"version": "7.5.8", "dev": true, "license": "MIT"}, "node_modules/@types/send": {"version": "0.17.4", "dev": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "node_modules/@types/serve-index": {"version": "1.9.4", "dev": true, "license": "MIT", "dependencies": {"@types/express": "*"}}, "node_modules/@types/serve-static": {"version": "1.15.7", "dev": true, "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "node_modules/@types/sockjs": {"version": "0.3.36", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@types/uuid": {"version": "9.0.8", "dev": true, "license": "MIT"}, "node_modules/@types/ws": {"version": "8.5.12", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/@typescript-eslint/eslint-plugin": {"version": "7.18.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.10.0", "@typescript-eslint/scope-manager": "7.18.0", "@typescript-eslint/type-utils": "7.18.0", "@typescript-eslint/utils": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0", "graphemer": "^1.4.0", "ignore": "^5.3.1", "natural-compare": "^1.4.0", "ts-api-utils": "^1.3.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^7.0.0", "eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/parser": {"version": "7.18.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "7.18.0", "@typescript-eslint/types": "7.18.0", "@typescript-eslint/typescript-estree": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0", "debug": "^4.3.4"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/scope-manager": {"version": "7.18.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/type-utils": {"version": "7.18.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "7.18.0", "@typescript-eslint/utils": "7.18.0", "debug": "^4.3.4", "ts-api-utils": "^1.3.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/types": {"version": "7.18.0", "dev": true, "license": "MIT", "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@typescript-eslint/typescript-estree": {"version": "7.18.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "7.18.0", "@typescript-eslint/visitor-keys": "7.18.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "minimatch": "^9.0.4", "semver": "^7.6.0", "ts-api-utils": "^1.3.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/@typescript-eslint/typescript-estree/node_modules/semver": {"version": "7.6.3", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@typescript-eslint/utils": {"version": "7.18.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "@typescript-eslint/scope-manager": "7.18.0", "@typescript-eslint/types": "7.18.0", "@typescript-eslint/typescript-estree": "7.18.0"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^8.56.0"}}, "node_modules/@typescript-eslint/visitor-keys": {"version": "7.18.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "7.18.0", "eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^18.18.0 || >=20.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "node_modules/@ungap/structured-clone": {"version": "1.2.0", "dev": true, "license": "ISC"}, "node_modules/@vitejs/plugin-basic-ssl": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=14.6.0"}, "peerDependencies": {"vite": "^3.0.0 || ^4.0.0"}}, "node_modules/@webassemblyjs/ast": {"version": "1.12.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6"}}, "node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.11.6", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-api-error": {"version": "1.11.6", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-buffer": {"version": "1.12.1", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-numbers": {"version": "1.11.6", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.11.6", "@webassemblyjs/helper-api-error": "1.11.6", "@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.11.6", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.12.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/helper-buffer": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.6", "@webassemblyjs/wasm-gen": "1.12.1"}}, "node_modules/@webassemblyjs/ieee754": {"version": "1.11.6", "dev": true, "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "node_modules/@webassemblyjs/leb128": {"version": "1.11.6", "dev": true, "license": "Apache-2.0", "dependencies": {"@xtuc/long": "4.2.2"}}, "node_modules/@webassemblyjs/utf8": {"version": "1.11.6", "dev": true, "license": "MIT"}, "node_modules/@webassemblyjs/wasm-edit": {"version": "1.12.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/helper-buffer": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.6", "@webassemblyjs/helper-wasm-section": "1.12.1", "@webassemblyjs/wasm-gen": "1.12.1", "@webassemblyjs/wasm-opt": "1.12.1", "@webassemblyjs/wasm-parser": "1.12.1", "@webassemblyjs/wast-printer": "1.12.1"}}, "node_modules/@webassemblyjs/wasm-gen": {"version": "1.12.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/helper-wasm-bytecode": "1.11.6", "@webassemblyjs/ieee754": "1.11.6", "@webassemblyjs/leb128": "1.11.6", "@webassemblyjs/utf8": "1.11.6"}}, "node_modules/@webassemblyjs/wasm-opt": {"version": "1.12.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/helper-buffer": "1.12.1", "@webassemblyjs/wasm-gen": "1.12.1", "@webassemblyjs/wasm-parser": "1.12.1"}}, "node_modules/@webassemblyjs/wasm-parser": {"version": "1.12.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@webassemblyjs/helper-api-error": "1.11.6", "@webassemblyjs/helper-wasm-bytecode": "1.11.6", "@webassemblyjs/ieee754": "1.11.6", "@webassemblyjs/leb128": "1.11.6", "@webassemblyjs/utf8": "1.11.6"}}, "node_modules/@webassemblyjs/wast-printer": {"version": "1.12.1", "dev": true, "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.12.1", "@xtuc/long": "4.2.2"}}, "node_modules/@webcomponents/custom-elements": {"version": "1.6.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@webcomponents/webcomponentsjs": {"version": "2.8.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@wessberg/ts-evaluator": {"version": "0.0.27", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "jsdom": "^16.4.0", "object-path": "^0.11.5", "tslib": "^2.0.3"}, "engines": {"node": ">=10.1.0"}, "funding": {"type": "github", "url": "https://github.com/wessberg/ts-evaluator?sponsor=1"}, "peerDependencies": {"typescript": ">=3.2.x || >= 4.x"}}, "node_modules/@xtuc/ieee754": {"version": "1.2.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/@xtuc/long": {"version": "4.2.2", "dev": true, "license": "Apache-2.0"}, "node_modules/@yarnpkg/lockfile": {"version": "1.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/@yarnpkg/parsers": {"version": "3.0.0-rc.46", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"js-yaml": "^3.10.0", "tslib": "^2.4.0"}, "engines": {"node": ">=14.15.0"}}, "node_modules/@zkochan/js-yaml": {"version": "0.0.6", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/@zkochan/js-yaml/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/abab": {"version": "2.0.6", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/abbrev": {"version": "1.1.1", "dev": true, "license": "ISC"}, "node_modules/accepts": {"version": "1.3.8", "dev": true, "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "node_modules/accepts/node_modules/negotiator": {"version": "0.6.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/acorn": {"version": "8.14.0", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-globals": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"acorn": "^7.1.1", "acorn-walk": "^7.1.1"}}, "node_modules/acorn-globals/node_modules/acorn": {"version": "7.4.1", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-import-attributes": {"version": "1.9.5", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^8"}}, "node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "node_modules/acorn-walk": {"version": "7.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/adjust-sourcemap-loader": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"loader-utils": "^2.0.0", "regex-parser": "^2.2.11"}, "engines": {"node": ">=8.9"}}, "node_modules/adjust-sourcemap-loader/node_modules/loader-utils": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/agent-base": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"debug": "4"}, "engines": {"node": ">= 6.0.0"}}, "node_modules/agentkeepalive": {"version": "4.5.0", "dev": true, "license": "MIT", "dependencies": {"humanize-ms": "^1.2.1"}, "engines": {"node": ">= 8.0.0"}}, "node_modules/aggregate-error": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"clean-stack": "^2.0.0", "indent-string": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/ajv": {"version": "8.12.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/ajv-formats": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "node_modules/ajv-keywords": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "node_modules/ansi-colors": {"version": "4.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-html-community": {"version": "0.0.8", "dev": true, "engines": ["node >= 0.8.0"], "license": "Apache-2.0", "bin": {"ansi-html": "bin/ansi-html"}}, "node_modules/ansi-regex": {"version": "5.0.1", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/aproba": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/are-we-there-yet": {"version": "3.0.1", "dev": true, "license": "ISC", "dependencies": {"delegates": "^1.0.0", "readable-stream": "^3.6.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/arg": {"version": "4.1.3", "dev": true, "license": "MIT"}, "node_modules/argparse": {"version": "1.0.10", "dev": true, "license": "MIT", "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/aria-query": {"version": "5.3.0", "dev": true, "license": "Apache-2.0", "dependencies": {"dequal": "^2.0.3"}}, "node_modules/array-flatten": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/array-union": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/astral-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/async": {"version": "3.2.6", "dev": true, "license": "MIT"}, "node_modules/asynckit": {"version": "0.4.0", "dev": true, "license": "MIT"}, "node_modules/autoprefixer": {"version": "10.4.14", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}], "license": "MIT", "dependencies": {"browserslist": "^4.21.5", "caniuse-lite": "^1.0.30001464", "fraction.js": "^4.2.0", "normalize-range": "^0.1.2", "picocolors": "^1.0.0", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/axios": {"version": "1.7.7", "dev": true, "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "node_modules/axios/node_modules/form-data": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/axobject-query": {"version": "4.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"dequal": "^2.0.3"}}, "node_modules/babel-loader": {"version": "9.1.3", "dev": true, "license": "MIT", "dependencies": {"find-cache-dir": "^4.0.0", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@babel/core": "^7.12.0", "webpack": ">=5"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-polyfill-corejs2": {"version": "0.4.11", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.22.6", "@babel/helper-define-polyfill-provider": "^0.6.2", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-corejs2/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/babel-plugin-polyfill-corejs3": {"version": "0.8.7", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.4.4", "core-js-compat": "^3.33.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-corejs3/node_modules/@babel/helper-define-polyfill-provider": {"version": "0.4.4", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-plugin-utils": "^7.22.5", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-regenerator": {"version": "0.5.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.5.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/babel-plugin-polyfill-regenerator/node_modules/@babel/helper-define-polyfill-provider": {"version": "0.5.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.22.6", "@babel/helper-plugin-utils": "^7.22.5", "debug": "^4.1.1", "lodash.debounce": "^4.0.8", "resolve": "^1.14.2"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/base64-js": {"version": "1.5.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/base64id": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": "^4.5.0 || >= 5.9"}}, "node_modules/batch": {"version": "0.6.1", "dev": true, "license": "MIT"}, "node_modules/big.js": {"version": "5.2.2", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/binary-extensions": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/bl": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "node_modules/body-parser": {"version": "1.20.3", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/bonjour-service": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.5"}}, "node_modules/boolbase": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/bootstrap": {"version": "5.3.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/twbs"}, {"type": "opencollective", "url": "https://opencollective.com/bootstrap"}], "license": "MIT", "peerDependencies": {"@popperjs/core": "^2.11.8"}}, "node_modules/brace-expansion": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browser-process-hrtime": {"version": "1.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/browserslist": {"version": "4.24.2", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001669", "electron-to-chromium": "^1.5.41", "node-releases": "^2.0.18", "update-browserslist-db": "^1.1.1"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/buffer": {"version": "5.7.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "node_modules/buffer-from": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/bytes": {"version": "3.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/cacache": {"version": "17.1.4", "dev": true, "license": "ISC", "dependencies": {"@npmcli/fs": "^3.1.0", "fs-minipass": "^3.0.0", "glob": "^10.2.2", "lru-cache": "^7.7.1", "minipass": "^7.0.3", "minipass-collect": "^1.0.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "p-map": "^4.0.0", "ssri": "^10.0.0", "tar": "^6.1.11", "unique-filename": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/cacache/node_modules/glob": {"version": "10.4.5", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/cacache/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/cacache/node_modules/minipass": {"version": "7.1.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/call-bind": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "5.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001675", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "node_modules/chalk": {"version": "4.1.2", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chardet": {"version": "0.7.0", "dev": true, "license": "MIT"}, "node_modules/chokidar": {"version": "3.5.3", "funding": [{"type": "individual", "url": "https://paulmillr.com/funding/"}], "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/chownr": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/chrome-trace-event": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"node": ">=6.0"}}, "node_modules/clean-stack": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/cli-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "node_modules/cli-spinners": {"version": "2.9.2", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"slice-ansi": "^5.0.0", "string-width": "^7.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate/node_modules/ansi-regex": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/cli-truncate/node_modules/emoji-regex": {"version": "10.4.0", "dev": true, "license": "MIT"}, "node_modules/cli-truncate/node_modules/string-width": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/cli-truncate/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/cli-width": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": ">= 10"}}, "node_modules/cliui": {"version": "8.0.1", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/clone": {"version": "1.0.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/clone-deep": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/color-convert": {"version": "2.0.1", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "license": "MIT"}, "node_modules/color-support": {"version": "1.1.3", "dev": true, "license": "ISC", "bin": {"color-support": "bin.js"}}, "node_modules/colord": {"version": "2.9.3", "dev": true, "license": "MIT"}, "node_modules/colorette": {"version": "2.0.20", "dev": true, "license": "MIT"}, "node_modules/combined-stream": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/commander": {"version": "2.20.3", "dev": true, "license": "MIT"}, "node_modules/common-path-prefix": {"version": "3.0.0", "dev": true, "license": "ISC"}, "node_modules/compressible": {"version": "2.0.18", "dev": true, "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/compression": {"version": "1.7.4", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.5", "bytes": "3.0.0", "compressible": "~2.0.16", "debug": "2.6.9", "on-headers": "~1.0.2", "safe-buffer": "5.1.2", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/compression/node_modules/bytes": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/compression/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/compression/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/compression/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/concat": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"commander": "^2.9.0"}, "bin": {"concat": "bin/concat"}, "engines": {"node": ">=6"}}, "node_modules/concat-map": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/connect": {"version": "3.7.0", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "finalhandler": "1.1.2", "parseurl": "~1.3.3", "utils-merge": "1.0.1"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/connect-history-api-fallback": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "node_modules/connect/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/connect/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/console-control-strings": {"version": "1.1.0", "dev": true, "license": "ISC"}, "node_modules/content-disposition": {"version": "0.5.4", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "node_modules/content-type": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/convert-source-map": {"version": "1.9.0", "license": "MIT"}, "node_modules/cookie": {"version": "0.7.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/cookie-signature": {"version": "1.0.6", "dev": true, "license": "MIT"}, "node_modules/copy-anything": {"version": "2.0.6", "dev": true, "license": "MIT", "dependencies": {"is-what": "^3.14.1"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "node_modules/copy-webpack-plugin": {"version": "11.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-glob": "^3.2.11", "glob-parent": "^6.0.1", "globby": "^13.1.1", "normalize-path": "^3.0.0", "schema-utils": "^4.0.0", "serialize-javascript": "^6.0.0"}, "engines": {"node": ">= 14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}}, "node_modules/copy-webpack-plugin/node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/copy-webpack-plugin/node_modules/globby": {"version": "13.2.2", "dev": true, "license": "MIT", "dependencies": {"dir-glob": "^3.0.1", "fast-glob": "^3.3.0", "ignore": "^5.2.4", "merge2": "^1.4.1", "slash": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/copy-webpack-plugin/node_modules/slash": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/core-js-compat": {"version": "3.38.1", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.23.3"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "node_modules/core-util-is": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/cors": {"version": "2.8.5", "dev": true, "license": "MIT", "dependencies": {"object-assign": "^4", "vary": "^1"}, "engines": {"node": ">= 0.10"}}, "node_modules/cosmiconfig": {"version": "8.3.6", "dev": true, "license": "MIT", "dependencies": {"import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0", "path-type": "^4.0.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/d-fischer"}, "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/cosmiconfig/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/cosmiconfig/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/create-require": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/critters": {"version": "0.0.20", "dev": true, "license": "Apache-2.0", "dependencies": {"chalk": "^4.1.0", "css-select": "^5.1.0", "dom-serializer": "^2.0.0", "domhandler": "^5.0.2", "htmlparser2": "^8.0.2", "postcss": "^8.4.23", "pretty-bytes": "^5.3.0"}}, "node_modules/cross-spawn": {"version": "7.0.3", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/crypto-js": {"version": "4.2.0", "license": "MIT"}, "node_modules/css-functions-list": {"version": "3.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=12 || >=16"}}, "node_modules/css-loader": {"version": "6.8.1", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.4.21", "postcss-modules-extract-imports": "^3.0.0", "postcss-modules-local-by-default": "^4.0.3", "postcss-modules-scope": "^3.0.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.2.0", "semver": "^7.3.8"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/css-select": {"version": "5.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/css-tree": {"version": "2.3.1", "dev": true, "license": "MIT", "dependencies": {"mdn-data": "2.0.30", "source-map-js": "^1.0.1"}, "engines": {"node": "^10 || ^12.20.0 || ^14.13.0 || >=15.0.0"}}, "node_modules/css-what": {"version": "6.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "node_modules/cssesc": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "node_modules/cssom": {"version": "0.4.4", "dev": true, "license": "MIT"}, "node_modules/cssstyle": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"cssom": "~0.3.6"}, "engines": {"node": ">=8"}}, "node_modules/cssstyle/node_modules/cssom": {"version": "0.3.8", "dev": true, "license": "MIT"}, "node_modules/custom-event": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/data-urls": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"abab": "^2.0.3", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.0.0"}, "engines": {"node": ">=10"}}, "node_modules/date-format": {"version": "4.0.14", "dev": true, "license": "MIT", "engines": {"node": ">=4.0"}}, "node_modules/debug": {"version": "4.3.7", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/decimal.js": {"version": "10.4.3", "dev": true, "license": "MIT"}, "node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/deepmerge-ts": {"version": "5.1.0", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=16.0.0"}}, "node_modules/default-gateway": {"version": "6.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">= 10"}}, "node_modules/defaults": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/define-data-property": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/define-lazy-prop": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/delayed-stream": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "node_modules/delegates": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/depd": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/dequal": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/destroy": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "node_modules/detect-node": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/di": {"version": "0.0.1", "dev": true, "license": "MIT"}, "node_modules/diff": {"version": "4.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.3.1"}}, "node_modules/dir-glob": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/dns-packet": {"version": "5.6.1", "dev": true, "license": "MIT", "dependencies": {"@leichtgewicht/ip-codec": "^2.0.1"}, "engines": {"node": ">=6"}}, "node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "node_modules/dom-serialize": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"custom-event": "~1.0.0", "ent": "~2.2.0", "extend": "^3.0.0", "void-elements": "^2.0.0"}}, "node_modules/dom-serializer": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "node_modules/domelementtype": {"version": "2.3.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/domexception": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"webidl-conversions": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/domexception/node_modules/webidl-conversions": {"version": "5.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/domhandler": {"version": "5.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.3.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "node_modules/domutils": {"version": "3.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "node_modules/dotenv": {"version": "10.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10"}}, "node_modules/duplexer": {"version": "0.1.2", "dev": true, "license": "MIT"}, "node_modules/eastasianwidth": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/ee-first": {"version": "1.1.1", "dev": true, "license": "MIT"}, "node_modules/ejs": {"version": "3.1.10", "dev": true, "license": "Apache-2.0", "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/electron-to-chromium": {"version": "1.5.49", "license": "ISC"}, "node_modules/emoji-regex": {"version": "8.0.0", "license": "MIT"}, "node_modules/emojis-list": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/encodeurl": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/encoding": {"version": "0.1.13", "dev": true, "license": "MIT", "optional": true, "dependencies": {"iconv-lite": "^0.6.2"}}, "node_modules/encoding/node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/end-of-stream": {"version": "1.4.4", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "node_modules/engine.io": {"version": "6.6.2", "dev": true, "license": "MIT", "dependencies": {"@types/cookie": "^0.4.1", "@types/cors": "^2.8.12", "@types/node": ">=10.0.0", "accepts": "~1.3.4", "base64id": "2.0.0", "cookie": "~0.7.2", "cors": "~2.8.5", "debug": "~4.3.1", "engine.io-parser": "~5.2.1", "ws": "~8.17.1"}, "engines": {"node": ">=10.2.0"}}, "node_modules/engine.io-parser": {"version": "5.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "node_modules/engine.io/node_modules/ws": {"version": "8.17.1", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/enhanced-resolve": {"version": "5.17.1", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "node_modules/enquirer": {"version": "2.3.6", "dev": true, "license": "MIT", "dependencies": {"ansi-colors": "^4.1.1"}, "engines": {"node": ">=8.6"}}, "node_modules/ent": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"punycode": "^1.4.1"}, "engines": {"node": ">= 0.4"}}, "node_modules/entities": {"version": "4.5.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "node_modules/env-paths": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/environment": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/err-code": {"version": "2.0.3", "dev": true, "license": "MIT"}, "node_modules/errno": {"version": "0.1.8", "dev": true, "license": "MIT", "optional": true, "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "node_modules/error-ex": {"version": "1.3.2", "dev": true, "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/es-define-property": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.4"}, "engines": {"node": ">= 0.4"}}, "node_modules/es-errors": {"version": "1.3.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "node_modules/es-module-lexer": {"version": "1.5.4", "dev": true, "license": "MIT"}, "node_modules/esbuild": {"version": "0.18.17", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.18.17", "@esbuild/android-arm64": "0.18.17", "@esbuild/android-x64": "0.18.17", "@esbuild/darwin-arm64": "0.18.17", "@esbuild/darwin-x64": "0.18.17", "@esbuild/freebsd-arm64": "0.18.17", "@esbuild/freebsd-x64": "0.18.17", "@esbuild/linux-arm": "0.18.17", "@esbuild/linux-arm64": "0.18.17", "@esbuild/linux-ia32": "0.18.17", "@esbuild/linux-loong64": "0.18.17", "@esbuild/linux-mips64el": "0.18.17", "@esbuild/linux-ppc64": "0.18.17", "@esbuild/linux-riscv64": "0.18.17", "@esbuild/linux-s390x": "0.18.17", "@esbuild/linux-x64": "0.18.17", "@esbuild/netbsd-x64": "0.18.17", "@esbuild/openbsd-x64": "0.18.17", "@esbuild/sunos-x64": "0.18.17", "@esbuild/win32-arm64": "0.18.17", "@esbuild/win32-ia32": "0.18.17", "@esbuild/win32-x64": "0.18.17"}}, "node_modules/esbuild-wasm": {"version": "0.18.17", "dev": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}}, "node_modules/escalade": {"version": "3.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/escape-html": {"version": "1.0.3", "dev": true, "license": "MIT"}, "node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/escodegen": {"version": "2.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "node_modules/escodegen/node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/eslint": {"version": "8.57.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.1", "@humanwhocodes/config-array": "^0.13.0", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-config-prettier": {"version": "10.1.8", "resolved": "https://slmaven.indra.es/nexus/repository/ATCMP_Npm_Group/eslint-config-prettier/-/eslint-config-prettier-10.1.8.tgz", "integrity": "sha512-82GZUjRS0p/jganf6q1rEO25VSoHH0hKPCTrgillPjdI/3bgBhAE1QzHrHTizjpRvy6pGAvKjDJtk2pF9NDq8w==", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "funding": {"url": "https://opencollective.com/eslint-config-prettier"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "node_modules/eslint-scope": {"version": "7.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/eslint/node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/eslint/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/eslint/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/eslint/node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "node_modules/eslint/node_modules/globals": {"version": "13.24.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/eslint/node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/eslint/node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/eslint/node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/eslint/node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/espree": {"version": "9.6.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "node_modules/esprima": {"version": "4.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/esquery": {"version": "1.6.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/etag": {"version": "1.8.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/eventemitter-asyncresource": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/eventemitter3": {"version": "4.0.7", "dev": true, "license": "MIT"}, "node_modules/events": {"version": "3.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.x"}}, "node_modules/execa": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/exponential-backoff": {"version": "3.1.1", "dev": true, "license": "Apache-2.0"}, "node_modules/express": {"version": "4.21.1", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.10", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}}, "node_modules/express/node_modules/cookie": {"version": "0.7.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/express/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/express/node_modules/encodeurl": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/express/node_modules/finalhandler": {"version": "1.3.1", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/express/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/express/node_modules/statuses": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/extend": {"version": "3.0.2", "dev": true, "license": "MIT"}, "node_modules/external-editor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}}, "node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "node_modules/fast-glob": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "node_modules/fastest-levenshtein": {"version": "1.0.16", "dev": true, "license": "MIT", "engines": {"node": ">= 4.9.1"}}, "node_modules/fastq": {"version": "1.17.1", "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "node_modules/faye-websocket": {"version": "0.11.4", "dev": true, "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/figures": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/figures/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/filelist": {"version": "1.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/filelist/node_modules/minimatch": {"version": "5.1.6", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/finalhandler": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "on-finished": "~2.3.0", "parseurl": "~1.3.3", "statuses": "~1.5.0", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/finalhandler/node_modules/on-finished": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/find-cache-dir": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"common-path-prefix": "^3.0.0", "pkg-dir": "^7.0.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/flat": {"version": "5.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "bin": {"flat": "cli.js"}}, "node_modules/flat-cache": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "node_modules/flatted": {"version": "3.3.1", "dev": true, "license": "ISC"}, "node_modules/follow-redirects": {"version": "1.15.9", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "node_modules/foreground-child": {"version": "3.3.0", "dev": true, "license": "ISC", "dependencies": {"cross-spawn": "^7.0.0", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/foreground-child/node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/form-data": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "node_modules/forwarded": {"version": "0.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fraction.js": {"version": "4.3.7", "dev": true, "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "node_modules/fresh": {"version": "0.5.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/fs-constants": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/fs-extra": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "node_modules/fs-minipass": {"version": "3.0.3", "dev": true, "license": "ISC", "dependencies": {"minipass": "^7.0.3"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/fs-minipass/node_modules/minipass": {"version": "7.1.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/fs-monkey": {"version": "1.0.6", "dev": true, "license": "Unlicense"}, "node_modules/fs.realpath": {"version": "1.0.0", "dev": true, "license": "ISC"}, "node_modules/function-bind": {"version": "1.1.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gauge": {"version": "4.0.4", "dev": true, "license": "ISC", "dependencies": {"aproba": "^1.0.3 || ^2.0.0", "color-support": "^1.1.3", "console-control-strings": "^1.1.0", "has-unicode": "^2.0.1", "signal-exit": "^3.0.7", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "wide-align": "^1.1.5"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-east-asian-width": {"version": "1.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/get-intrinsic": {"version": "1.2.4", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2", "has-proto": "^1.0.1", "has-symbols": "^1.0.3", "hasown": "^2.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/get-package-type": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8.0.0"}}, "node_modules/get-stream": {"version": "6.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob": {"version": "7.2.3", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/glob-to-regexp": {"version": "0.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/glob/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/glob/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/global-modules": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"global-prefix": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/global-prefix": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}, "engines": {"node": ">=6"}}, "node_modules/global-prefix/node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC"}, "node_modules/global-prefix/node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/globals": {"version": "11.12.0", "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/globby": {"version": "11.1.0", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/globjoin": {"version": "0.1.4", "dev": true, "license": "MIT"}, "node_modules/gopd": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.1.3"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/graceful-fs": {"version": "4.2.11", "dev": true, "license": "ISC"}, "node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/guess-parser": {"version": "0.4.22", "dev": true, "license": "MIT", "dependencies": {"@wessberg/ts-evaluator": "0.0.27"}, "peerDependencies": {"typescript": ">=3.7.5"}}, "node_modules/handle-thing": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/has-flag": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/has-property-descriptors": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-proto": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-symbols": {"version": "1.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/has-unicode": {"version": "2.0.1", "dev": true, "license": "ISC"}, "node_modules/hasown": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/hdr-histogram-js": {"version": "2.0.3", "dev": true, "license": "BSD", "dependencies": {"@assemblyscript/loader": "^0.10.1", "base64-js": "^1.2.0", "pako": "^1.0.3"}}, "node_modules/hdr-histogram-percentiles-obj": {"version": "3.0.0", "dev": true, "license": "MIT"}, "node_modules/hosted-git-info": {"version": "6.1.1", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^7.5.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/hosted-git-info/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/hpack.js": {"version": "2.1.6", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "node_modules/hpack.js/node_modules/readable-stream": {"version": "2.3.8", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/hpack.js/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "node_modules/hpack.js/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/html-encoding-sniffer": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^1.0.5"}, "engines": {"node": ">=10"}}, "node_modules/html-entities": {"version": "2.5.2", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/mdevils"}, {"type": "patreon", "url": "https://patreon.com/mdevils"}], "license": "MIT"}, "node_modules/html-escaper": {"version": "2.0.2", "dev": true, "license": "MIT"}, "node_modules/html-tags": {"version": "3.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/htmlparser2": {"version": "8.0.2", "dev": true, "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.0.1", "entities": "^4.4.0"}}, "node_modules/http-cache-semantics": {"version": "4.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "node_modules/http-deceiver": {"version": "1.2.7", "dev": true, "license": "MIT"}, "node_modules/http-errors": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/http-errors/node_modules/statuses": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/http-parser-js": {"version": "0.5.8", "dev": true, "license": "MIT"}, "node_modules/http-proxy": {"version": "1.18.1", "dev": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "node_modules/http-proxy-agent": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "1", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/http-proxy-middleware": {"version": "2.0.7", "dev": true, "license": "MIT", "dependencies": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"@types/express": "^4.17.13"}, "peerDependenciesMeta": {"@types/express": {"optional": true}}}, "node_modules/https-proxy-agent": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/human-signals": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "node_modules/humanize-ms": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.0.0"}}, "node_modules/husky": {"version": "9.1.6", "dev": true, "license": "MIT", "bin": {"husky": "bin.js"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/typicode"}}, "node_modules/iconv-lite": {"version": "0.4.24", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "node_modules/icss-utils": {"version": "5.1.0", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/ieee754": {"version": "1.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ignore": {"version": "5.3.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/ignore-walk": {"version": "6.0.5", "dev": true, "license": "ISC", "dependencies": {"minimatch": "^9.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/image-size": {"version": "0.5.5", "dev": true, "license": "MIT", "optional": true, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/immutable": {"version": "4.3.7", "dev": true, "license": "MIT"}, "node_modules/import-fresh": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/import-fresh/node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "node_modules/indent-string": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/infer-owner": {"version": "1.0.4", "dev": true, "license": "ISC"}, "node_modules/inflight": {"version": "1.0.6", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "node_modules/ini": {"version": "4.1.1", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/inquirer": {"version": "8.2.4", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.1", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.21", "mute-stream": "0.0.8", "ora": "^5.4.1", "run-async": "^2.4.0", "rxjs": "^7.5.5", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12.0.0"}}, "node_modules/ip-address": {"version": "9.0.5", "dev": true, "license": "MIT", "dependencies": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">= 12"}}, "node_modules/ip-address/node_modules/sprintf-js": {"version": "1.1.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ipaddr.js": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/is-arrayish": {"version": "0.2.1", "dev": true, "license": "MIT"}, "node_modules/is-binary-path": {"version": "2.1.0", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/is-core-module": {"version": "2.15.1", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-docker": {"version": "2.2.1", "dev": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-interactive": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-lambda": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-plain-obj": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-plain-object": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "node_modules/is-potential-custom-element-name": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/is-stream": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-unicode-supported": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/is-what": {"version": "3.14.1", "dev": true, "license": "MIT"}, "node_modules/is-wsl": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/isbinaryfile": {"version": "4.0.10", "dev": true, "license": "MIT", "engines": {"node": ">= 8.0.0"}, "funding": {"url": "https://github.com/sponsors/gjtorikian/"}}, "node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/isobject": {"version": "3.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "5.2.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument/node_modules/semver": {"version": "6.3.1", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-source-maps/node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jackspeak": {"version": "3.4.3", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "node_modules/jake": {"version": "10.9.2", "dev": true, "license": "Apache-2.0", "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/jake/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/jake/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/jasmine-core": {"version": "4.6.1", "dev": true, "license": "MIT"}, "node_modules/jest-worker": {"version": "27.5.1", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/jiti": {"version": "1.21.6", "dev": true, "license": "MIT", "bin": {"jiti": "bin/jiti.js"}}, "node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "node_modules/js-yaml": {"version": "3.14.1", "dev": true, "license": "MIT", "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsbn": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/jsdom": {"version": "16.7.0", "dev": true, "license": "MIT", "dependencies": {"abab": "^2.0.5", "acorn": "^8.2.4", "acorn-globals": "^6.0.0", "cssom": "^0.4.4", "cssstyle": "^2.3.0", "data-urls": "^2.0.0", "decimal.js": "^10.2.1", "domexception": "^2.0.1", "escodegen": "^2.0.0", "form-data": "^3.0.0", "html-encoding-sniffer": "^2.0.1", "http-proxy-agent": "^4.0.1", "https-proxy-agent": "^5.0.0", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.0", "parse5": "6.0.1", "saxes": "^5.0.1", "symbol-tree": "^3.2.4", "tough-cookie": "^4.0.0", "w3c-hr-time": "^1.0.2", "w3c-xmlserializer": "^2.0.0", "webidl-conversions": "^6.1.0", "whatwg-encoding": "^1.0.5", "whatwg-mimetype": "^2.3.0", "whatwg-url": "^8.5.0", "ws": "^7.4.6", "xml-name-validator": "^3.0.0"}, "engines": {"node": ">=10"}, "peerDependencies": {"canvas": "^2.5.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "node_modules/jsesc": {"version": "2.5.2", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=4"}}, "node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "license": "MIT"}, "node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/jsonc-parser": {"version": "3.2.0", "dev": true, "license": "MIT"}, "node_modules/jsonfile": {"version": "4.0.0", "dev": true, "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/jsonparse": {"version": "1.3.1", "dev": true, "engines": ["node >= 0.2.0"], "license": "MIT"}, "node_modules/karma": {"version": "6.4.4", "dev": true, "license": "MIT", "dependencies": {"@colors/colors": "1.5.0", "body-parser": "^1.19.0", "braces": "^3.0.2", "chokidar": "^3.5.1", "connect": "^3.7.0", "di": "^0.0.1", "dom-serialize": "^2.2.1", "glob": "^7.1.7", "graceful-fs": "^4.2.6", "http-proxy": "^1.18.1", "isbinaryfile": "^4.0.8", "lodash": "^4.17.21", "log4js": "^6.4.1", "mime": "^2.5.2", "minimatch": "^3.0.4", "mkdirp": "^0.5.5", "qjobs": "^1.2.0", "range-parser": "^1.2.1", "rimraf": "^3.0.2", "socket.io": "^4.7.2", "source-map": "^0.6.1", "tmp": "^0.2.1", "ua-parser-js": "^0.7.30", "yargs": "^16.1.1"}, "bin": {"karma": "bin/karma"}, "engines": {"node": ">= 10"}}, "node_modules/karma-chrome-launcher": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"which": "^1.2.1"}}, "node_modules/karma-chrome-launcher/node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "node_modules/karma-coverage": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"istanbul-lib-coverage": "^3.2.0", "istanbul-lib-instrument": "^5.1.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.1", "istanbul-reports": "^3.0.5", "minimatch": "^3.0.4"}, "engines": {"node": ">=10.0.0"}}, "node_modules/karma-coverage/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/karma-coverage/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/karma-jasmine": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"jasmine-core": "^4.1.0"}, "engines": {"node": ">=12"}, "peerDependencies": {"karma": "^6.0.0"}}, "node_modules/karma-jasmine-html-reporter": {"version": "2.1.0", "dev": true, "license": "MIT", "peerDependencies": {"jasmine-core": "^4.0.0 || ^5.0.0", "karma": "^6.0.0", "karma-jasmine": "^5.0.0"}}, "node_modules/karma-source-map-support": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"source-map-support": "^0.5.5"}}, "node_modules/karma/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/karma/node_modules/cliui": {"version": "7.0.4", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/karma/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/karma/node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/karma/node_modules/tmp": {"version": "0.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/karma/node_modules/yargs": {"version": "16.2.0", "dev": true, "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "node_modules/karma/node_modules/yargs-parser": {"version": "20.2.9", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "node_modules/kind-of": {"version": "6.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/klona": {"version": "2.0.6", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/known-css-properties": {"version": "0.34.0", "dev": true, "license": "MIT"}, "node_modules/launch-editor": {"version": "2.9.1", "dev": true, "license": "MIT", "dependencies": {"picocolors": "^1.0.0", "shell-quote": "^1.8.1"}}, "node_modules/less": {"version": "4.1.3", "dev": true, "license": "Apache-2.0", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0"}, "bin": {"lessc": "bin/lessc"}, "engines": {"node": ">=6"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}}, "node_modules/less-loader": {"version": "11.1.0", "dev": true, "license": "MIT", "dependencies": {"klona": "^2.0.4"}, "engines": {"node": ">= 14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}}, "node_modules/less/node_modules/make-dir": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "engines": {"node": ">=6"}}, "node_modules/less/node_modules/mime": {"version": "1.6.0", "dev": true, "license": "MIT", "optional": true, "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/less/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver"}}, "node_modules/less/node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "engines": {"node": ">=0.10.0"}}, "node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/license-webpack-plugin": {"version": "4.0.2", "dev": true, "license": "ISC", "dependencies": {"webpack-sources": "^3.0.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}, "webpack-sources": {"optional": true}}}, "node_modules/lilconfig": {"version": "3.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "dev": true, "license": "MIT"}, "node_modules/lint-staged": {"version": "15.2.10", "dev": true, "license": "MIT", "dependencies": {"chalk": "~5.3.0", "commander": "~12.1.0", "debug": "~4.3.6", "execa": "~8.0.1", "lilconfig": "~3.1.2", "listr2": "~8.2.4", "micromatch": "~4.0.8", "pidtree": "~0.6.0", "string-argv": "~0.3.2", "yaml": "~2.5.0"}, "bin": {"lint-staged": "bin/lint-staged.js"}, "engines": {"node": ">=18.12.0"}, "funding": {"url": "https://opencollective.com/lint-staged"}}, "node_modules/lint-staged/node_modules/chalk": {"version": "5.3.0", "dev": true, "license": "MIT", "engines": {"node": "^12.17.0 || ^14.13 || >=16.0.0"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/lint-staged/node_modules/commander": {"version": "12.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "node_modules/lint-staged/node_modules/execa": {"version": "8.0.1", "dev": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^8.0.1", "human-signals": "^5.0.0", "is-stream": "^3.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^5.1.0", "onetime": "^6.0.0", "signal-exit": "^4.1.0", "strip-final-newline": "^3.0.0"}, "engines": {"node": ">=16.17"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/lint-staged/node_modules/get-stream": {"version": "8.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/human-signals": {"version": "5.0.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=16.17.0"}}, "node_modules/lint-staged/node_modules/is-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/mimic-fn": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/npm-run-path": {"version": "5.3.0", "dev": true, "license": "MIT", "dependencies": {"path-key": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/onetime": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/path-key": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/lint-staged/node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/lint-staged/node_modules/strip-final-newline": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/listr2": {"version": "8.2.5", "dev": true, "license": "MIT", "dependencies": {"cli-truncate": "^4.0.0", "colorette": "^2.0.20", "eventemitter3": "^5.0.1", "log-update": "^6.1.0", "rfdc": "^1.4.1", "wrap-ansi": "^9.0.0"}, "engines": {"node": ">=18.0.0"}}, "node_modules/listr2/node_modules/ansi-regex": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/listr2/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/listr2/node_modules/emoji-regex": {"version": "10.4.0", "dev": true, "license": "MIT"}, "node_modules/listr2/node_modules/eventemitter3": {"version": "5.0.1", "dev": true, "license": "MIT"}, "node_modules/listr2/node_modules/string-width": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/listr2/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/listr2/node_modules/wrap-ansi": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "string-width": "^7.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/loader-runner": {"version": "4.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=6.11.5"}}, "node_modules/loader-utils": {"version": "3.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 12.13.0"}}, "node_modules/locate-path": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "dev": true, "license": "MIT"}, "node_modules/lodash.debounce": {"version": "4.0.8", "dev": true, "license": "MIT"}, "node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "node_modules/lodash.truncate": {"version": "4.4.2", "dev": true, "license": "MIT"}, "node_modules/log-symbols": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^7.0.0", "cli-cursor": "^5.0.0", "slice-ansi": "^7.1.0", "strip-ansi": "^7.1.0", "wrap-ansi": "^9.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/ansi-escapes": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"environment": "^1.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/ansi-regex": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/log-update/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/log-update/node_modules/cli-cursor": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"restore-cursor": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/emoji-regex": {"version": "10.4.0", "dev": true, "license": "MIT"}, "node_modules/log-update/node_modules/is-fullwidth-code-point": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"get-east-asian-width": "^1.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/onetime": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"mimic-function": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/restore-cursor": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"onetime": "^7.0.0", "signal-exit": "^4.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/log-update/node_modules/slice-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "is-fullwidth-code-point": "^5.0.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/log-update/node_modules/string-width": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^10.3.0", "get-east-asian-width": "^1.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/log-update/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/log-update/node_modules/wrap-ansi": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.2.1", "string-width": "^7.0.0", "strip-ansi": "^7.1.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/log4js": {"version": "6.9.1", "dev": true, "license": "Apache-2.0", "dependencies": {"date-format": "^4.0.14", "debug": "^4.3.4", "flatted": "^3.2.7", "rfdc": "^1.3.0", "streamroller": "^3.1.5"}, "engines": {"node": ">=8.0"}}, "node_modules/lru-cache": {"version": "5.1.1", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "node_modules/magic-string": {"version": "0.30.1", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.4.15"}, "engines": {"node": ">=12"}}, "node_modules/make-dir": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-error": {"version": "1.3.6", "dev": true, "license": "ISC"}, "node_modules/make-fetch-happen": {"version": "10.2.1", "dev": true, "license": "ISC", "dependencies": {"agentkeepalive": "^4.2.1", "cacache": "^16.1.0", "http-cache-semantics": "^4.1.0", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "minipass": "^3.1.6", "minipass-collect": "^1.0.2", "minipass-fetch": "^2.0.3", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "socks-proxy-agent": "^7.0.0", "ssri": "^9.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/make-fetch-happen/node_modules/@npmcli/fs": {"version": "2.1.2", "dev": true, "license": "ISC", "dependencies": {"@gar/promisify": "^1.1.3", "semver": "^7.3.5"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/make-fetch-happen/node_modules/@tootallnate/once": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/make-fetch-happen/node_modules/cacache": {"version": "16.1.3", "dev": true, "license": "ISC", "dependencies": {"@npmcli/fs": "^2.1.0", "@npmcli/move-file": "^2.0.0", "chownr": "^2.0.0", "fs-minipass": "^2.1.0", "glob": "^8.0.1", "infer-owner": "^1.0.4", "lru-cache": "^7.7.1", "minipass": "^3.1.6", "minipass-collect": "^1.0.2", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "mkdirp": "^1.0.4", "p-map": "^4.0.0", "promise-inflight": "^1.0.1", "rimraf": "^3.0.2", "ssri": "^9.0.0", "tar": "^6.1.11", "unique-filename": "^2.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/make-fetch-happen/node_modules/fs-minipass": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/make-fetch-happen/node_modules/glob": {"version": "8.1.0", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^5.0.1", "once": "^1.3.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/make-fetch-happen/node_modules/http-proxy-agent": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/make-fetch-happen/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/make-fetch-happen/node_modules/minimatch": {"version": "5.1.6", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/make-fetch-happen/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/make-fetch-happen/node_modules/mkdirp": {"version": "1.0.4", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/make-fetch-happen/node_modules/ssri": {"version": "9.0.1", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.1.1"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/make-fetch-happen/node_modules/unique-filename": {"version": "2.0.1", "dev": true, "license": "ISC", "dependencies": {"unique-slug": "^3.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/make-fetch-happen/node_modules/unique-slug": {"version": "3.0.0", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/make-fetch-happen/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/mathml-tag-names": {"version": "2.1.3", "dev": true, "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "node_modules/mdn-data": {"version": "2.0.30", "dev": true, "license": "CC0-1.0"}, "node_modules/media-typer": {"version": "0.3.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/memfs": {"version": "3.5.3", "dev": true, "license": "Unlicense", "dependencies": {"fs-monkey": "^1.0.4"}, "engines": {"node": ">= 4.0.0"}}, "node_modules/meow": {"version": "13.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-descriptors": {"version": "1.0.3", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/merge-stream": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/merge2": {"version": "1.4.1", "license": "MIT", "engines": {"node": ">= 8"}}, "node_modules/methods": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mime": {"version": "2.6.0", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "node_modules/mime-db": {"version": "1.52.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/mime-types": {"version": "2.1.35", "dev": true, "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/mimic-function": {"version": "5.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/mini-css-extract-plugin": {"version": "2.7.6", "dev": true, "license": "MIT", "dependencies": {"schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "node_modules/minimalistic-assert": {"version": "1.0.1", "dev": true, "license": "ISC"}, "node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/minimist": {"version": "1.2.8", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/minipass": {"version": "5.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=8"}}, "node_modules/minipass-collect": {"version": "1.0.2", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-collect/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-collect/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/minipass-fetch": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"minipass": "^3.1.6", "minipass-sized": "^1.0.3", "minizlib": "^2.1.2"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}, "optionalDependencies": {"encoding": "^0.1.13"}}, "node_modules/minipass-fetch/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-fetch/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/minipass-flush": {"version": "1.0.5", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minipass-flush/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-flush/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/minipass-json-stream": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"jsonparse": "^1.3.1", "minipass": "^3.0.0"}}, "node_modules/minipass-json-stream/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-json-stream/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/minipass-pipeline": {"version": "1.2.4", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-pipeline/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-pipeline/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/minipass-sized": {"version": "1.0.3", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-sized/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minipass-sized/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/minizlib": {"version": "2.1.2", "dev": true, "license": "MIT", "dependencies": {"minipass": "^3.0.0", "yallist": "^4.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/minizlib/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/minizlib/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/mkdirp": {"version": "0.5.6", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.6"}, "bin": {"mkdirp": "bin/cmd.js"}}, "node_modules/mrmime": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=10"}}, "node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "node_modules/multicast-dns": {"version": "7.2.5", "dev": true, "license": "MIT", "dependencies": {"dns-packet": "^5.2.2", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "node_modules/mute-stream": {"version": "0.0.8", "dev": true, "license": "ISC"}, "node_modules/nanoid": {"version": "3.3.7", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "node_modules/needle": {"version": "3.3.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"iconv-lite": "^0.6.3", "sax": "^1.2.4"}, "bin": {"needle": "bin/needle"}, "engines": {"node": ">= 4.4.x"}}, "node_modules/needle/node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/negotiator": {"version": "0.6.4", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/neo-async": {"version": "2.6.2", "dev": true, "license": "MIT"}, "node_modules/ng-lint-staged": {"version": "12.0.4", "dev": true, "license": "MIT", "bin": {"ng-lint-staged": "ng-lint-staged.js"}, "engines": {"node": ">= 6"}}, "node_modules/ng-recaptcha": {"version": "12.0.2", "license": "MIT", "dependencies": {"@types/grecaptcha": "^3.0.3", "tslib": "^2.2.0"}, "peerDependencies": {"@angular/core": "^16.0.0"}}, "node_modules/ngx-cookie-service": {"version": "16.1.0", "license": "MIT", "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@angular/common": "^16.0.0", "@angular/core": "^16.0.0"}}, "node_modules/ngx-device-detector": {"version": "6.0.2", "resolved": "https://slmaven.indra.es/nexus/repository/ATCMP_Npm_Group/ngx-device-detector/-/ngx-device-detector-6.0.2.tgz", "integrity": "sha512-+zaYUYGepNE4vMBA/6kyc1rhuUm5uU4cwbvFEEy0gAY4OarId15zwcb1iyvQbyrLCXhZW0DrPZTBoQxzp7dyVw==", "license": "MIT", "peer": true, "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"@angular/common": "^16.0.0", "@angular/core": "^16.0.0"}}, "node_modules/ngx-translate-multi-http-loader": {"version": "16.0.2", "license": "MIT", "dependencies": {"tslib": "^2.6.2"}, "peerDependencies": {"@angular/common": ">=13.0.0", "@angular/core": ">=13.0.0", "@ngx-translate/core": ">=15.0.0", "deepmerge-ts": "^5.1.0", "rxjs": "^7.8.1"}}, "node_modules/node-addon-api": {"version": "3.2.1", "dev": true, "license": "MIT"}, "node_modules/node-forge": {"version": "1.3.1", "dev": true, "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "node_modules/node-gyp": {"version": "9.4.1", "dev": true, "license": "MIT", "dependencies": {"env-paths": "^2.2.0", "exponential-backoff": "^3.1.1", "glob": "^7.1.4", "graceful-fs": "^4.2.6", "make-fetch-happen": "^10.0.3", "nopt": "^6.0.0", "npmlog": "^6.0.0", "rimraf": "^3.0.2", "semver": "^7.3.5", "tar": "^6.1.2", "which": "^2.0.2"}, "bin": {"node-gyp": "bin/node-gyp.js"}, "engines": {"node": "^12.13 || ^14.13 || >=16"}}, "node_modules/node-gyp-build": {"version": "4.8.2", "dev": true, "license": "MIT", "bin": {"node-gyp-build": "bin.js", "node-gyp-build-optional": "optional.js", "node-gyp-build-test": "build-test.js"}}, "node_modules/node-releases": {"version": "2.0.18", "license": "MIT"}, "node_modules/nopt": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"abbrev": "^1.0.0"}, "bin": {"nopt": "bin/nopt.js"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/normalize-package-data": {"version": "5.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^6.0.0", "is-core-module": "^2.8.1", "semver": "^7.3.5", "validate-npm-package-license": "^3.0.4"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/normalize-range": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/npm-bundled": {"version": "3.0.1", "dev": true, "license": "ISC", "dependencies": {"npm-normalize-package-bin": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-install-checks": {"version": "6.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"semver": "^7.1.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-normalize-package-bin": {"version": "3.0.1", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-package-arg": {"version": "10.1.0", "dev": true, "license": "ISC", "dependencies": {"hosted-git-info": "^6.0.0", "proc-log": "^3.0.0", "semver": "^7.3.5", "validate-npm-package-name": "^5.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-packlist": {"version": "7.0.4", "dev": true, "license": "ISC", "dependencies": {"ignore-walk": "^6.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-pick-manifest": {"version": "8.0.1", "dev": true, "license": "ISC", "dependencies": {"npm-install-checks": "^6.0.0", "npm-normalize-package-bin": "^3.0.0", "npm-package-arg": "^10.0.0", "semver": "^7.3.5"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-registry-fetch": {"version": "14.0.5", "dev": true, "license": "ISC", "dependencies": {"make-fetch-happen": "^11.0.0", "minipass": "^5.0.0", "minipass-fetch": "^3.0.0", "minipass-json-stream": "^1.0.1", "minizlib": "^2.1.2", "npm-package-arg": "^10.0.0", "proc-log": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-registry-fetch/node_modules/@tootallnate/once": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/npm-registry-fetch/node_modules/http-proxy-agent": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/npm-registry-fetch/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/npm-registry-fetch/node_modules/make-fetch-happen": {"version": "11.1.1", "dev": true, "license": "ISC", "dependencies": {"agentkeepalive": "^4.2.1", "cacache": "^17.0.0", "http-cache-semantics": "^4.1.1", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "minipass": "^5.0.0", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "socks-proxy-agent": "^7.0.0", "ssri": "^10.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/npm-registry-fetch/node_modules/minipass-fetch": {"version": "3.0.5", "dev": true, "license": "MIT", "dependencies": {"minipass": "^7.0.3", "minipass-sized": "^1.0.3", "minizlib": "^2.1.2"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "optionalDependencies": {"encoding": "^0.1.13"}}, "node_modules/npm-registry-fetch/node_modules/minipass-fetch/node_modules/minipass": {"version": "7.1.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/npm-run-path": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/npmlog": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"are-we-there-yet": "^3.0.0", "console-control-strings": "^1.1.0", "gauge": "^4.0.3", "set-blocking": "^2.0.0"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/nth-check": {"version": "2.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "node_modules/nwsapi": {"version": "2.2.13", "dev": true, "license": "MIT"}, "node_modules/nx": {"version": "16.5.1", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@nrwl/tao": "16.5.1", "@parcel/watcher": "2.0.4", "@yarnpkg/lockfile": "^1.1.0", "@yarnpkg/parsers": "3.0.0-rc.46", "@zkochan/js-yaml": "0.0.6", "axios": "^1.0.0", "chalk": "^4.1.0", "cli-cursor": "3.1.0", "cli-spinners": "2.6.1", "cliui": "^7.0.2", "dotenv": "~10.0.0", "enquirer": "~2.3.6", "fast-glob": "3.2.7", "figures": "3.2.0", "flat": "^5.0.2", "fs-extra": "^11.1.0", "glob": "7.1.4", "ignore": "^5.0.4", "js-yaml": "4.1.0", "jsonc-parser": "3.2.0", "lines-and-columns": "~2.0.3", "minimatch": "3.0.5", "npm-run-path": "^4.0.1", "open": "^8.4.0", "semver": "7.5.3", "string-width": "^4.2.3", "strong-log-transformer": "^2.1.0", "tar-stream": "~2.2.0", "tmp": "~0.2.1", "tsconfig-paths": "^4.1.2", "tslib": "^2.3.0", "v8-compile-cache": "2.3.0", "yargs": "^17.6.2", "yargs-parser": "21.1.1"}, "bin": {"nx": "bin/nx.js"}, "optionalDependencies": {"@nx/nx-darwin-arm64": "16.5.1", "@nx/nx-darwin-x64": "16.5.1", "@nx/nx-freebsd-x64": "16.5.1", "@nx/nx-linux-arm-gnueabihf": "16.5.1", "@nx/nx-linux-arm64-gnu": "16.5.1", "@nx/nx-linux-arm64-musl": "16.5.1", "@nx/nx-linux-x64-gnu": "16.5.1", "@nx/nx-linux-x64-musl": "16.5.1", "@nx/nx-win32-arm64-msvc": "16.5.1", "@nx/nx-win32-x64-msvc": "16.5.1"}, "peerDependencies": {"@swc-node/register": "^1.4.2", "@swc/core": "^1.2.173"}, "peerDependenciesMeta": {"@swc-node/register": {"optional": true}, "@swc/core": {"optional": true}}}, "node_modules/nx/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/nx/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/nx/node_modules/cli-spinners": {"version": "2.6.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/nx/node_modules/cliui": {"version": "7.0.4", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "node_modules/nx/node_modules/fast-glob": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8"}}, "node_modules/nx/node_modules/fs-extra": {"version": "11.2.0", "dev": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/nx/node_modules/glob": {"version": "7.1.4", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.0.4", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}}, "node_modules/nx/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/nx/node_modules/jsonfile": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/nx/node_modules/lines-and-columns": {"version": "2.0.4", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}, "node_modules/nx/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/nx/node_modules/minimatch": {"version": "3.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/nx/node_modules/semver": {"version": "7.5.3", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/nx/node_modules/tmp": {"version": "0.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/nx/node_modules/universalify": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/nx/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/object-assign": {"version": "4.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/object-inspect": {"version": "1.13.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/object-path": {"version": "0.11.8", "dev": true, "license": "MIT", "engines": {"node": ">= 10.12.0"}}, "node_modules/obuf": {"version": "1.1.2", "dev": true, "license": "MIT"}, "node_modules/on-finished": {"version": "2.4.1", "dev": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "node_modules/on-headers": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/open": {"version": "8.4.2", "dev": true, "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/optionator": {"version": "0.9.4", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/ora": {"version": "5.4.1", "dev": true, "license": "MIT", "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/os-tmpdir": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/p-limit": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-map": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"aggregate-error": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-retry": {"version": "4.6.2", "dev": true, "license": "MIT", "dependencies": {"@types/retry": "0.12.0", "retry": "^0.13.1"}, "engines": {"node": ">=8"}}, "node_modules/p-retry/node_modules/retry": {"version": "0.13.1", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/p-try": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/package-json-from-dist": {"version": "1.0.1", "dev": true, "license": "BlueOak-1.0.0"}, "node_modules/pacote": {"version": "15.2.0", "dev": true, "license": "ISC", "dependencies": {"@npmcli/git": "^4.0.0", "@npmcli/installed-package-contents": "^2.0.1", "@npmcli/promise-spawn": "^6.0.1", "@npmcli/run-script": "^6.0.0", "cacache": "^17.0.0", "fs-minipass": "^3.0.0", "minipass": "^5.0.0", "npm-package-arg": "^10.0.0", "npm-packlist": "^7.0.0", "npm-pick-manifest": "^8.0.0", "npm-registry-fetch": "^14.0.0", "proc-log": "^3.0.0", "promise-retry": "^2.0.1", "read-package-json": "^6.0.0", "read-package-json-fast": "^3.0.0", "sigstore": "^1.3.0", "ssri": "^10.0.0", "tar": "^6.1.11"}, "bin": {"pacote": "lib/bin.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/pako": {"version": "1.0.11", "dev": true, "license": "(MIT AND Zlib)"}, "node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/parse-node-version": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/parse5": {"version": "6.0.1", "dev": true, "license": "MIT"}, "node_modules/parse5-html-rewriting-stream": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"entities": "^4.3.0", "parse5": "^7.0.0", "parse5-sax-parser": "^7.0.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/parse5-html-rewriting-stream/node_modules/parse5": {"version": "7.2.1", "dev": true, "license": "MIT", "dependencies": {"entities": "^4.5.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/parse5-sax-parser": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"parse5": "^7.0.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/parse5-sax-parser/node_modules/parse5": {"version": "7.2.1", "dev": true, "license": "MIT", "dependencies": {"entities": "^4.5.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "node_modules/parseurl": {"version": "1.3.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "dev": true, "license": "MIT"}, "node_modules/path-scurry": {"version": "1.11.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "node_modules/path-to-regexp": {"version": "0.1.10", "dev": true, "license": "MIT"}, "node_modules/path-type": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pidtree": {"version": "0.6.0", "dev": true, "license": "MIT", "bin": {"pidtree": "bin/pidtree.js"}, "engines": {"node": ">=0.10"}}, "node_modules/pify": {"version": "4.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "node_modules/piscina": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"eventemitter-asyncresource": "^1.0.0", "hdr-histogram-js": "^2.0.1", "hdr-histogram-percentiles-obj": "^3.0.0"}, "optionalDependencies": {"nice-napi": "^1.0.2"}}, "node_modules/pkg-dir": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^6.3.0"}, "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-dir/node_modules/find-up": {"version": "6.3.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^7.1.0", "path-exists": "^5.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-dir/node_modules/locate-path": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^6.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-dir/node_modules/p-limit": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^1.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-dir/node_modules/p-locate": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^4.0.0"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/pkg-dir/node_modules/path-exists": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}}, "node_modules/pkg-dir/node_modules/yocto-queue": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=12.20"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/postcss": {"version": "8.4.31", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.6", "picocolors": "^1.0.0", "source-map-js": "^1.0.2"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/postcss-loader": {"version": "7.3.3", "dev": true, "license": "MIT", "dependencies": {"cosmiconfig": "^8.2.0", "jiti": "^1.18.2", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"postcss": "^7.0.0 || ^8.0.1", "webpack": "^5.0.0"}}, "node_modules/postcss-media-query-parser": {"version": "0.2.3", "dev": true, "license": "MIT"}, "node_modules/postcss-modules-extract-imports": {"version": "3.1.0", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-local-by-default": {"version": "4.0.5", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.1.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-scope": {"version": "3.2.0", "dev": true, "license": "ISC", "dependencies": {"postcss-selector-parser": "^6.0.4"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-modules-values": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"icss-utils": "^5.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "node_modules/postcss-resolve-nested-selector": {"version": "0.1.6", "dev": true, "license": "MIT"}, "node_modules/postcss-safe-parser": {"version": "7.0.1", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss-safe-parser"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "engines": {"node": ">=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "node_modules/postcss-scss": {"version": "4.0.9", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss-scss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "engines": {"node": ">=12.0"}, "peerDependencies": {"postcss": "^8.4.29"}}, "node_modules/postcss-selector-parser": {"version": "6.1.2", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "node_modules/postcss-value-parser": {"version": "4.2.0", "dev": true, "license": "MIT"}, "node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "node_modules/prettier": {"version": "3.3.3", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "node_modules/pretty-bytes": {"version": "5.6.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/primeng": {"version": "16.9.1", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": "^16.2.0", "@angular/core": "^16.2.0", "@angular/forms": "^16.2.0", "rxjs": "^6.0.0 || ^7.8.1", "zone.js": "~0.13.0"}}, "node_modules/proc-log": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/process-nextick-args": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/promise-inflight": {"version": "1.0.1", "dev": true, "license": "ISC"}, "node_modules/promise-retry": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"err-code": "^2.0.2", "retry": "^0.12.0"}, "engines": {"node": ">=10"}}, "node_modules/proxy-addr": {"version": "2.0.7", "dev": true, "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "node_modules/proxy-addr/node_modules/ipaddr.js": {"version": "1.9.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "node_modules/proxy-from-env": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/prr": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true}, "node_modules/psl": {"version": "1.9.0", "dev": true, "license": "MIT"}, "node_modules/punycode": {"version": "1.4.1", "dev": true, "license": "MIT"}, "node_modules/qjobs": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.9"}}, "node_modules/qs": {"version": "6.13.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/querystringify": {"version": "2.2.0", "dev": true, "license": "MIT"}, "node_modules/queue-microtask": {"version": "1.2.3", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/randombytes": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "node_modules/range-parser": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/raw-body": {"version": "2.5.2", "dev": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "node_modules/read-package-json": {"version": "6.0.4", "dev": true, "license": "ISC", "dependencies": {"glob": "^10.2.2", "json-parse-even-better-errors": "^3.0.0", "normalize-package-data": "^5.0.0", "npm-normalize-package-bin": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/read-package-json-fast": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"json-parse-even-better-errors": "^3.0.0", "npm-normalize-package-bin": "^3.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/read-package-json-fast/node_modules/json-parse-even-better-errors": {"version": "3.0.2", "dev": true, "license": "MIT", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/read-package-json/node_modules/glob": {"version": "10.4.5", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^3.1.2", "minimatch": "^9.0.4", "minipass": "^7.1.2", "package-json-from-dist": "^1.0.0", "path-scurry": "^1.11.1"}, "bin": {"glob": "dist/esm/bin.mjs"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/read-package-json/node_modules/json-parse-even-better-errors": {"version": "3.0.2", "dev": true, "license": "MIT", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/read-package-json/node_modules/minipass": {"version": "7.1.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/readable-stream": {"version": "3.6.2", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "node_modules/readdirp": {"version": "3.6.0", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "node_modules/reflect-metadata": {"version": "0.1.14", "license": "Apache-2.0"}, "node_modules/regenerate": {"version": "1.4.2", "dev": true, "license": "MIT"}, "node_modules/regenerate-unicode-properties": {"version": "10.2.0", "dev": true, "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "node_modules/regenerator-runtime": {"version": "0.13.11", "dev": true, "license": "MIT"}, "node_modules/regenerator-transform": {"version": "0.15.2", "dev": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.8.4"}}, "node_modules/regex-parser": {"version": "2.3.0", "dev": true, "license": "MIT"}, "node_modules/regexpu-core": {"version": "6.1.1", "dev": true, "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.11.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "node_modules/regjsgen": {"version": "0.8.0", "dev": true, "license": "MIT"}, "node_modules/regjsparser": {"version": "0.11.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~3.0.2"}, "bin": {"regjsparser": "bin/parser"}}, "node_modules/regjsparser/node_modules/jsesc": {"version": "3.0.2", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/require-directory": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/require-from-string": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/requires-port": {"version": "1.0.0", "dev": true, "license": "MIT"}, "node_modules/resolve": {"version": "1.22.2", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.11.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-from": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/resolve-url-loader": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"adjust-sourcemap-loader": "^4.0.0", "convert-source-map": "^1.7.0", "loader-utils": "^2.0.0", "postcss": "^8.2.14", "source-map": "0.6.1"}, "engines": {"node": ">=12"}}, "node_modules/resolve-url-loader/node_modules/loader-utils": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "node_modules/resolve-url-loader/node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/restore-cursor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "node_modules/retry": {"version": "0.12.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/reusify": {"version": "1.0.4", "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "node_modules/rfdc": {"version": "1.4.1", "dev": true, "license": "MIT"}, "node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/rollup": {"version": "3.29.5", "dev": true, "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "node_modules/run-async": {"version": "2.4.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "node_modules/run-parallel": {"version": "1.2.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "node_modules/rxjs": {"version": "7.8.1", "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/safe-buffer": {"version": "5.2.1", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT"}, "node_modules/sass": {"version": "1.64.1", "dev": true, "license": "MIT", "dependencies": {"chokidar": ">=3.0.0 <4.0.0", "immutable": "^4.0.0", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}}, "node_modules/sass-loader": {"version": "13.3.2", "dev": true, "license": "MIT", "dependencies": {"neo-async": "^2.6.2"}, "engines": {"node": ">= 14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"fibers": ">= 3.1.0", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "sass": "^1.3.0", "sass-embedded": "*", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"fibers": {"optional": true}, "node-sass": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}}}, "node_modules/sax": {"version": "1.4.1", "dev": true, "license": "ISC", "optional": true}, "node_modules/saxes": {"version": "5.0.1", "dev": true, "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=10"}}, "node_modules/schema-utils": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/se-ui-components-mf-lib": {"version": "0.176.0-snapshot.20250904132330", "resolved": "https://slmaven.indra.es/nexus/repository/ATCMP_Npm_Group/se-ui-components-mf-lib/-/se-ui-components-mf-lib-0.176.0-snapshot.20250904132330.tgz", "integrity": "sha512-y26kTtYWhjc+DKPJOyRLoKjg0T+bLUgeH2OgkGv3ZJqgKtULVongsWIGPh35987kTmmxglSvDElabdbi9EZfCA==", "dependencies": {"tslib": "^2.3.0"}, "peerDependencies": {"@angular/common": ">=16.0.0 <17.0.0", "@angular/core": ">=16.0.0 <17.0.0", "@ng-bootstrap/ng-bootstrap": "^15.1.1", "@ng-icons/core": ">=25.2.0", "@ng-icons/material-icons": ">=25.2.0", "crypto-js": ">=4.1.1", "ngx-device-detector": "^6.0.0"}}, "node_modules/select-hose": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/selfsigned": {"version": "2.4.1", "dev": true, "license": "MIT", "dependencies": {"@types/node-forge": "^1.3.0", "node-forge": "^1"}, "engines": {"node": ">=10"}}, "node_modules/semver": {"version": "7.5.4", "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/lru-cache": {"version": "6.0.0", "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/semver/node_modules/yallist": {"version": "4.0.0", "license": "ISC"}, "node_modules/send": {"version": "0.19.0", "dev": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/send/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/send/node_modules/mime": {"version": "1.6.0", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "node_modules/send/node_modules/statuses": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/serialize-javascript": {"version": "6.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "node_modules/serve-index": {"version": "1.9.1", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-index/node_modules/debug": {"version": "2.6.9", "dev": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "node_modules/serve-index/node_modules/depd": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/http-errors": {"version": "1.6.3", "dev": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "node_modules/serve-index/node_modules/inherits": {"version": "2.0.3", "dev": true, "license": "ISC"}, "node_modules/serve-index/node_modules/ms": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/serve-index/node_modules/setprototypeof": {"version": "1.1.0", "dev": true, "license": "ISC"}, "node_modules/serve-static": {"version": "1.16.2", "dev": true, "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/serve-static/node_modules/encodeurl": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/set-blocking": {"version": "2.0.0", "dev": true, "license": "ISC"}, "node_modules/set-function-length": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/setprototypeof": {"version": "1.2.0", "dev": true, "license": "ISC"}, "node_modules/shallow-clone": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/shell-quote": {"version": "1.8.1", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/side-channel": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.4", "object-inspect": "^1.13.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/signal-exit": {"version": "3.0.7", "dev": true, "license": "ISC"}, "node_modules/sigstore": {"version": "1.9.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@sigstore/bundle": "^1.1.0", "@sigstore/protobuf-specs": "^0.2.0", "@sigstore/sign": "^1.0.0", "@sigstore/tuf": "^1.0.3", "make-fetch-happen": "^11.0.1"}, "bin": {"sigstore": "bin/sigstore.js"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/sigstore/node_modules/@tootallnate/once": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/sigstore/node_modules/http-proxy-agent": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/sigstore/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/sigstore/node_modules/make-fetch-happen": {"version": "11.1.1", "dev": true, "license": "ISC", "dependencies": {"agentkeepalive": "^4.2.1", "cacache": "^17.0.0", "http-cache-semantics": "^4.1.1", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "minipass": "^5.0.0", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "socks-proxy-agent": "^7.0.0", "ssri": "^10.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/sigstore/node_modules/minipass-fetch": {"version": "3.0.5", "dev": true, "license": "MIT", "dependencies": {"minipass": "^7.0.3", "minipass-sized": "^1.0.3", "minizlib": "^2.1.2"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "optionalDependencies": {"encoding": "^0.1.13"}}, "node_modules/sigstore/node_modules/minipass-fetch/node_modules/minipass": {"version": "7.1.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/slash": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/slice-ansi": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.0.0", "is-fullwidth-code-point": "^4.0.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/slice-ansi/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/slice-ansi/node_modules/is-fullwidth-code-point": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/smart-buffer": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "node_modules/socket.io": {"version": "4.8.1", "dev": true, "license": "MIT", "dependencies": {"accepts": "~1.3.4", "base64id": "~2.0.0", "cors": "~2.8.5", "debug": "~4.3.2", "engine.io": "~6.6.0", "socket.io-adapter": "~2.5.2", "socket.io-parser": "~4.2.4"}, "engines": {"node": ">=10.2.0"}}, "node_modules/socket.io-adapter": {"version": "2.5.5", "dev": true, "license": "MIT", "dependencies": {"debug": "~4.3.4", "ws": "~8.17.1"}}, "node_modules/socket.io-adapter/node_modules/ws": {"version": "8.17.1", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/socket.io-parser": {"version": "4.2.4", "dev": true, "license": "MIT", "dependencies": {"@socket.io/component-emitter": "~3.1.0", "debug": "~4.3.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/sockjs": {"version": "0.3.24", "dev": true, "license": "MIT", "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}}, "node_modules/socks": {"version": "2.8.3", "dev": true, "license": "MIT", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "node_modules/socks-proxy-agent": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^6.0.2", "debug": "^4.3.3", "socks": "^2.6.2"}, "engines": {"node": ">= 10"}}, "node_modules/source-map": {"version": "0.7.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">= 8"}}, "node_modules/source-map-js": {"version": "1.2.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-loader": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"abab": "^2.0.6", "iconv-lite": "^0.6.3", "source-map-js": "^1.0.2"}, "engines": {"node": ">= 14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.72.1"}}, "node_modules/source-map-loader/node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.21", "dev": true, "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/source-map-support/node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "node_modules/spdx-correct": {"version": "3.2.0", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-exceptions": {"version": "2.5.0", "dev": true, "license": "CC-BY-3.0"}, "node_modules/spdx-expression-parse": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "node_modules/spdx-license-ids": {"version": "3.0.20", "dev": true, "license": "CC0-1.0"}, "node_modules/spdy": {"version": "4.0.2", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "engines": {"node": ">=6.0.0"}}, "node_modules/spdy-transport": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "node_modules/sprintf-js": {"version": "1.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/ssri": {"version": "10.0.6", "dev": true, "license": "ISC", "dependencies": {"minipass": "^7.0.3"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/ssri/node_modules/minipass": {"version": "7.1.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/statuses": {"version": "1.5.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/streamroller": {"version": "3.1.5", "dev": true, "license": "MIT", "dependencies": {"date-format": "^4.0.14", "debug": "^4.3.4", "fs-extra": "^8.1.0"}, "engines": {"node": ">=8.0"}}, "node_modules/string_decoder": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "node_modules/string-argv": {"version": "0.3.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.6.19"}}, "node_modules/string-width": {"version": "4.2.3", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/strong-log-transformer": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "dependencies": {"duplexer": "^0.1.1", "minimist": "^1.2.0", "through": "^2.3.4"}, "bin": {"sl-log-transformer": "bin/sl-log-transformer.js"}, "engines": {"node": ">=4"}}, "node_modules/stylelint": {"version": "16.7.0", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/stylelint"}, {"type": "github", "url": "https://github.com/sponsors/stylelint"}], "license": "MIT", "dependencies": {"@csstools/css-parser-algorithms": "^2.7.1", "@csstools/css-tokenizer": "^2.4.1", "@csstools/media-query-list-parser": "^2.1.13", "@csstools/selector-specificity": "^3.1.1", "@dual-bundle/import-meta-resolve": "^4.1.0", "balanced-match": "^2.0.0", "colord": "^2.9.3", "cosmiconfig": "^9.0.0", "css-functions-list": "^3.2.2", "css-tree": "^2.3.1", "debug": "^4.3.5", "fast-glob": "^3.3.2", "fastest-levenshtein": "^1.0.16", "file-entry-cache": "^9.0.0", "global-modules": "^2.0.0", "globby": "^11.1.0", "globjoin": "^0.1.4", "html-tags": "^3.3.1", "ignore": "^5.3.1", "imurmurhash": "^0.1.4", "is-plain-object": "^5.0.0", "known-css-properties": "^0.34.0", "mathml-tag-names": "^2.1.3", "meow": "^13.2.0", "micromatch": "^4.0.7", "normalize-path": "^3.0.0", "picocolors": "^1.0.1", "postcss": "^8.4.39", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^7.0.0", "postcss-selector-parser": "^6.1.0", "postcss-value-parser": "^4.2.0", "resolve-from": "^5.0.0", "string-width": "^4.2.3", "strip-ansi": "^7.1.0", "supports-hyperlinks": "^3.0.0", "svg-tags": "^1.0.0", "table": "^6.8.2", "write-file-atomic": "^5.0.1"}, "bin": {"stylelint": "bin/stylelint.mjs"}, "engines": {"node": ">=18.12.0"}}, "node_modules/stylelint-config-recommended": {"version": "14.0.1", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/stylelint"}, {"type": "github", "url": "https://github.com/sponsors/stylelint"}], "license": "MIT", "engines": {"node": ">=18.12.0"}, "peerDependencies": {"stylelint": "^16.1.0"}}, "node_modules/stylelint-config-recommended-scss": {"version": "14.1.0", "dev": true, "license": "MIT", "dependencies": {"postcss-scss": "^4.0.9", "stylelint-config-recommended": "^14.0.1", "stylelint-scss": "^6.4.0"}, "engines": {"node": ">=18.12.0"}, "peerDependencies": {"postcss": "^8.3.3", "stylelint": "^16.6.1"}, "peerDependenciesMeta": {"postcss": {"optional": true}}}, "node_modules/stylelint-config-standard": {"version": "36.0.1", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/stylelint"}, {"type": "github", "url": "https://github.com/sponsors/stylelint"}], "license": "MIT", "dependencies": {"stylelint-config-recommended": "^14.0.1"}, "engines": {"node": ">=18.12.0"}, "peerDependencies": {"stylelint": "^16.1.0"}}, "node_modules/stylelint-config-standard-scss": {"version": "13.1.0", "dev": true, "license": "MIT", "dependencies": {"stylelint-config-recommended-scss": "^14.0.0", "stylelint-config-standard": "^36.0.0"}, "engines": {"node": ">=18.12.0"}, "peerDependencies": {"postcss": "^8.3.3", "stylelint": "^16.3.1"}, "peerDependenciesMeta": {"postcss": {"optional": true}}}, "node_modules/stylelint-scss": {"version": "6.8.1", "dev": true, "license": "MIT", "dependencies": {"css-tree": "^3.0.0", "is-plain-object": "^5.0.0", "known-css-properties": "^0.34.0", "mdn-data": "^2.11.1", "postcss-media-query-parser": "^0.2.3", "postcss-resolve-nested-selector": "^0.1.6", "postcss-selector-parser": "^6.1.2", "postcss-value-parser": "^4.2.0"}, "engines": {"node": ">=18.12.0"}, "peerDependencies": {"stylelint": "^16.0.2"}}, "node_modules/stylelint-scss/node_modules/css-tree": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"mdn-data": "2.12.1", "source-map-js": "^1.0.1"}, "engines": {"node": "^10 || ^12.20.0 || ^14.13.0 || >=15.0.0"}}, "node_modules/stylelint-scss/node_modules/css-tree/node_modules/mdn-data": {"version": "2.12.1", "dev": true, "license": "CC0-1.0"}, "node_modules/stylelint-scss/node_modules/is-plain-object": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/stylelint-scss/node_modules/mdn-data": {"version": "2.12.2", "dev": true, "license": "CC0-1.0"}, "node_modules/stylelint/node_modules/ansi-regex": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "node_modules/stylelint/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "node_modules/stylelint/node_modules/balanced-match": {"version": "2.0.0", "dev": true, "license": "MIT"}, "node_modules/stylelint/node_modules/cosmiconfig": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"env-paths": "^2.2.1", "import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/d-fischer"}, "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "node_modules/stylelint/node_modules/fast-glob": {"version": "3.3.2", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.4"}, "engines": {"node": ">=8.6.0"}}, "node_modules/stylelint/node_modules/file-entry-cache": {"version": "9.1.0", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^5.0.0"}, "engines": {"node": ">=18"}}, "node_modules/stylelint/node_modules/flat-cache": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.3.1", "keyv": "^4.5.4"}, "engines": {"node": ">=18"}}, "node_modules/stylelint/node_modules/is-plain-object": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/stylelint/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/stylelint/node_modules/postcss": {"version": "8.4.47", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.7", "picocolors": "^1.1.0", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "node_modules/stylelint/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "node_modules/supports-color": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-hyperlinks": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">=14.18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/svg-tags": {"version": "1.0.0", "dev": true}, "node_modules/symbol-observable": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10"}}, "node_modules/symbol-tree": {"version": "3.2.4", "dev": true, "license": "MIT"}, "node_modules/table": {"version": "6.8.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/table/node_modules/slice-ansi": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/tapable": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tar": {"version": "6.2.1", "dev": true, "license": "ISC", "dependencies": {"chownr": "^2.0.0", "fs-minipass": "^2.0.0", "minipass": "^5.0.0", "minizlib": "^2.1.1", "mkdirp": "^1.0.3", "yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/tar-stream": {"version": "2.2.0", "dev": true, "license": "MIT", "dependencies": {"bl": "^4.0.3", "end-of-stream": "^1.4.1", "fs-constants": "^1.0.0", "inherits": "^2.0.3", "readable-stream": "^3.1.1"}, "engines": {"node": ">=6"}}, "node_modules/tar/node_modules/fs-minipass": {"version": "2.1.0", "dev": true, "license": "ISC", "dependencies": {"minipass": "^3.0.0"}, "engines": {"node": ">= 8"}}, "node_modules/tar/node_modules/fs-minipass/node_modules/minipass": {"version": "3.3.6", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/tar/node_modules/mkdirp": {"version": "1.0.4", "dev": true, "license": "MIT", "bin": {"mkdirp": "bin/cmd.js"}, "engines": {"node": ">=10"}}, "node_modules/tar/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "node_modules/terser": {"version": "5.19.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/terser-webpack-plugin": {"version": "5.3.10", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.20", "jest-worker": "^27.4.5", "schema-utils": "^3.1.1", "serialize-javascript": "^6.0.1", "terser": "^5.26.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "node_modules/terser-webpack-plugin/node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/terser-webpack-plugin/node_modules/ajv-keywords": {"version": "3.5.2", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/terser-webpack-plugin/node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/terser-webpack-plugin/node_modules/schema-utils": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/terser-webpack-plugin/node_modules/terser": {"version": "5.36.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.8.2", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "node_modules/test-exclude": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/test-exclude/node_modules/brace-expansion": {"version": "1.1.11", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/test-exclude/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "node_modules/through": {"version": "2.3.8", "dev": true, "license": "MIT"}, "node_modules/thunky": {"version": "1.1.0", "dev": true, "license": "MIT"}, "node_modules/tmp": {"version": "0.0.33", "dev": true, "license": "MIT", "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/toidentifier": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.6"}}, "node_modules/tough-cookie": {"version": "4.1.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "engines": {"node": ">=6"}}, "node_modules/tough-cookie/node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tough-cookie/node_modules/universalify": {"version": "0.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/tr46": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.1.1"}, "engines": {"node": ">=8"}}, "node_modules/tr46/node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/tree-kill": {"version": "1.2.2", "dev": true, "license": "MIT", "bin": {"tree-kill": "cli.js"}}, "node_modules/ts-api-utils": {"version": "1.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=16"}, "peerDependencies": {"typescript": ">=4.2.0"}}, "node_modules/ts-node": {"version": "10.9.2", "dev": true, "license": "MIT", "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/ts-node/node_modules/acorn-walk": {"version": "8.3.4", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/tsconfig-paths": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"json5": "^2.2.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}, "engines": {"node": ">=6"}}, "node_modules/tslib": {"version": "2.8.0", "license": "0BSD"}, "node_modules/tsutils": {"version": "3.21.0", "dev": true, "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "node_modules/tsutils/node_modules/tslib": {"version": "1.14.1", "dev": true, "license": "0BSD"}, "node_modules/tuf-js": {"version": "1.1.7", "dev": true, "license": "MIT", "dependencies": {"@tufjs/models": "1.0.4", "debug": "^4.3.4", "make-fetch-happen": "^11.1.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/tuf-js/node_modules/@tootallnate/once": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "node_modules/tuf-js/node_modules/http-proxy-agent": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/once": "2", "agent-base": "6", "debug": "4"}, "engines": {"node": ">= 6"}}, "node_modules/tuf-js/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/tuf-js/node_modules/make-fetch-happen": {"version": "11.1.1", "dev": true, "license": "ISC", "dependencies": {"agentkeepalive": "^4.2.1", "cacache": "^17.0.0", "http-cache-semantics": "^4.1.1", "http-proxy-agent": "^5.0.0", "https-proxy-agent": "^5.0.0", "is-lambda": "^1.0.1", "lru-cache": "^7.7.1", "minipass": "^5.0.0", "minipass-fetch": "^3.0.0", "minipass-flush": "^1.0.5", "minipass-pipeline": "^1.2.4", "negotiator": "^0.6.3", "promise-retry": "^2.0.1", "socks-proxy-agent": "^7.0.0", "ssri": "^10.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/tuf-js/node_modules/minipass-fetch": {"version": "3.0.5", "dev": true, "license": "MIT", "dependencies": {"minipass": "^7.0.3", "minipass-sized": "^1.0.3", "minizlib": "^2.1.2"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "optionalDependencies": {"encoding": "^0.1.13"}}, "node_modules/tuf-js/node_modules/minipass-fetch/node_modules/minipass": {"version": "7.1.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "node_modules/type-fest": {"version": "0.21.3", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/type-is": {"version": "1.6.18", "dev": true, "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "node_modules/typed-assert": {"version": "1.0.9", "dev": true, "license": "MIT"}, "node_modules/typescript": {"version": "5.1.6", "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/ua-parser-js": {"version": "0.7.39", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/ua-parser-js"}, {"type": "paypal", "url": "https://paypal.me/faisalman"}, {"type": "github", "url": "https://github.com/sponsors/faisalman"}], "license": "MIT", "bin": {"ua-parser-js": "script/cli.js"}, "engines": {"node": "*"}}, "node_modules/undici-types": {"version": "6.19.8", "dev": true, "license": "MIT"}, "node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "node_modules/unicode-match-property-value-ecmascript": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "node_modules/unique-filename": {"version": "3.0.0", "dev": true, "license": "ISC", "dependencies": {"unique-slug": "^4.0.0"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/unique-slug": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/universalify": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "node_modules/unpipe": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/update-browserslist-db": {"version": "1.1.1", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.0"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "node_modules/uri-js/node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/url-parse": {"version": "1.5.10", "dev": true, "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "node_modules/util-deprecate": {"version": "1.0.2", "dev": true, "license": "MIT"}, "node_modules/utils-merge": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "node_modules/uuid": {"version": "8.3.2", "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "node_modules/v8-compile-cache": {"version": "2.3.0", "dev": true, "license": "MIT"}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "dev": true, "license": "MIT"}, "node_modules/validate-npm-package-license": {"version": "3.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "node_modules/validate-npm-package-name": {"version": "5.0.1", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/vary": {"version": "1.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "node_modules/vite": {"version": "4.5.5", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.18.10", "postcss": "^8.4.27", "rollup": "^3.27.1"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "peerDependencies": {"@types/node": ">= 14", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "node_modules/void-elements": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/w3c-hr-time": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"browser-process-hrtime": "^1.0.0"}}, "node_modules/w3c-xmlserializer": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"xml-name-validator": "^3.0.0"}, "engines": {"node": ">=10"}}, "node_modules/watchpack": {"version": "2.4.2", "dev": true, "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "node_modules/wbuf": {"version": "1.7.3", "dev": true, "license": "MIT", "dependencies": {"minimalistic-assert": "^1.0.0"}}, "node_modules/wcwidth": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "node_modules/webidl-conversions": {"version": "6.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=10.4"}}, "node_modules/webpack": {"version": "5.94.0", "dev": true, "license": "MIT", "dependencies": {"@types/estree": "^1.0.5", "@webassemblyjs/ast": "^1.12.1", "@webassemblyjs/wasm-edit": "^1.12.1", "@webassemblyjs/wasm-parser": "^1.12.1", "acorn": "^8.7.1", "acorn-import-attributes": "^1.9.5", "browserslist": "^4.21.10", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.1", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^3.2.0", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.10", "watchpack": "^2.4.1", "webpack-sources": "^3.2.3"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "node_modules/webpack-dev-middleware": {"version": "6.1.2", "dev": true, "license": "MIT", "dependencies": {"colorette": "^2.0.10", "memfs": "^3.4.12", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}}}, "node_modules/webpack-dev-server": {"version": "4.15.1", "dev": true, "license": "MIT", "dependencies": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/express": "^4.17.13", "@types/serve-index": "^1.9.1", "@types/serve-static": "^1.13.10", "@types/sockjs": "^0.3.33", "@types/ws": "^8.5.5", "ansi-html-community": "^0.0.8", "bonjour-service": "^1.0.11", "chokidar": "^3.5.3", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "default-gateway": "^6.0.3", "express": "^4.17.3", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.3", "ipaddr.js": "^2.0.1", "launch-editor": "^2.6.0", "open": "^8.0.9", "p-retry": "^4.5.0", "rimraf": "^3.0.2", "schema-utils": "^4.0.0", "selfsigned": "^2.1.1", "serve-index": "^1.9.1", "sockjs": "^0.3.24", "spdy": "^4.0.2", "webpack-dev-middleware": "^5.3.1", "ws": "^8.13.0"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.37.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}, "webpack-cli": {"optional": true}}}, "node_modules/webpack-dev-server/node_modules/webpack-dev-middleware": {"version": "5.3.4", "dev": true, "license": "MIT", "dependencies": {"colorette": "^2.0.10", "memfs": "^3.4.3", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "node_modules/webpack-dev-server/node_modules/ws": {"version": "8.18.0", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/webpack-merge": {"version": "5.10.0", "dev": true, "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "flat": "^5.0.2", "wildcard": "^2.0.0"}, "engines": {"node": ">=10.0.0"}}, "node_modules/webpack-sources": {"version": "3.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=10.13.0"}}, "node_modules/webpack-subresource-integrity": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"typed-assert": "^1.0.8"}, "engines": {"node": ">= 12"}, "peerDependencies": {"html-webpack-plugin": ">= 5.0.0-beta.1 < 6", "webpack": "^5.12.0"}, "peerDependenciesMeta": {"html-webpack-plugin": {"optional": true}}}, "node_modules/webpack/node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/webpack/node_modules/ajv-keywords": {"version": "3.5.2", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "node_modules/webpack/node_modules/eslint-scope": {"version": "5.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "node_modules/webpack/node_modules/estraverse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "node_modules/webpack/node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "node_modules/webpack/node_modules/schema-utils": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.8", "ajv": "^6.12.5", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "node_modules/websocket-driver": {"version": "0.7.4", "dev": true, "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "node_modules/websocket-extensions": {"version": "0.1.4", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "node_modules/whatwg-encoding": {"version": "1.0.5", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.4.24"}}, "node_modules/whatwg-mimetype": {"version": "2.3.0", "dev": true, "license": "MIT"}, "node_modules/whatwg-url": {"version": "8.7.0", "dev": true, "license": "MIT", "dependencies": {"lodash": "^4.7.0", "tr46": "^2.1.0", "webidl-conversions": "^6.1.0"}, "engines": {"node": ">=10"}}, "node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wide-align": {"version": "1.1.5", "dev": true, "license": "ISC", "dependencies": {"string-width": "^1.0.2 || 2 || 3 || 4"}}, "node_modules/wildcard": {"version": "2.0.1", "dev": true, "license": "MIT"}, "node_modules/word-wrap": {"version": "1.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "node_modules/write-file-atomic": {"version": "5.0.1", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^4.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "node_modules/write-file-atomic/node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/ws": {"version": "7.5.10", "dev": true, "license": "MIT", "engines": {"node": ">=8.3.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": "^5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "node_modules/xml-name-validator": {"version": "3.0.0", "dev": true, "license": "Apache-2.0"}, "node_modules/xmlchars": {"version": "2.2.0", "dev": true, "license": "MIT"}, "node_modules/y18n": {"version": "5.0.8", "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "node_modules/yaml": {"version": "2.5.1", "dev": true, "license": "ISC", "bin": {"yaml": "bin.mjs"}, "engines": {"node": ">= 14"}}, "node_modules/yargs": {"version": "17.7.2", "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yn": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/zone.js": {"version": "0.13.3", "license": "MIT", "dependencies": {"tslib": "^2.3.0"}}}}