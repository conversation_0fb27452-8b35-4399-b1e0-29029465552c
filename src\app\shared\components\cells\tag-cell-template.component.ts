// cell-template.component.ts
import {
  ChangeDetectionStrategy,
  Component,
  Input,
  TemplateRef,
} from '@angular/core';
import {
  CellComponent,
  CellConfig,
  Column,
  FlattenedCell,
  FlattenedRow,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-se-text-tag-cell',
  template: `
    <div
      class="d-flex align-items-center flex-row justify-content-start gap-2 tag-cell-template"
    >
      <div
        *ngIf="value"
        [ngClass]="{
          'text-ellipsis': cellConfig.ellipsis,
          'text-nowrap': cellConfig.nowrap,
        }"
        [ngStyle]="cellConfig['ngStyle']"
      >
        {{ value | translate }}
      </div>

      <se-tag
        *ngIf="cell['rowData']?.nou?.value || cell['rowData']?.modificat?.value"
        [tooltipText]="getTooltip()"
        [tagTheme]="
          (cell['rowData']?.plate?.cellConfig?.tagTheme ||
            cellConfig.tagCell?.tagTheme) ??
          'primary'
        "
        [closable]="false"
      >
        <div class="d-flex align-items-center flex-row gap-1">
          <ng-icon
            *ngIf="cellConfig['iconName']"
            [name]="cellConfig['iconName']"
          ></ng-icon>
          {{
            cell['rowData']?.plate?.cellConfig?.tagValue ||
              cellConfig['tagValue'] | translate
          }}
        </div>
      </se-tag>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TextTagCellComponent implements CellComponent {
  @Input() value: string | undefined;
  @Input() cell!: FlattenedCell;
  @Input() column!: Column;
  @Input() row!: FlattenedRow;
  @Input() cellConfig!: CellConfig;

  getTooltip = (): string | TemplateRef<HTMLElement> | undefined =>
    this.cellConfig.tooltip ? (this.cellConfig.tooltipText ?? this.value) : '';
}
