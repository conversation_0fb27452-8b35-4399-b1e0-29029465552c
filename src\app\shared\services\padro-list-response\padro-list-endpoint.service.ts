import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';
import {
  ListPadroRequest,
  ListPadroResponse,
  SolicitudRequest,
  SolicitudResponse,
} from '@shared/models';

@Injectable({
  providedIn: 'root',
})
export class PadroListEndPointService {
  constructor(private httpService: SeHttpService) {}

  public getListPadro(
    request: ListPadroRequest,
  ): Observable<ListPadroResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `llistat-padro`,
      method: 'post',
      body: request,
    };
    return this.httpService.post<ListPadroResponse>(httpRequest);
  }

  public getListPadroPendingPayments(
    request: ListPadroRequest,
  ): Observable<ListPadroResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `llistat-padro/count-pendent`,
      method: 'post',
      body: request,
    };
    return this.httpService.post<ListPadroResponse>(httpRequest);
  }

  public getListSolicitudes(
    request: SolicitudRequest,
  ): Observable<SolicitudResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `llistat-sollicituds`,
      method: 'post',
      body: request,
    };
    return this.httpService.post<SolicitudResponse>(httpRequest);
  }
}
