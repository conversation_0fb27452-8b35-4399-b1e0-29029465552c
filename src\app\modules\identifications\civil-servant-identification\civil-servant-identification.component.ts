import { DatePipe } from '@angular/common';
import { Component, OnInit } from '@angular/core';
import {
  AbstractControl,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { SeValidations } from 'se-ui-components-mf-lib';
import { IdentificationsService, StorageService } from '@core/services';
import { debounceTime, Subject, takeUntil } from 'rxjs';
import { AppRoutes, IdentificationType, LoginRequest } from '@core/models';
import { CivilServantIdentificationEndpointsService } from './civil-servant-identification-endpoints.service';

@Component({
  selector: 'app-civil-servant-identification',
  templateUrl: './civil-servant-identification.component.html',
})
export class CivilServantIdentificationComponent implements OnInit {
  identificationTypes = {
    PERSONAL: '1',
    PHONE: '2',
  };

  civilServantForm: FormGroup = new FormGroup({
    nif: new FormControl(null, [
      Validators.required,
      SeValidations.dniNieCif(),
    ]),
    representativeNif: new FormControl(null, [SeValidations.dniNieCif()]),
    declaration: new FormControl(null),
    identificationType: new FormControl(null, [Validators.required]),
  });

  protected showResponsibleDeclarationCheckbox = false;

  private areValidNIFs = false;
  private titularNIFStored: string | null = null;
  private representativeNIFStored: string | null = null;
  private unsubscribe = new Subject<void>();
  private currentDate?: Date;

  get showPhoneForm(): boolean {
    return this.identificationTypeCivilValue === this.identificationTypes.PHONE;
  }

  get isDisabledSubmitButton(): boolean {
    // si identificationType es presencial, documents debe tener valor para pasar
    const isValid = this.civilServantForm.valid && this.areValidNIFs;

    return !isValid;
  }

  get identificationTypeCivilValue(): string {
    return this.civilServantForm.get('identificationType')?.value;
  }

  get representativeNIFControl(): AbstractControl | null {
    return this.civilServantForm.get('representativeNif');
  }
  get titularNIFControl(): AbstractControl | null {
    return this.civilServantForm.get('nif');
  }

  get disableValidateBothNIFsButton(): boolean {
    // si es que se agrega el NIF del representante, este debe ser distinto al del titular y validar que tenga formato de NIF
    const hasCorrectFormatRepresentativeNIF =
      this.representativeNIFControl?.valid &&
      this.representativeNIFControl.value &&
      this.representativeNIFControl.value !== this.titularNIFControl?.value;

    // solo es obligatorio el NIF del titular
    const isAbleValidateBothNIFs =
      this.titularNIFControl?.valid &&
      this.titularNIFControl.value &&
      (!this.representativeNIFControl?.value ||
        hasCorrectFormatRepresentativeNIF);

    // se verifica que los NIFs sean válidos y que no se haya validado antes
    const isAbleToValidate = isAbleValidateBothNIFs && !this.areValidNIFs;

    return !isAbleToValidate;
  }

  constructor(
    private datePipe: DatePipe,
    private endpointsService: CivilServantIdentificationEndpointsService,
    private identificationsSrv: IdentificationsService,
    private storeSrv: StorageService,
  ) {}

  ngOnInit(): void {
    this.civilServantForm
      .get('identificationType')
      ?.valueChanges.subscribe((value: string) => {
        this.updateCivilServantForm(value);
      });

    // subscripcion para cuando cambie algo en nif titular o nif representante
    // se pone a false el checkbox y esconderlo y boton de validar disable
    this.titularNIFControl?.valueChanges
      .pipe(takeUntil(this.unsubscribe), debounceTime(500))
      .subscribe((value) => {
        if (value !== this.titularNIFStored && this.areValidNIFs) {
          this.resetNIFsValidation();
        }
      });

    this.representativeNIFControl?.valueChanges
      .pipe(takeUntil(this.unsubscribe), debounceTime(500))
      .subscribe((value) => {
        if (value !== this.representativeNIFStored && this.areValidNIFs) {
          this.resetNIFsValidation();
        }
      });
  }

  private updateCivilServantForm(addForms: string): void {
    if (addForms) {
      if (addForms === this.identificationTypes.PHONE) {
        this.currentDate = new Date();
        this.civilServantForm.addControl(
          'phone',
          new FormControl(null, [Validators.required, SeValidations.phone]),
        );
        this.civilServantForm.addControl(
          'date',
          new FormControl(
            {
              value: this.datePipe.transform(this.currentDate, 'dd/MM/yyyy'),
              disabled: true,
            },
            [Validators.required],
          ),
        );
        this.civilServantForm.addControl(
          'hour',
          new FormControl(
            {
              value: this.datePipe.transform(this.currentDate, 'HH:mm:ss'),
              disabled: true,
            },
            [Validators.required],
          ),
        );
      } else {
        this.civilServantForm.removeControl('phone');
        this.civilServantForm.removeControl('date');
        this.civilServantForm.removeControl('hour');
      }
    }
  }

  protected onSubmit(): void {
    const body: LoginRequest = {
      nifTitular: this.titularNIFControl?.value?.toUpperCase(),
      tipusAtencio: this.identificationTypeCivilValue,
      ...(this.representativeNIFControl?.value &&
        this.representativeNIFControl?.valid && {
          nifRepresentant: this.representativeNIFControl?.value?.toUpperCase(),
        }),
      ...(this.showPhoneForm && {
        mobil: this.civilServantForm.get('phone')?.value,
      }),
    };

    this.identificationsSrv
      .loginCo2(body, IdentificationType.CIVIL_SERVANT, AppRoutes.RECEIPTS)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((response) => {
        this.storeSrv.isInPersonCivilServant =
          this.identificationTypeCivilValue ===
          this.identificationTypes.PERSONAL;
        this.storeSrv.civilServantVehicleNifTitular =
          this.titularNIFControl?.value?.toUpperCase() || '';
        this.storeSrv.civilServantVehicleNameTitular =
          response?.content?.nombreTitular || '';
        this.storeSrv.civilServantVehicleNifRepresentant =
          this.representativeNIFControl?.value?.toUpperCase() || '';
        this.storeSrv.civilServantVehiclePhone =
          this.civilServantForm.get('phone')?.value || '';
        this.storeSrv.civilServantVehicleDate = this.currentDate || new Date();
        this.storeSrv.civilServantVehicleHour = this.currentDate || new Date();
      });
  }

  protected updateDeclarationForm(event: boolean): void {
    this.civilServantForm.get('declaration')?.patchValue(event);
  }

  protected validateNIFs(): void {
    // primero se agrega el NIF titular que es obligatorio y se verifica que exista para activarse
    const body: string[] = [this.titularNIFControl?.value as string];
    // si el usuario agrega el nif representante, se agrega en el body
    if (this.representativeNIFControl?.value) {
      body.push(this.representativeNIFControl.value);
    }

    this.endpointsService
      .verifyCivilServantNIF(body)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((response) => {
        // cuando los valores del objeto response son true, debe mostrar el checkbox de declaracion responsable
        if (response.content) {
          // los valores de cada key son booleans, si el o los booleans son true, aparece el checkbox
          this.showResponsibleDeclarationCheckbox =
            Object.values(response.content).length === 2;
          this.setDeclarationValidation();
          this.areValidNIFs = true;
          // se guarda los NIFs ingresados para ver que si hay un cambio, esconder el checkbox ponerlo a false y deshabilitar el botón de submit
          this.titularNIFStored = this.titularNIFControl?.value as string;
          if (this.representativeNIFControl?.value) {
            this.representativeNIFStored = this.representativeNIFControl.value;
          }
        }
      });
  }

  private setDeclarationValidation(): void {
    this.civilServantForm
      .get('declaration')
      ?.setValidators(
        this.showResponsibleDeclarationCheckbox
          ? [Validators.requiredTrue]
          : [],
      );
    this.civilServantForm.updateValueAndValidity();
  }

  // cuando hay cambios en los NIFs después de ser validados
  private resetNIFsValidation(): void {
    this.showResponsibleDeclarationCheckbox = false;
    this.civilServantForm.get('declaration')?.patchValue(false);
    this.areValidNIFs = false;
  }
}
