<div class="app-reason-allegation pt-3">
  <app-request-summary
    [title]="'SE_PADRO_CO2.ALLEGATIONS.RESUM_TITLE' | translate"
    [reasonColumns]="allegacionsColumns"
    [reasonRows]="allegacionsRows"
    [data]="data"
    [isAllegationsTramit]="true"
    [footerMessage]="''"
    [modalTitle]="'SE_PADRO_CO2.MODAL.VEHICLES.TITLE_ALLEGACIONS' | translate"
  >
    <app-user-data-login-simple-form
      [loginSimpleUserForm]="loginSimpleUserForm"
    >
    </app-user-data-login-simple-form>
  </app-request-summary>

  <div class="mt-4">
    <app-civil-servant-required-doc-block
      [idEntity]="idTramit || ''"
      [idFunctionalModule]="idFunctionalModule"
      [authAlegacio]="true"
      [plates]="plates"
      (disableContinueButton)="onDisableContinueButton($event)"
    >
    </app-civil-servant-required-doc-block>
  </div>

  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button
      type="button"
      btnTheme="secondary"
      (onClick)="onGoBackButtonClick()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.BACK' | translate }}
    </se-button>

    <se-button
      type="submit"
      btnTheme="primary"
      [disabled]="!!loginSimpleUserForm?.invalid || disableContinueButton"
      (onClick)="onContinueButtonClick()"
    >
      {{ 'SE_PADRO_CO2.ALLEGATIONS.SUBMIT' | translate }}
    </se-button>
  </div>
</div>
