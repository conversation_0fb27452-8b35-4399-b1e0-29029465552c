import { SeHttpResponse, iDocumentPadoct } from 'se-ui-components-mf-lib';
import { FunctionalModuleEnumT } from './docs.model';

export interface RequestGetDocuments {
  entityId: string;
  key: string;
  functionalModule: FunctionalModuleEnumT;
  lstCodGTATSigedaType: string[];
  getDocument?: boolean;
}

export interface DocumentsInputData extends RequestGetDocuments {
  idFunctionalModule?: FunctionalModuleEnumT;
  lstCodGTATSigedaTypeTranslations?: { [key: string]: string };
  sigedaDocumentsDescriptions?: SigedaDocumentsDescription[];
  subtypes: string[];
}

export interface SigedaDocumentsDescription {
  type: string;
  subtype: string;
  name?: string;
  description?: string;
  required?: boolean;
  allowedFiles?: string[];
  allowedSize?: number;
}

export interface ResponseGetDocuments extends SeHttpResponse {
  content: {
    documentResponseList: iDocumentPadoct[];
    error: boolean;
  };
}

export interface RequestDeleteDocuments {
  idsBBDD?: string[];
  entityId: string;
  functionalModule: FunctionalModuleEnumT;
  lstCodGTATSigedaType?: string[];
}

export interface ResponseDeleteDocuments extends SeHttpResponse {
  content: number;
}

export interface RequestDeleteFile {
  idDocument: string;
}

export interface ResponseDeleteFile extends SeHttpResponse {
  content: {
    error: boolean;
  };
}

export interface FindDocumentResponse extends SeHttpResponse {
  content?: Partial<{
    idPadoct: string;
    idEntity: string;
    idFunctionalModule: string;
    key: string;
    sigedaType: string;
    codSigedaType: string;
    nomSigedaType: string;
    documentType: string;
    /** @example 'application/pdf' */
    format: string;
    /** @example 'PDF' */
    nomFormat: string;
    /** @example 'Justificant_9470000002714.pdf' */
    nom: string;
    csv: string;
    indActivo: boolean;
    codeDescriptionComplementary: string;
    descriptionComplementary: string;
    description: string;
    /** @example '2023-07-26T10:25:08.083' */
    updatedOn: string;
    /** @example '2023-07-26T10:25:08.083' */
    createdOn: string;
    creatorNif: string;
    creatorName: string;
    /** Size in bytes. @example 727758 */
    size: number;
    origenCiutada: boolean;
    language: string;
    passiveSubject: Array<{ nif: string; nom: string }>;
    sarcatNumber: string;
    sarcatDate: string;
    mongoParentId: string;
    evidenceType: string;
    version: string;
    error: boolean;
    id: string;
    base64File: string;
    status: 'PENDING' | 'TECHNICAL_ERROR' | 'UPLOADED' | 'VALIDATING_ERROR';
  }>;
}

export interface MultiDownloadRequestPayload {
  idDocuments: string[];
}

export interface DownloadResponse {
  base64File: string;
  format: string;
  length: number;
}

export interface DownloadHttpResponse extends SeHttpResponse {
  content?: DownloadResponse;
}
