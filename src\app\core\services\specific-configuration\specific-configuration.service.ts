import { Injectable } from '@angular/core';
import {
  ConfigurationDates,
  MF_CO2_CONFIGURATION_STORAGE,
  MfSpecificConfiguration,
  SpecificConfiguration,
} from '@core/models';
import {
  SeDataStorageService,
  Nullable,
  SeAuthService,
} from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class SpecificConfigurationService {
  private config: Nullable<SpecificConfiguration>;

  constructor(
    private dataStorageService: SeDataStorageService,
    private seAuthService: SeAuthService,
  ) {
    this.setConfig();
  }

  setConfig(): void {
    const mfSpecificConfiguration = this.dataStorageService.getItem(
      MF_CO2_CONFIGURATION_STORAGE,
    ) as MfSpecificConfiguration;
    this.config = mfSpecificConfiguration?.confEspecifica;
  }

  private resetTimeOfDate(date: string | Date, year?: number): Date {
    const formattedDate = new Date(date);

    formattedDate.setHours(0);
    formattedDate.setMinutes(0);
    formattedDate.setSeconds(0);
    formattedDate.setMilliseconds(0);

    if (year) formattedDate.setFullYear(year);

    return formattedDate;
  }

  get currentDate(): Date {
    // return new Date(this.seAuthService.getSessionStorageUser()?.fechaLogin);
    return new Date('2025-11-01T00:00:00.000Z');
  }

  get isProvisional(): boolean {
    const currentYear = this.currentDate.getFullYear();
    const startProvisional = this.resetTimeOfDate(
      this.config?.dates?.publicacioPadroProvisional as string,
      currentYear,
    );
    const endProvisional = this.resetTimeOfDate(
      this.config?.dates?.publicacioPadroDefinitiu as string,
      currentYear,
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    return (
      currentDate.getTime() >= startProvisional.getTime() &&
      currentDate.getTime() < endProvisional.getTime()
    );
  }

  get inTimeToBeDeferred(): boolean {
    const endDate = this.resetTimeOfDate(
      this.config?.dates?.iniciFraccionamentAjornament as string,
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    return currentDate.getTime() >= endDate.getTime();
  }

  get outOfTimeToBeDomiciled(): boolean {
    const endDate = this.resetTimeOfDate(
      this.config?.dates?.limitDomiciliacions as string,
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    return currentDate.getTime() > endDate.getTime();
  }

  get outOfTimeToModifyAccount(): boolean {
    const endDate = this.resetTimeOfDate(
      this.config?.dates?.limitModificacionsCompte as string,
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    return currentDate.getTime() > endDate.getTime();
  }

  get outOfTimeToBeDomiciledButBeforeDefinitivePadro(): boolean {
    const currentYear = this.currentDate.getFullYear();
    const startDate = this.resetTimeOfDate(
      this.config?.dates?.limitDomiciliacions as string,
      currentYear,
    );
    const endDate = this.resetTimeOfDate(
      this.config?.dates?.publicacioPadroDefinitiu as string,
      currentYear,
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    return (
      currentDate.getTime() > startDate.getTime() &&
      currentDate.getTime() < endDate.getTime()
    );
  }

  get inTimeToModifyAccount(): boolean {
    const currentYear = this.currentDate.getFullYear();
    const startDate = this.resetTimeOfDate(
      this.config?.dates?.publicacioPadroProvisional as string,
      currentYear,
    );
    const endDate = this.resetTimeOfDate(
      this.config?.dates?.limitModificacionsCompte as string,
      currentYear,
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    return (
      currentDate.getTime() >= startDate.getTime() &&
      currentDate.getTime() <= endDate.getTime()
    );
  }

  get inTimeToBeDomiciled(): boolean {
    const currentYear = this.currentDate.getFullYear();
    const startDate = this.resetTimeOfDate(
      this.config?.dates?.publicacioPadroProvisional as string,
      currentYear,
    );
    const endDate = this.resetTimeOfDate(
      this.config?.dates?.limitDomiciliacions as string,
      currentYear,
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    return (
      currentDate.getTime() >= startDate.getTime() &&
      currentDate.getTime() <= endDate.getTime()
    );
  }

  get inTimeToBeAlleged(): boolean {
    const currentYear = this.currentDate.getFullYear();
    const startDate = this.resetTimeOfDate(
      this.config?.dates?.publicacioPadroProvisional as string,
      currentYear,
    );
    const endDate = this.resetTimeOfDate(
      this.config?.dates?.limitAlegacions as string,
      currentYear,
    );
    const currentDate2 = this.resetTimeOfDate(this.currentDate);

    return (
      currentDate2.getTime() >= startDate.getTime() &&
      currentDate2.getTime() <= endDate.getTime()
    );
  }

  // *CASOS nextReceiptDate:
  // 1: si la actual fecha está entre startDate y cutoffDate, la fecha es [20/11/año actual]... this.currentDate >= startDate && this.currentDate <= cutoffDate
  // 2: si la actual fecha está entre el 01 de enero y startDate, la fecha es [20/11/año actual]... this.currentDate >= new Date(currentYear, 0, 1) && this.currentDate < startDate
  // 3: si la actual fecha tiene el mismo año que la fecha de fin de domiciliaciones y es mayor que la fecha de fin domiciliaciones va [20/11/año siguiente]...
  // 4: si son años diferentes la fecha actual con el fin de domiciliaciones, ej: fecha actual (2025) y la fecha de fin de domiciliaciones (2024), va [20/11/2025]...
  get nextReceiptDate(): string {
    const currentYear = this.currentDate.getFullYear();
    const startDate = this.resetTimeOfDate(
      this.config?.dates?.publicacioPadroProvisional as string,
    );
    const endDate = this.resetTimeOfDate(
      this.config?.dates?.limitDomiciliacions as string,
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    if (
      currentDate.getTime() >= startDate.getTime() &&
      currentDate.getTime() <= endDate.getTime()
    ) {
      return '20/11/' + currentYear;
    }

    if (currentDate.getFullYear() > startDate.getFullYear()) {
      return '20/11/' + currentYear;
    }

    if (
      currentDate.getFullYear() === startDate.getFullYear() &&
      currentDate.getTime() > endDate.getTime()
    ) {
      return '20/11/' + (currentYear + 1);
    }

    return '20/11/' + currentYear;
  }

  get exerciceDom(): number {
    const currentYear = this.currentDate.getFullYear();
    const startDate = this.resetTimeOfDate(
      this.config?.dates?.publicacioPadroProvisional as string,
    );
    const endDate = this.resetTimeOfDate(
      this.config?.dates?.limitDomiciliacions as string,
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    if (
      currentDate.getTime() >= startDate.getTime() &&
      currentDate.getTime() <= endDate.getTime()
    ) {
      return currentYear - 1;
    }

    if (currentDate.getFullYear() > startDate.getFullYear()) {
      return currentYear - 1;
    }

    if (
      currentDate.getFullYear() === startDate.getFullYear() &&
      currentDate.getTime() > endDate.getTime()
    ) {
      return currentYear;
    }

    return currentYear;
  }

  get publicacioPadroDefinitiu(): string {
    return this.config?.dates?.publicacioPadroDefinitiu as string;
  }

  get fiPeriodeVoluntariPagament(): string {
    return this.config?.dates?.fiPeriodeVoluntariPagament as string;
  }

  get limitDomiciliacions(): string {
    return this.config?.dates?.limitDomiciliacions as string;
  }

  get percentDiscount(): number {
    return (this.config?.percentatgeDescompte as number) * 100;
  }

  get llistaExercicis(): string[] {
    if (this.config?.llistaExercicis.length) {
      const previousYears = [...this.config.llistaExercicis];
      return previousYears.slice(0, -1); // retorna todos los elementos de la lista excepto el último elemento, acuerdo con BE
    }

    return [];
  }

  get dates(): ConfigurationDates {
    return this.config?.dates as ConfigurationDates;
  }

  get isPastExercise(): boolean {
    const startPadroProvisional = this.resetTimeOfDate(
      this.config?.dates?.publicacioPadroProvisional as string,
      this.currentDate.getFullYear(),
    );
    const currentDate = this.resetTimeOfDate(this.currentDate);

    return (
      currentDate.getTime() >=
        new Date(currentDate.getFullYear(), 0, 1).getTime() &&
      currentDate.getTime() <= startPadroProvisional.getTime()
    );
  }

  // acuerdo con BE, el último elemento de la lista de ejercicios -> llistaExercicis
  get currentExercise(): string {
    if (this.config?.llistaExercicis.length) {
      return this.config.llistaExercicis[
        this.config.llistaExercicis.length - 1
      ];
    }

    const year = this.currentDate.getFullYear();
    return (this.isPastExercise ? year - 2 : year - 1).toString();
  }

  get exerciseToBeDomiciliated(): string {
    if (this.outOfTimeToBeDomiciled) {
      return String(Number(this.currentExercise) + 1);
    }

    return this.currentExercise;
  }

  get exerciseToModifyAccount(): string {
    if (this.outOfTimeToModifyAccount) {
      return String(Number(this.currentExercise) + 1);
    }

    return this.currentExercise;
  }

  get importMinAjornamentFraccionament(): number {
    return this.config?.importMinAjornamentFraccionament ?? 0;
  }
}
