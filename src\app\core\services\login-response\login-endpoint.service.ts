import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { map, Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import {
  ConveniatVerificationResponse,
  LoginRequest,
  LoginResponse,
  SimpleLoginResponse,
} from '../../models';
import { GetScoringHttpResponse } from '@app/core/models/login-response.model';

@Injectable({
  providedIn: 'root',
})
export class LoginResponseEndPointService {
  constructor(private httpService: SeHttpService) {}

  getSmsLoginSimple(
    request: LoginRequest,
    recaptcha: string,
  ): Observable<LoginResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `login-simple`,
      method: 'post',
      headers: { recaptcha },
      body: request,
    };

    return this.httpService.post<LoginResponse>(httpRequest);
  }

  public login(request: LoginRequest): Observable<LoginResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: ``,
      method: 'post',
      body: request,
    };
    return this.httpService.post<LoginResponse>(httpRequest);
  }

  public confirmSMSCode(
    request: LoginRequest,
    recaptcha: string,
  ): Observable<SimpleLoginResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlSeguretat,
      url: `/validar-pin`,
      method: 'post',
      headers: { recaptcha },
      body: request,
    };
    return this.httpService.post<SimpleLoginResponse>(httpRequest);
  }

  public confirmRelationNifPlate(
    nif: string,
    plate: string,
  ): Observable<ConveniatVerificationResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `validar/nif/${nif}/matricula/${plate}`,
      method: 'get',
    };
    return this.httpService.get<ConveniatVerificationResponse>(httpRequest);
  }

  /**
   * Comprueba si el NIF del Sujeto Pasivo se corresponde con el
   * nombre proporcionado.
   *
   * @param taxpayerNIF El NIF del Sujeto Pasivo
   * @param taxpayerName El nombre del Sujeto Pasivo
   * @returns `true` si el NIF del Sujeto Pasivo se corresponde con `taxpayerName`. `false` en cualquier otro caso.
   */
  public getIsTaxpayerNIFRelatedToName(
    taxpayerNIF: string,
    taxpayerName: string,
  ): Observable<{ match: boolean; fullName: string }> {
    taxpayerNIF = this.sanitize(taxpayerNIF);
    taxpayerName = this.sanitize(taxpayerName);

    return this.httpService
      .get({
        baseUrl: environment.baseUrlSeguretat,
        url: `/usuaris/${taxpayerNIF}/nom/${taxpayerName}/scoring`,
        method: 'get',
      })
      .pipe(
        map(({ content }: GetScoringHttpResponse) => ({
          match: !!content?.match,
          fullName: content?.fullName ?? '',
        })),
      );
  }

  private sanitize(text: string): string {
    return encodeURIComponent(text);
  }
}
