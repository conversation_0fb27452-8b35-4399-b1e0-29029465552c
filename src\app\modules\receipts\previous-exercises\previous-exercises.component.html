<form [formGroup]="filterForm" class="mb-3">
  <div *ngIf="showTable" class="search-filter px-4 pt-3 flex-nowrap">
    <!-- buscador de matricula y dropdown -->
    <div class="row">
      <div class="col-md-2 col-12">
        <se-dropdown
          class=""
          formControlName="year"
          [id]="'share-dropdown'"
          [placeholder]="
            'SE_PADRO_CO2.PREVIOUS_EXERCISES.TOTS_ANTERIORS' | translate
          "
          [filter]="yearOptions.length > 5"
          [options]="yearOptions"
          [optionLabel]="'label'"
          [optionValue]="'id'"
          [showClear]="true"
          (dropdownOutput)="getPadroData()"
        ></se-dropdown>
      </div>

      <!-- buscador -->
      <div
        class="col-md-3 col-12 ps-md-0 d-flex flex-row"
        *ngIf="canSearchByPlate"
      >
        <!-- TODO subir ngif al div cuando se cambien el resto de filtros  -->
        <se-input
          class="w-100 w-md-auto"
          formControlName="plate"
          [placeholder]="
            'SE_PADRO_CO2.PREVIOUS_EXERCISES.WRITE_PLATE' | translate
          "
          [type]="'text'"
          [showClear]="true"
        ></se-input>
        <se-button
          class="ms-1"
          [type]="'button'"
          [btnTheme]="'secondary'"
          [icon]="'matSearchOutline'"
          [iconSize]="'1.5em'"
          [ariaLabel]="'SE_PADRO_CO2.BUTTONS.SEARCH' | translate"
          (click)="getPadroData()"
        >
        </se-button>
      </div>

      <!-- botones de fraccionar y pagar -->
      <div
        class="d-flex col ms-auto flex-column flex-md-row flex-nowrap justify-content-end"
        *ngIf="!isCoordinador"
      >
        <!-- TODO: Show in a future development -->
        <!-- <se-button
          [type]="'button'"
          [btnTheme]="'secondary'"
          [size]="'default'"
          [disabled]="!selectedRows.length"
        >
          {{
            'SE_PADRO_CO2.PREVIOUS_EXERCISES.DEFERMENT_INSTALLMENT' | translate
          }}
        </se-button> -->

        <!-- BOTON OTRAS ACCIONES -->
        <!-- TODO en un futuro se puede implementar -->
        <!-- <ng-container *ngIf="!specificConfigurationService.isProvisional">
          <se-button-dropdown
            [items]="dropdownButtonAnotherActions"
            [buttonOptions]="otherActionDropdownButtonOptions"
          >
            {{ 'SE_PADRO_CO2.BUTTONS.OTHER_ACTIONS' | translate }}
          </se-button-dropdown>
        </ng-container> -->

        <!-- BOTON PAGAR -->
        <se-button
          class="ms-0 ms-md-2 mt-2 mt-md-0 mb-md-0 mb-2"
          [type]="'button'"
          [btnTheme]="'primary'"
          [size]="'default'"
          [disabled]="!selectedRows.length"
          (onClick)="onPagamentSelectedPlates()"
        >
          {{ 'SE_PADRO_CO2.LABELS.PAY' | translate }}
        </se-button>
      </div>
    </div>
  </div>

  <!-- 2ª linea de filtros - DESKTOP -->
  <app-previous-exercises-filters
    class="d-md-block d-none"
    [filterForm]="filterForm"
    (handleApplyFilter)="getPadroData()"
    *ngIf="showFilters"
  ></app-previous-exercises-filters>
</form>

<!-- BOTON FILTROS MÓVIL -->
<aside *ngIf="showFilters" class="mt-1 mb-4 d-block d-md-none">
  <se-button
    [type]="'button'"
    [btnTheme]="'secondary'"
    [size]="'default'"
    (onClick)="openModalFilter()"
  >
    {{ 'SE_PADRO_CO2.BUTTONS.FILTERS' | translate }}
  </se-button>
</aside>

<se-table
  *ngIf="showTable; else noData"
  [selectable]="true"
  [columns]="tableColumns"
  [data]="tableRows"
  [resizable]="true"
  [currentPage]="currentPage"
  [cellTemplatePriorityOrder]="'cell-row-column'"
  [lazyTotalRecords]="totalRecords"
  [lazy]="true"
  [showSelectAll]="totalRecords > MIN_ELEMENTS_TO_SHOW_FILTERS"
  [showPagination]="totalRecords > MIN_ELEMENTS_TO_SHOW_FILTERS"
  [showRowsPerPage]="true"
  [showEmptyState]="true"
  [clickableRows]="true"
  [rowsPerPageOptions]="rowsPerPageOptions"
  [itemsPerPage]="itemsPerPage"
  [paginationDownloadButton]="downloadButton"
  (paginationDownloadClick)="openDownloadExcelModal()"
  (onSelectionChange)="onSelectionChange($event)"
  (onPageChange)="handlePageChange($event)"
  (sortByColumn)="sortByColumn($event)"
  (onRowClick)="onRowClick($event)"
></se-table>

<ng-template #noData>
  <se-empty-state [icon]="'info'" [backgroundTheme]="'primary'" />
</ng-template>
