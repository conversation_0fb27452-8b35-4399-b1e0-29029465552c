<div class="row mb-2">
  <!-- TITLE WITH TOOLTIP -->
  <div class="col-12 col-md-6 title">
    <span [innerHTML]="title ?? '' | translate"></span>
    <ng-icon
      *ngIf="tooltipText"
      [pTooltipAccessible]="tooltipText | translate"
      tooltipStyleClass="tooltip-position-personalized"
      [name]="'matInfoOutline'"
      class="tooltip-icon"
    ></ng-icon>
  </div>

  <!-- DETAIL -->
  <div class="col-12 col-md-6">
    <div class="row">
      <ng-container *ngIf="description">
        <div
          class="justify-content-start"
          [ngClass]="{ 'col-4': linkText, 'col-auto': !linkText }"
        >
          <span>{{ description | translate }}</span>
        </div>
      </ng-container>
      <div class="col-6 justify-content-start" *ngIf="tagLabel">
        <se-tag [tagTheme]="tagTheme || 'info'" [closable]="false">
          {{ tagLabel | translate }}
        </se-tag>
      </div>
      <div class="col-6 justify-content-start" *ngIf="linkText">
        <se-link
          [disabled]="false"
          [size]="'regular'"
          [linkTheme]="'secondary'"
          (onClick)="onLinkClick()"
        >
          {{ linkText | translate }}
        </se-link>
      </div>
    </div>
  </div>
</div>
