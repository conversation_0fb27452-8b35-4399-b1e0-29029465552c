import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SeModal } from 'se-ui-components-mf-lib';
import { TitularPanelData, UserInfo } from './models/titular-data-modal.model';
import { TitularDataModalService } from './services/titular-data-modal.service';

@Component({
  selector: 'app-titular-data-modal',
  templateUrl: './titular-data-modal.component.html',
})
export class TitularDataModalComponent implements OnInit {
  @Input() data: SeModal | undefined;
  @Input() userInfo: UserInfo | undefined;

  panelData: TitularPanelData[] | undefined;

  constructor(
    private activatedModalService: NgbActiveModal,
    private titularDataService: TitularDataModalService,
  ) {}

  ngOnInit(): void {
    if (!this.userInfo) return;

    this.panelData = this.titularDataService.setPanelData(this.userInfo);
  }

  closeModal(): void {
    this.activatedModalService.close();
  }
}
