import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DatePipe } from '@angular/common';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { RouterTestingModule } from '@angular/router/testing';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonModule,
  SeCheckboxModule,
  SeConfirmationMessageModule,
  SeHighlightRadioContainerModule,
  SeInputModule,
  SePanelModule,
  SeRadioModule,
  SeUserInfoBarModule,
} from 'se-ui-components-mf-lib';
import { ConveniatIdentificationComponent } from './conveniat-identification/conveniat-identification.component';
import { CivilServantIdentificationComponent } from './civil-servant-identification/civil-servant-identification.component';
import { CoordinatorIdentificationComponent } from './coordinator-identification/coordinator-identification.component';
import { IdentificationsComponent } from './identifications.component';
import { PersonalIdentificationComponent } from './personal-identification/personal-identification.component';

describe('IdentificationsComponent', () => {
  let component: IdentificationsComponent;
  let fixture: ComponentFixture<IdentificationsComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [
        IdentificationsComponent,
        CoordinatorIdentificationComponent,
        CivilServantIdentificationComponent,
        ConveniatIdentificationComponent,
        PersonalIdentificationComponent,
      ],
      imports: [
        BrowserAnimationsModule,
        RouterTestingModule,
        HttpClientTestingModule,
        TranslateModule.forRoot(),
        FormsModule,
        ReactiveFormsModule,
        SePanelModule,
        SeUserInfoBarModule,
        SeInputModule,
        SeCheckboxModule,
        SeHighlightRadioContainerModule,
        SeAlertModule,
        SeButtonModule,
        SeRadioModule,
        SeConfirmationMessageModule,
      ],
      providers: [DatePipe],
    }).compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(IdentificationsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
