import { IdentificationType } from '@core/models';
import { Nullable } from 'se-ui-components-mf-lib';

export interface AssociatedDocumentsRequest {
  tipusAccess: IdentificationType;
  matricula: Nullable<string>;
  idPersTitular: string;
}

export interface AssociatedDocuments {
  format: string; // TODO add more types;
  nom: string;
  tamany: number;
  tipusDocument: string;
  exercici: string;
  ids: string[];
}

export interface AssociatedDocumentsResponse {
  content: AssociatedDocuments[];
  messages: string[];
}
