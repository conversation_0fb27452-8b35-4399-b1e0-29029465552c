import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PaymentsConfirmationComponent } from './payments-confirmation.component';
import { TranslateModule } from '@ngx-translate/core';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';

describe('PaymentsConfirmationComponent', () => {
  let component: PaymentsConfirmationComponent;
  let fixture: ComponentFixture<PaymentsConfirmationComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [PaymentsConfirmationComponent],
      imports: [
        TranslateModule.forRoot(),
        HttpClientTestingModule,
        RouterTestingModule,
      ],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    });
    fixture = TestBed.createComponent(PaymentsConfirmationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
