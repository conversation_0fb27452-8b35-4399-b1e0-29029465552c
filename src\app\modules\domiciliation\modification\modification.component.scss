@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

:host ::ng-deep se-radio + se-radio > .radio-container {
  @include media-breakpoint-down(md) {
    margin-left: 0 !important;
  }
}

:host {
  &::ng-deep se-radio + se-radio > .radio-container {
    @include media-breakpoint-down(md) {
      margin-left: 0 !important;
    }
  }

  .radio-form {
    padding: 24px;
    gap: 24px;
  }

  .iban-container {
    &::ng-deep {
      div.iban-container {
        width: 100% !important;
      }

      .dades-bancaries__dr .declaracio-responsable__title {
        font-size: var(--text-sm);
        line-height: var(--line-sm);
      }
    }
  }
}

.vehicle-selection {
  &::ng-deep {
    se-panel .p-panel .p-panel-content {
      padding: 0;
    }
  }

  &__request-direct-debit-panel,
  &__vehicles-panel {
    @include media-breakpoint-down(md) {
      margin-bottom: 1rem;
    }
  }
}
