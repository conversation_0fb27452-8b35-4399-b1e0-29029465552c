import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SeDropdownOption, WcComponentInput } from 'se-ui-components-mf-lib';
import { CheckBoxOption, FunctionalModuleEnumT } from 'src/app/core/models/docs.model';

@Component({
  selector: 'app-add-doc-modal',
  template: `
		<mf-documents
			*axLazyElement
			[input]="modalDocumentsWcInput"
			(output)="onDocumentsEvent($event)">
		</mf-documents>
	`
})
export class AddDocModalComponent {

  // Modal imputs
  @Input() documentsSelectList: SeDropdownOption[] = [];
  @Input() idEntity!: string;
  @Input() subtype!: string;
  @Input() selection!: CheckBoxOption[];
  @Input() functionalModule!: FunctionalModuleEnumT;
  @Input() acceptedFileSize!: number;

  // Modal output
  @Output() modalOutput: EventEmitter<any> = new EventEmitter<any>();

  // Webcomponent > Documents
  modalDocumentsWcInput!: WcComponentInput;

  constructor(
    private activeModal: NgbActiveModal,
  ) { }

  ngOnInit(): void {
    this.setDocumentsData();
  }

  private setDocumentsData() {
    this.modalDocumentsWcInput = {
      component: 'upload-document',
      data: {
        idFunctionalModule: this.functionalModule,
        functionalModule: this.functionalModule,
        selection: this.selection,
        idEntity: this.idEntity,
        codeDescriptionComplementary: this.subtype,
        acceptedFileExtensions: '.pdf, .doc, .docx, .jpg',
        acceptedFileSize: this.acceptedFileSize || 25690115, // 25MB
        tableConfiguration: {
          _columns: [
            {
              id: 'description',
              label: 'COMPONENT_UPLOAD.TABLE_COLUMNS.description',
              template: 'inputText',
              options: {
                readOnly: true,
                formFieldOptions: {
                  maxlength: 30
                }
              }
            },
            {
              id: 'documentType',
              label: 'COMPONENT_UPLOAD.TABLE_COLUMNS.documentType',
              template: 'inputSelect',
              options: {
                inputSelectList: this.documentsSelectList,
                readOnly: true
              }
            }
          ],
          _formArrayName: "certificats"
        }
      }
    };
  }

  onDocumentsEvent(event: Event) {
    this.activeModal.close();
    this.modalOutput.emit(event)
  }
}
