<div class="payment-confirmation-container">
  <mf-pagaments-confirmacio-pagament
    *axLazyElement
    (paymentCheckResult)="setTitle($event)"
    [emailLabel]="'SE_PADRO_CO2.PAYMENTS_PROCESS.EMAIL_LABEL' | translate"
    [alertPaymentOkList]="[
      'SE_PADRO_CO2.PAYMENTS_PROCESS.ALERT.DESCRIPTION' | translate,
    ]"
    [paymentData]="paymentData"
    [showBackButton]="false"
  >
    <p>
      {{ 'SE_PADRO_CO2.PAYMENTS_PROCESS.ASTERISK_SECTION.MSG_1' | translate }}
      <se-link
        target="_self"
        linkTheme="secondary"
        size="semibold"
        [dashed]="true"
        [disabled]="false"
        [href]="asteriskLink1"
        [target]="'_blank'"
      >
        {{ asteriskLink1Label }}
      </se-link>
    </p>
    <p>
      {{ 'SE_PADRO_CO2.PAYMENTS_PROCESS.ASTERISK_SECTION.MSG_2' | translate }}
      <se-link
        target="_self"
        linkTheme="secondary"
        size="semibold"
        [dashed]="true"
        [disabled]="false"
        [href]="asteriskLink2"
        [target]="'_blank'"
      >
        {{ asteriskLink2Label }}
      </se-link>
    </p>
  </mf-pagaments-confirmacio-pagament>

  <div class="mt-4" *ngIf="showPanel">
    <se-panel
      [title]="
        'SE_PADRO_CO2.PRESENTATION_RECEIPT.PENDING_PAYMENT_PANEL.TITLE_IN_PERIOD'
      "
    >
      <div class="d-flex align-items-center mb-4">
        <p class="mb-0 me-3">
          <strong>
            {{
              'SE_PADRO_CO2.PRESENTATION_RECEIPT.PENDING_PAYMENT_PANEL.REMINDER'
                | translate
            }}
          </strong>
        </p>
        <se-tag [tagTheme]="'warning'" [closable]="false">
          {{
            'SE_PADRO_CO2.PRESENTATION_RECEIPT.PENDING_PAYMENT_PANEL.TAG'
              | translate
          }}
        </se-tag>
      </div>
      <p
        [innerHTML]="
          'SE_PADRO_CO2.PRESENTATION_RECEIPT.PENDING_PAYMENT_PANEL.DETAIL_TELEMATIC_PAYMENT'
            | translate
        "
      ></p>
    </se-panel>
  </div>

  <!-- card de domiciliaciones -->
  <div
    class="my-5"
    *ngIf="showModifyDomiciliation || showRegisterDomiciliation"
    appHideOnCoordinator
  >
    <app-info-messages
      [title]="'SE_PADRO_CO2.RECEIPTS.DOMICILIATE_CO2_RECEIPTS' | translate"
      [text]="domiciliatedInfoMessage"
      [image]="'note'"
    >
      <div class="d-flex flex-column row-gap-2 flex-sm-row mt-1">
        <se-button
          *ngIf="showModifyDomiciliation"
          [btnTheme]="'secondary'"
          (onClick)="navigateToUpdateDomiciled()"
          >{{
            'SE_PADRO_CO2.BUTTONS.MODIFY_DOMICILIATE' | translate
          }}</se-button
        >
        <se-button
          class="mt-md-0 mt-2 ms-md-2 ms-0"
          *ngIf="showRegisterDomiciliation"
          (onClick)="navigateToCreateDomiciliation()"
          [btnTheme]="'primary'"
          >{{
            'SE_PADRO_CO2.BUTTONS.DOMICILIAR_PAYMENTS' | translate
          }}</se-button
        >
      </div>
    </app-info-messages>
  </div>

  <div class="payments-confirmation__survey-container"></div>

  <!-- buttom back fuera porque va el card de domiciiaciones arriba -->
  <div class="d-flex flex-column flex-sm-row justify-content-sm-end mt-4">
    <se-button (onClick)="navigateToList()">
      {{ 'SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate }}
    </se-button>
  </div>
</div>
