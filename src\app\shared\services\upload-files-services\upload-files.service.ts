import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import {
  Observable,
  catchError,
  concatAll,
  forkJoin,
  from,
  map,
  mergeMap,
  of,
  switchMap,
  take,
} from 'rxjs';
import { AttachFile, iDocumentPadoct } from 'se-ui-components-mf-lib';
import {
  RequestUploadFile,
  ResponseUploadFile,
  ResponseUploadFiles,
  TemplateFile,
} from './../../models';
import { UploadFilesEndpointsService } from './upload-files-endpoints.service';
import type { SeDocument, FunctionalModuleEnumT } from 'src/app/core/models';

@Injectable({
  providedIn: 'root',
})
export class UploadFilesService {
  constructor(
    private uploadFilesEndpointsService: UploadFilesEndpointsService,
    private translateService: TranslateService,
  ) {}

  uploadSyncFiles(
    files: AttachFile[],
    idEntity: string,
    key: string,
    idFunctionalModule: FunctionalModuleEnumT,
    docType: string,
    sigedaDescriptions: SeDocument[],
  ): Observable<AttachFile | undefined> {
    //TRANSFORM FILES TO UPLOAD
    const requests$ = forkJoin(
      files.map((file) => {
        return this.transformToRequest(
          file,
          this.getSubtype(sigedaDescriptions, this.getDocType(file, docType)),
          idEntity,
          idFunctionalModule,
          key,
          this.getGTATSigedaType(
            sigedaDescriptions,
            this.getDocType(file, docType),
          ),
          docType,
        );
      }),
    );

    //UPLOAD EVERY FILE SYNCHRONOUSLY
    return requests$.pipe(
      switchMap((requests: RequestUploadFile[]) =>
        this.mapRequestsToObservable(requests, files),
      ),
      concatAll(),
      catchError(() => {
        return of();
      }),
    );
  }

  private mapRequestsToObservable(
    requests: RequestUploadFile[],
    files: AttachFile[],
  ): Observable<AttachFile | undefined>[] {
    return requests.map((request) =>
      this.uploadFilesEndpointsService.uploadFile(request).pipe(
        take(1),
        map((response: ResponseUploadFile) =>
          this.mapUploadSyncFileResponse(response?.content, files),
        ),
        catchError(() => {
          return of();
        }),
      ),
    );
  }

  private mapUploadSyncFileResponse(
    responseContent: iDocumentPadoct,
    files: AttachFile[],
  ): AttachFile | undefined {
    if (responseContent) {
      const returnedFile = files.find(
        (res) => res.name === responseContent.nom,
      );
      if (returnedFile) {
        returnedFile['id'] = responseContent.id;
      }

      return returnedFile;
    } else {
      return;
    }
  }

  uploadFiles(
    files: AttachFile[],
    idEntity: string,
    key: string,
    idFunctionalModule: FunctionalModuleEnumT,
    docType: string,
    sigedaDescriptions: SeDocument[],
  ): Observable<AttachFile[] | undefined> {
    //TRANSFORM FILES TO UPLOAD
    const requests$ = forkJoin(
      files.map((file) =>
        from(
          this.transformToRequest(
            file,
            this.getSubtype(sigedaDescriptions, this.getDocType(file, docType)),
            idEntity,
            idFunctionalModule,
            key,
            this.getGTATSigedaType(
              sigedaDescriptions,
              this.getDocType(file, docType),
            ),
            docType,
          ),
        ),
      ),
    );

    //UPLOAD EVERY FILE
    return requests$.pipe(
      take(1),
      mergeMap((request: RequestUploadFile[]) =>
        this.uploadFilesEndpointsService.uploadFiles(request).pipe(
          take(1),
          map((response: ResponseUploadFiles) => {
            if (response?.content) {
              files.forEach((file) => {
                file['id'] = response?.content?.find(
                  (res) => res.nom === file.name,
                )?.id;
              });
              return files;
            } else {
              return;
            }
          }),
          catchError(() => {
            return of();
          }),
        ),
      ),
      catchError(() => {
        return of();
      }),
    );
  }

  filterDocumentsByEntityAndKey(
    documents: iDocumentPadoct[],
    idEntity: string,
    key: string,
  ): iDocumentPadoct[] {
    return documents.filter(
      (element: iDocumentPadoct) =>
        element.idEntity?.toString() === idEntity?.toString() &&
        element.key?.toString() === key?.toString(),
    );
  }

  formatDocumentResponseList = (
    documents: iDocumentPadoct[],
    idEntity: string,
    key: string,
    sigedaDescriptions: SeDocument[],
  ): AttachFile[] => {
    const files: AttachFile[] = [];

    this.filterDocumentsByEntityAndKey(documents, idEntity, key).forEach(
      (element: iDocumentPadoct) => {
        const sigedaDescription = this.getSigedaDescription(
          sigedaDescriptions,
          element.codSigedaType,
          element.codeDescriptionComplementary,
        );

        files.push({
          name: element.nom,
          size: element.size,
          type: element.documentType,
          description: element.description,
          id: element.id,
          idType: sigedaDescription?.subtype || '',
          docType:
            sigedaDescription?.description ||
            this.translateService.instant(
              'SE_DOCUMENTS_MF.SIGEDA_TYPES.' + element.codSigedaType,
            ),
          lastModified: 0,
          webkitRelativePath: '',
          arrayBuffer: function (): Promise<ArrayBuffer> {
            throw new Error('Function not implemented.');
          },
          slice: function (): Blob {
            throw new Error('Function not implemented.');
          },
          stream: function (): ReadableStream<Uint8Array> {
            throw new Error('Function not implemented.');
          },
          text: function (): Promise<string> {
            throw new Error('Function not implemented.');
          },
          bytes: function (): Promise<Uint8Array> {
            throw new Error('Function not implemented.');
          },
        });
      },
    );

    return files;
  };

  private getSubtype(
    sigedaDescriptions: SeDocument[],
    docType: string,
  ): string {
    return sigedaDescriptions.find(
      (element) => element?.description === docType,
    )?.subtype as string;
  }

  private getGTATSigedaType(
    sigedaDescriptions: SeDocument[],
    docType: string,
  ): string {
    return sigedaDescriptions.find(
      (element) => element?.description === docType,
    )?.type as string;
  }

  private getSigedaDescription(
    sigedaDescriptions: SeDocument[],
    codSigedaType: string,
    codeDescriptionComplementary: string,
  ): SeDocument | undefined {
    return sigedaDescriptions?.find(
      (sigedaDoc) =>
        sigedaDoc.subtype === codeDescriptionComplementary &&
        sigedaDoc.type === codSigedaType,
    );
  }

  private transformToRequest(
    file: AttachFile,
    codeDescriptionComplementary: string,
    idEntity: string,
    idFunctionalModule: FunctionalModuleEnumT,
    key: string,
    codGTATSigedaType: string,
    docType?: string,
  ): Observable<RequestUploadFile> {
    return this.transformToBase64(file).pipe(
      take(1),
      map((fileBase64) => {
        let format = '';
        if (file?.name?.match(/\.[0-9a-z]+$/i)) {
          format = file.name.split('.').pop() as string;
        }
        return {
          key,
          format,
          idEntity,
          fileBase64,
          codGTATSigedaType,
          codeDescriptionComplementary,
          nom: file.name,
          csvRequired: true,
          origenCiutada: true,
          documentType: file['docType'] ?? docType,
          description: file['description'],
          idFunctionalModule: idFunctionalModule,
        } as RequestUploadFile;
      }),
    );
  }

  mapIdDocumentPadocDocToRequest(doc: iDocumentPadoct): RequestUploadFile {
    return {
      key: doc.key,
      format: doc.format,
      idEntity: doc.idEntity,
      fileBase64: doc.base64File,
      codGTATSigedaType: doc.codSigedaType,
      codeDescriptionComplementary: doc.codeDescriptionComplementary,
      nom: doc.nom,
      csvRequired: true,
      origenCiutada: true,
      description: doc.description,
      idFunctionalModule: doc.idFunctionalModule as FunctionalModuleEnumT,
    };
  }

  private getDocType(file: AttachFile, docType: string): string {
    return file['docType'] ?? docType;
  }

  private transformToBase64(file: AttachFile): Observable<string> {
    return new Observable((observer) => {
      const reader = new FileReader();
      reader.onload = (): void => {
        const base64Data = reader?.result?.toString()?.split(',')[1];
        observer.next(base64Data as string);
        observer.complete();
      };
      reader.onerror = (error): void => {
        observer.error(error);
      };
      reader.readAsDataURL(file);
    });
  }

  createEmptyFile(value: TemplateFile): AttachFile {
    const file = new File([''], '', {
      type: 'plain/text',
      lastModified: Date.now(),
    });

    return Object.assign(file, {
      ...value,
      emptySize: true,
      rowConfig: {
        hasDelete: false,
        hasDownload: false,
      },
    });
  }
}
