export enum PaymentType {
  BIZUM = 'B',
  CUENTA = 'C',
  TARJETA = 'T',
}

export interface PaymentData {
  idBBDD?: string;
  refPago?: string;
  idPagament: string;
}

export interface PaymentResult {
  result: PaymentResultStates;
  error?: unknown;
}

export enum PaymentResultStates {
  KO = 'KO',
  OK = 'OK',
}

export interface FormIbanValue {
  iban: string;
  entity: string;
  representant: string;
  valid: boolean;
}
