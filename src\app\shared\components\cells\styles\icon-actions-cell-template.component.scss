// RESPONSIVE
@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

// @media (max-width: 768px) {
@include media-breakpoint-down(sm) {
  .action-button {
    border: 1px solid var(--color-blue-500);
    border-radius: 4px;
    width: 30px;
    height: 30px;
    display: inline-flex;
    justify-content: center;
    align-items: center;
  }
}

.action-button {
  all: initial;
  font-size: var(--text-lg);
  cursor: pointer;
  color: var(--color-blue-500);

  &:hover {
    color: var(--color-blue-700);
  }
}

:host {
  se-button-dropdown {
    ::ng-deep {
      se-button {
        .se-button {
          padding: 0 !important;

          &:hover {
            background: transparent;
            border: 1px solid transparent;
            color: var(--color-primary-action);
          }
        }
      }
    }
  }
}
