import { Injectable } from '@angular/core';
import { DetallPadroRequest, DetallPadroResponse } from '@shared/models';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';

@Injectable({
  providedIn: 'root',
})
export class DetailPadroEndpointService {
  constructor(private httpService: SeHttpService) {}

  public getPadroDetail(
    request: DetallPadroRequest,
  ): Observable<DetallPadroResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `detall-padro`,
      method: 'post',
      body: request,
    };
    return this.httpService.post<DetallPadroResponse>(httpRequest);
  }
}
