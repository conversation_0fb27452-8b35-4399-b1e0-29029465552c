<se-header-info
  [title]="'SE_DECINF_MF.APP_TITLE'"
  [showHelp]="true"
  [infoItems]="header.infoItems$ | async"
  [tags]="header.tags$ | async"
  (helpButtonClick)="onHelpButtonClick()"
/>

<div class="main-container mt-4 mb-4">
  <div class="container">
    <!--/* STEPPER */-->
    <section
      class="mb-4"
      *ngIf="pageLayoutService.isElementVisible && (steps$ | async) as steps"
    >
      <se-stepper [steps]="steps" [canNavigate]="true"></se-stepper>
    </section>

    <!--/* MESSAGES */-->
    <se-exception-viewer></se-exception-viewer>

    <div class="page-container">
      <router-outlet></router-outlet>
    </div>
  </div>
</div>
