import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonModule,
  SeCheckboxModule,
  SeLinkModule,
  SeModalModule,
  SePanelModule,
  SeTableModule,
} from 'se-ui-components-mf-lib';

import { RequestSummaryComponent } from './request-summary.component';
import { ModalVehiclesModule } from '../modal-vehicles';
import { IbanFormatterPipe } from '@app/shared/pipes/iban-formatter';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@NgModule({
  declarations: [RequestSummaryComponent],
  exports: [RequestSummaryComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SeModalModule,
    SeTableModule,
    SePanelModule,
    SeButtonModule,
    SeAlertModule,
    SeLinkModule,
    SeCheckboxModule,
    ModalVehiclesModule,
    IbanFormatterPipe,
    FormsModule,
    ReactiveFormsModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class RequestSummaryModule {}
