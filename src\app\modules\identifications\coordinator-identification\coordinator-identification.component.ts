import { Component, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { IdentificationsService, StorageService } from '@core/services';
import { Subject, takeUntil } from 'rxjs';
import { SeValidations } from 'se-ui-components-mf-lib';
import { AppRoutes, IdentificationType, LoginRequest } from '@core/models';

@Component({
  selector: 'app-coordinator-identification',
  templateUrl: './coordinator-identification.component.html',
})
export class CoordinatorIdentificationComponent implements OnDestroy {
  coordinatorForm: FormGroup = new FormGroup({
    nifTitular: new FormControl(null, [
      Validators.required,
      SeValidations.dniNieCif(),
    ]),
  });

  private unsubscribe = new Subject<void>();

  constructor(
    private identificationsSrv: IdentificationsService,
    private storageService: StorageService,
  ) {}

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  continue(): void {
    const nifTitular = this.coordinatorForm
      .get('nifTitular')
      ?.value?.toUpperCase();
    const body: LoginRequest = {
      ...this.coordinatorForm.value,
      nifTitular: nifTitular,
    };
    this.identificationsSrv
      .loginCo2(body, IdentificationType.COORDINADOR, AppRoutes.RECEIPTS)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((response) => {
        if (response.content) {
          this.storageService.coordinadorNifContribuent = nifTitular;
        }
      });
  }
}
