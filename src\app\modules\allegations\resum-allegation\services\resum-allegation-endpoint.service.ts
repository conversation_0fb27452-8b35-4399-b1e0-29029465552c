import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';
import {
  AllegationPresentationRequest,
  AllegationPresentationResponse,
  AllegationResumResponse,
} from '../models';

@Injectable({
  providedIn: 'root',
})
export class ResumAllegacionsEndpointService {
  constructor(private httpService: SeHttpService) {}

  getAllegationVehicles(
    idAllegacio: string,
  ): Observable<AllegationResumResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `allegacions/${idAllegacio}/resum`,
      method: 'get',
    };

    return this.httpService.get<AllegationResumResponse>(httpRequest);
  }

  setAllegationPresentation(
    request: AllegationPresentationRequest,
  ): Observable<AllegationPresentationResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `allegacions/presentar-allegacio`,
      method: 'post',
      body: request,
    };

    return this.httpService.post<AllegationResumResponse>(httpRequest);
  }
}
