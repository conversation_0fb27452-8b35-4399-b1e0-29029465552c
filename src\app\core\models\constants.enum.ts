export enum Constant {
  PROCESS_CODE = '7',
  NAME = 'DECINF',
  TESTING_NAME = 'ISBEE', // TODO Remove after testing
  ANUAL_PERIOD = '0A',
  TAX_DOCUMENT_TYPE = 'TD99-203',
}

export enum Models {
  MODEL_523 = '523',
  MODEL_524 = '524',
  MODEL_525 = '525',
  MODEL_543 = '543',
  MODEL_643 = '643',
  MODEL_644 = '644',
  MODEL_645 = '645',
  MODEL_648 = '648',
  MODEL_673 = '673',
  MODEL_943 = '943',
  MODEL_993 = '993',
}
