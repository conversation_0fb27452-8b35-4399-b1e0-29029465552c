import { Component, OnInit } from '@angular/core';
import { AppRoutes, ResourceProcessDocument } from '@app/core/models';
import { CustomRouterService, StorageService } from '@app/core/services';
import { ProcessNameToDisplayInPresentationReceipt } from '@app/modules/presentation-receipt';
import {
  DeferralPaymentsEndpointsService,
  ResumDeferralPaymentResponse,
} from '@app/shared/services';
import { TranslateService } from '@ngx-translate/core';
import { take } from 'rxjs';
import { iDocumentPadoct, SeMessageService } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-deferral-payments-confirmation',
  template: ``,
  styleUrls: [],
})
export class DeferralPaymentsConfirmationComponent implements OnInit {
  constructor(
    private readonly storage: StorageService,
    private readonly customRouter: CustomRouterService,
    private readonly endpointsService: DeferralPaymentsEndpointsService,
    private readonly msgService: SeMessageService,
    private readonly translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    const { identificadorMui } = this.storage.getDeferralPaymentVehicles();

    if (!identificadorMui) {
      this.goBack();
      return;
    }

    this.endpointsService
      .getResumDeferralPayments(identificadorMui)
      .pipe(take(1))
      .subscribe((data) => {
        if (data) {
          if (data.errors?.length > 0) {
            this.showMessageError(data);
            return;
          }

          this.setDataToShowThemInPresentationReceipt(data);
          this.customRouter.navigateByBaseUrl(AppRoutes.PRESENTATION_RECEIPT);
        }
      });
  }

  goBack(): void {
    this.storage.clearVehiclesToBeRegistered();
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  private setDataToShowThemInPresentationReceipt(
    response: ResumDeferralPaymentResponse,
  ): void {
    this.storage.setResourceProcessDocumentData({
      receipt: {
        idPadoct: response.idJustificant,
        nom: response.fileNameJustificant,
      } as iDocumentPadoct,
      idFunctionalModule:
        ProcessNameToDisplayInPresentationReceipt.DeferralPayments,
    } as ResourceProcessDocument);
  }

  private showMessageError(data: ResumDeferralPaymentResponse): void {
    const code = data.errors?.[0]?.technicalCode || null;
    const error = data.errors?.[0]?.code || null;
    let title: string;
    let subtitle: string;

    const titleTranslated: boolean = this.translateService
      .instant(`UI_COMPONENTS.EXCEPTIONS.CODES.${error}`)
      .includes(error);

    if (titleTranslated) {
      title = `UI_COMPONENTS.EXCEPTIONS.CODES.${error}`;
    } else {
      title = 'Error';
    }

    const subtitleTranslated: boolean = this.translateService
      .instant(`UI_COMPONENTS.EXCEPTIONS.MSG.${code}`)
      .includes(code);

    if (subtitleTranslated) {
      subtitle = `UI_COMPONENTS.EXCEPTIONS.MSG.${code}`;
    } else {
      subtitle = this.translateService.instant(
        `UI_COMPONENTS.EXCEPTIONS.MSG.DEFAULT_MSG`,
        { code },
      );
    }

    this.msgService.addMessages([{ severity: 'error', title, subtitle }]);
  }
}
