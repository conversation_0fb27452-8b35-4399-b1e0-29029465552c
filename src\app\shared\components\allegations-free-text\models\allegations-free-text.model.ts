import { iDocumentPadoct } from 'se-ui-components-mf-lib';
import { ContestableActDocument } from '../../allegations-options';

export const FREE_TEXT_BLOCK_CONTROL_NAME = 'free_text';

export interface AllegationsFormData {
  isAllegationsFormValid: boolean;
  allegationsFormValue: AllegationsFormValue;
  allegationsDocuments: iDocumentPadoct[];
  documentsSigedaDescriptions: ContestableActDocument[];
}

export interface AllegationsFormValue {
  [key: string]: string;
}

export enum DocSigedaCodes {
  BANK_DOC = 'TD11-021',
  ALLEGATION_DOC = 'TD17-015',
  RECLAMACIO_JUSTIFICANT = 'TD11-017',
}

export enum DocSubtypeCodes {
  RECLAMACIO_JUSTIFICANT = '19',
}
