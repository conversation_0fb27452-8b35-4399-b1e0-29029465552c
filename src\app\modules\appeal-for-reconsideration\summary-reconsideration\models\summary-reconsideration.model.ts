import { SeHttpResponse } from 'se-ui-components-mf-lib';
import { Vehicle, Motius, DocumentsUpload } from '@shared/models';

export interface RecursResumResponse extends SeHttpResponse {
  content: RecursResum;
}

export interface RecursResum {
  vehicles?: Vehicle[];
  titular?: string;
  quota?: number;
  motius?: Motius[];
  ibanNumeroCompte?: string;
  documentNumeroCompte?: DocumentsUpload;
  declaracioNumeroCompte?: boolean;
}

export interface RecursPresentationResponse extends SeHttpResponse {
  content: ProcedureSavedRecurs;
}

export interface ProcedureSavedRecurs {
  idJustificant: string;
  fileName: string;
  idTramit: string;
}
