@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';

:host {
  ::ng-deep {
    th:last-of-type {
      justify-items: end !important;
    }

    // se-dropdown-filter
    .dropdown-header {
      height: 40px;

      .dropdown-title {
        padding: 4px !important;
      }
    }

    .se-dropdown-filter {
      max-width: none !important;
    }

    se-range-filter {
      .se-dropdown-filter {
        width: 100% !important;
      }
    }
  }

  .search-filter {
    background-color: var(--color-gray-200);
    border: 1px solid var(--color-gray-300);
  }
}

.dropdown-year {
  width: auto;
  min-width: 8rem;
}
