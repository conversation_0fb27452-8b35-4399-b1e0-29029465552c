import { Component, OnInit } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  AppRoutes,
  IdentificationType,
  SimpleLoginResponse,
  LoginResponse,
} from '@core/models';
import {
  CustomRouterService,
  HeaderInfoService,
  LoginResponseService,
  StorageService,
} from '@core/services';
import { Subject, takeUntil } from 'rxjs';
import { SeMessageService, SeValidations } from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';
import { ComponentEventOutput } from '@app/shared/components';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-simple-identification',
  templateUrl: './simple-identification.component.html',
  styleUrls: ['./simple-identification.component.scss'],
})
export class SimpleIdentificationComponent implements OnInit {
  simpleLoginForm: FormGroup = this.fb.group({
    matricula: new FormControl(null, [
      Validators.required,
      Validators.maxLength(9),
      Validators.pattern(/^\w+$/),
    ]),
    nifTitular: [
      '',
      SeValidations.listValidations([
        { validator: Validators.required },
        {
          validator: SeValidations.dniNie(),
          translation: 'SE_PADRO_CO2.LOGIN_SIMPLE.NIE_NIF_VALIDATION',
        },
      ]),
    ],
    mobil: new FormControl(null, [Validators.required, SeValidations.phone]),
  });

  verificationCode: FormGroup = new FormGroup({
    code: new FormControl(null, [Validators.required]),
  });

  showSimpleForm: boolean = true;
  isSuccessAlertShown: boolean = false;

  protected captchaKey = environment.captchaPublicKey;
  protected recaptchaValue: string = '';
  protected resetCaptcha = false;
  protected currentLang: string = 'ca';

  private unsubscribe = new Subject<void>();

  constructor(
    private loginService: LoginResponseService,
    private msgService: SeMessageService,
    private storeSrv: StorageService,
    private customRouter: CustomRouterService,
    private translate: TranslateService,
    private header: HeaderInfoService,
    private fb: FormBuilder,
  ) {}

  ngOnInit(): void {
    this.header.reset();
    this.storeSrv.clearAllData();
    this.loginService.resetUser();
    this.currentLang = this.translate.currentLang;
  }

  requestSMS(): void {
    this.loginService
      .getSmsLoginSimple(
        {
          ...this.simpleLoginForm.value,
          nifTitular: String(
            this.simpleLoginForm.get('nifTitular')?.value,
          )?.toUpperCase(),
          matricula: String(
            this.simpleLoginForm.get('matricula')?.value,
          )?.toUpperCase(),
        },
        this.recaptchaValue,
      )
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((response: LoginResponse) => {
        if (response.content) {
          const { validAccess, hoursBloqueig, minsBloqueig, tipusBloqueig } =
            response.content;

          this.resetRecaptcha();

          if (validAccess) {
            this.showSuccessAlert();
            this.msgService.resetMessages();
            this.showSimpleForm = false;
          } else {
            if (tipusBloqueig) {
              this.setMessageErrorWithHoursAndMinutes(
                tipusBloqueig,
                hoursBloqueig,
                minsBloqueig,
              );
            }
          }
        }
      });
  }

  private showSuccessAlert(): void {
    this.isSuccessAlertShown = true;
    setTimeout(() => (this.isSuccessAlertShown = false), 4000);
  }

  confirmCode(): void {
    const request = {
      ...this.simpleLoginForm.value,
      nifTitular: String(
        this.simpleLoginForm.get('nifTitular')?.value,
      )?.toUpperCase(),
      matricula: String(
        this.simpleLoginForm.get('matricula')?.value,
      )?.toUpperCase(),
      pin: this.verificationCode.value.code,
    };
    this.loginService
      .confirmSMSCode(request, this.recaptchaValue)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe({
        next: (response: SimpleLoginResponse) => {
          if (response.content) {
            const {
              loginOk,
              hoursBloqueig,
              minsBloqueig,
              tipusError,
              usuario,
            } = response.content;

            if (loginOk) {
              this.resetCaptcha = false;
              this.storeSrv.licensePlate =
                this.simpleLoginForm.value.matricula?.toUpperCase();
              this.storeSrv.profileUser = IdentificationType.LOGIN_SIMPLE;
              this.emitNifToDispacherHeader(usuario.nif);
              this.msgService.resetMessages();
              this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
            } else {
              this.resetRecaptcha();

              this.setMessageErrorWithHoursAndMinutes(
                tipusError,
                hoursBloqueig,
                minsBloqueig,
              );
            }
          }
        },
        error: () => {
          this.resetRecaptcha();
        },
      });
  }

  private resetRecaptcha(): void {
    // reseteamos el valor de resetCaptcha y luego el recaptcha
    this.resetCaptcha = false;
    this.resetCaptcha = true;

    this.recaptchaValue = '';
  }

  private emitNifToDispacherHeader(nif: string): void {
    document.dispatchEvent(new CustomEvent('customUserText', { detail: nif }));
  }

  captchaResponse = (event: ComponentEventOutput): void => {
    this.recaptchaValue = event.componentValue;
  };

  captchaExpired = (): void => {
    this.recaptchaValue = '';
  };

  private setMessageErrorWithHoursAndMinutes = (
    lockType: string,
    hours?: string,
    minutes?: string,
  ): void => {
    if (lockType) {
      const title = this.translate.instant(lockType, {
        ...(hours && { hours }),
        ...(minutes && { minutes }),
      });
      this.msgService.addMessages([{ severity: 'error', title }]);
    }
  };
}
