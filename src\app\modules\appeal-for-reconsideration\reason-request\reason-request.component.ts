import { ChangeDetectorR<PERSON>, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { map, Subject, takeUntil, type Observable } from 'rxjs';

import { FormGroup } from '@angular/forms';
import { VehiclesSelectedInfo } from '@app/shared/models';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';
import {
  AppRoutes,
  FunctionalModuleEnum,
  IdentificationType,
  StatusCodes,
} from '@core/models';
import {
  CustomRouterService,
  HeaderInfoService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { ReasonAllegationEndpointService } from '@modules/allegations/reason-allegation/reason-allegation-endpoint.service';
import { ReasonAllegationService } from '@modules/allegations/reason-allegation/reason-allegation.service';
import { ReasonRequestEndpointService } from '@modules/appeal-for-reconsideration/reason-request/reason-request-endpoint.service';
import { RequestUpdateResourcesTramit } from '@modules/appeal-for-reconsideration/reason-request/reason-request.model';
import { AppelForReconsiderationService } from '@modules/appeal-for-reconsideration/services/appel-for-reconsideration.service';
import { RecursValidationsService } from '@app/shared/services/recurs-validations';
import { BankDetailInput } from '@modules/domiciliation/bank-details/model/bank-details.model';
import {
  ContestableActDocument,
  Reasons,
  ReasonsFormData,
} from '@shared/components';
import { PaymentsService } from '@shared/services';
import {
  FileFormatsSeparation,
  iDocumentPadoct,
  Nullable,
  SeAuthService,
  SeHttpResponse,
  SeModalOutputEvents,
  SeUser,
} from 'se-ui-components-mf-lib';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-reason-request',
  templateUrl: './reason-request.component.html',
  styleUrls: ['./reason-request.component.scss'],
})
export class ReasonRequestComponent implements OnInit, OnDestroy {
  private readonly unsubscribe: Subject<void> = new Subject();

  protected bankDetailInput: Nullable<BankDetailInput>;

  isIbanInvalid: boolean = true;

  private iban: string | undefined;
  private dr: boolean | undefined;
  private bankDataDoc: iDocumentPadoct[] = [];

  vehiclesInfo: Nullable<VehiclesSelectedInfo>;

  documentsSigedaDescriptions: ContestableActDocument[] = [];

  reasonsData: Reasons[] | undefined;
  idTramit: Nullable<string>;
  functionalModule = FunctionalModuleEnum;

  reasonsFormData: Nullable<ReasonsFormData>;
  isSuspensioAlertVisible = false;

  isSelfRepresented: boolean = false;
  showReasAlert: boolean = false;

  fileFormatSeparation: FileFormatsSeparation = FileFormatsSeparation.COMMA;

  private user: SeUser | undefined;

  constructor(
    private readonly storageData: StorageService,
    private readonly translate: TranslateService,
    private readonly loginSrv: LoginResponseService,
    private readonly specificConfigurationSrv: SpecificConfigurationService,
    private readonly header: HeaderInfoService,
    private readonly seAuthService: SeAuthService,
    private readonly reasonsRequestEndpoints: ReasonRequestEndpointService,
    private readonly reasonsAllegationEndpoints: ReasonAllegationEndpointService,
    private readonly reasonResourcesService: ReasonAllegationService,
    private readonly customRouter: CustomRouterService,
    private readonly paymentsSrv: PaymentsService,
    private readonly cdr: ChangeDetectorRef,
    private readonly procedureHeaderService: ProceduresHeaderService,
    private readonly appelForReconsiderationService: AppelForReconsiderationService,
    private readonly recursService: RecursValidationsService,
  ) {
    this.isSelfRepresented =
      this.storageData.profileUser === IdentificationType.NOM_PROPI;
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  ngOnInit(): void {
    this.user = this.seAuthService.getSessionStorageUser();
    this.vehiclesInfo =
      this.appelForReconsiderationService.getVehiclesSelectedInfo(
        this.user.nif,
      );
    this.idTramit = this.storageData.getRecursVehicles().recursId;

    this.showReasAlert =
      this.storageData.getRecursVehicles().showReasAlert ?? false;

    this.isSuspensioAlertVisible = this.isSomeVehicleUnpaid();

    this.procedureHeaderService.setupRecursHeader(
      this.vehiclesInfo?.matriculas,
    );

    if (!this.idTramit) {
      this.onGoBackButtonClick();
      return;
    }

    this.getBankDetails(this.idTramit)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(({ iban, dr }) => {
        this.iban = iban;
        this.dr = dr;
        this.setBankDetails({ iban, dr });
      });

    this.getReasonsData(this.idTramit);
  }

  private isSomeVehicleUnpaid(): boolean {
    return this.storageData
      .getRecursVehicles()
      ?.validation?.vehiclesRecurribles?.some(
        ({ codiSituacio }) =>
          codiSituacio !== StatusCodes.Paid &&
          codiSituacio !== StatusCodes.Paid_telematically,
      );
  }

  private getBankDetails(
    idTramit: string,
  ): Observable<{ iban: string | undefined; dr: boolean | undefined }> {
    return this.reasonsRequestEndpoints.getRecursResum(idTramit).pipe(
      map((resum) => ({
        iban: resum?.ibanNumeroCompte,
        dr: resum?.declaracioNumeroCompte,
      })),
    );
  }

  private setBankDetails({
    iban,
    dr,
  }: {
    iban: string | undefined;
    dr: boolean | undefined;
  }): void {
    this.bankDetailInput = this.paymentsSrv.getBankDetailInput({
      iban,
      dr,
      isIbanRequired: this.validateIbanRequired(),
      docData: {
        entityId: this.idTramit ?? '',
        idFunctionalModule: this.functionalModule.RECURS,
        lstCodGTATSigedaType: ['TD11-021'],
        subtypes: ['0'],
      },
      isPresenter:
        this.storageData.profileUser !== IdentificationType.CONVENIAT,
      drLabel: this.translate.instant(
        'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_2.BANK_DETAIL_DR',
      ),
    });
  }

  onReasLinkClick(): void {
    const recursVehicles = this.storageData.getRecursVehicles();

    this.recursService.initReas({
      vehiclesInfo: this.storageData.getVehiclesSelected(),
      advertimentTerminiAcceptat: recursVehicles?.advertimentTerminiAcceptat,
      nomesVehiclesValids: recursVehicles?.nomesVehiclesValids,
      csvNotificacio: recursVehicles?.csvNotificacio,
    });
  }

  getVehiclesSelectedInfo(): VehiclesSelectedInfo | null {
    const recursResponse = this.storageData.getRecursVehicles();

    if (
      !recursResponse?.validation?.vehiclesRecurribles ||
      recursResponse?.validation?.vehiclesRecurribles.length === 0
    ) {
      return null;
    }

    return {
      provisional: this.specificConfigurationSrv.isProvisional,
      matriculas: recursResponse.validation.vehiclesRecurribles.map(
        (vehicle) => vehicle.matricula,
      ),
      exercici: this.specificConfigurationSrv.currentExercise,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
      nifTitular: this.user?.nif,
    };
  }

  private getReasonsData(recursId: string): void {
    const request = recursId;
    this.reasonsAllegationEndpoints
      .getReasons(request)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((result) => {
        if (result.content) {
          this.reasonsData = result.content.motius ?? [];
          this.documentsSigedaDescriptions = result.content.documents ?? [];
        }
      });
  }

  onResourcesOptionsChange(data: ReasonsFormData): void {
    this.reasonsFormData = data;
  }

  onGoBackButtonClick(): void {
    this.header.reset();
    this.storageData.clearReasonsSelected();
    this.storageData.clearResourcesTramitInfo();
    this.storageData.clearIdTramit();
    this.storageData.deleteNotificationsData();
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  private updateRecursTramit(request: RequestUpdateResourcesTramit): void {
    if (this.idTramit) {
      this.reasonsRequestEndpoints
        .updateResourcesTramit(this.idTramit, request)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result: SeHttpResponse) => {
          if (result.content) {
            this.customRouter.navigateByBaseUrl(
              AppRoutes.RECONSIDERATION_NOTIFICATION,
            );
          }
        });
    }
  }

  onContinueButtonClick(): void {
    if (this.evaluateIbanWarningModalDisplay()) {
      this.appelForReconsiderationService
        .openAlertModal()
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result) => {
          if (result === SeModalOutputEvents.MAIN_ACTION) {
            this.configureAndSubmitTramitRequest();
          }
        });

      return;
    }

    this.configureAndSubmitTramitRequest();
  }

  private evaluateIbanWarningModalDisplay(): boolean {
    const isNomPropi =
      this.storageData.profileUser === IdentificationType.NOM_PROPI;
    const isConveniat =
      this.storageData.profileUser === IdentificationType.CONVENIAT;
    const isRepresentant =
      this.storageData.profileUser === IdentificationType.REPRESENTATIVE;
    const isFuncionario =
      this.storageData.profileUser === IdentificationType.CIVIL_SERVANT;

    const hasPaidVehicles = this.storageData
      .getRecursVehicles()
      ?.validation?.vehiclesRecurribles?.some(
        (vehicle) =>
          vehicle.codiSituacio === StatusCodes.Paid ||
          vehicle.codiSituacio === StatusCodes.Paid_telematically,
      );

    return (
      !this.iban &&
      ((isNomPropi && !hasPaidVehicles) ||
        isConveniat ||
        isRepresentant ||
        isFuncionario)
    );
  }

  private configureAndSubmitTramitRequest(): void {
    if (this.idTramit) {
      const request: RequestUpdateResourcesTramit = {
        motius: [],
        ibanNumeroCompte: this.iban || undefined,
        declaracioNumeroCompte: this.iban ? this.dr || undefined : undefined,
        ...(this.bankDataDoc
          ? {
              documentNumeroCompte: this.bankDataDoc?.map((doc) => ({
                id: doc.id,
                filename: doc.nom,
                documentTypeId: doc.codeDescriptionComplementary,
                documentType: doc.nomSigedaType,
                description: doc.description,
              }))[0],
            }
          : {}),
      };

      if (
        this.reasonsFormData?.reasonsFormValue &&
        this.reasonsFormData?.reasonsList
      ) {
        request.motius = this.reasonResourcesService.getRequestReasonsSelected(
          this.reasonsFormData.reasonsFormValue,
          this.reasonsFormData.reasonsList,
          this.reasonsFormData.reasonsDocuments,
        );
      }

      this.storageData.setReasonsSelected(
        this.reasonsFormData?.reasonsFormValue,
      );
      this.updateRecursTramit(request);
    }
  }

  protected isValidateRepresentative = (): boolean =>
    this.paymentsSrv.isValidateRepresentative();

  protected onBankDetailChange(event: Event): void {
    const customEvent = event as CustomEvent<FormGroup>;
    this.isIbanInvalid = customEvent.detail.invalid;
    this.iban = this.isIbanInvalid ? '' : customEvent.detail.value.iban;
    this.dr = customEvent.detail.value.DR;
    this.bankDataDoc = this.iban ? customEvent.detail.value.bankDataDoc : [];

    this.cdr.detectChanges();
  }

  private validateIbanRequired(): boolean {
    const isNomPropi =
      this.storageData.profileUser === IdentificationType.NOM_PROPI;
    const hasPaidVehicles = this.storageData
      .getRecursVehicles()
      ?.validation?.vehiclesRecurribles?.some(
        (vehicle) =>
          vehicle.codiSituacio === StatusCodes.Paid ||
          vehicle.codiSituacio === StatusCodes.Paid_telematically,
      );

    return isNomPropi && hasPaidVehicles;
  }
}
