import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Input,
  OnInit,
  Output,
} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import {
  Column,
  Row,
  SeModal,
  SeModalModule,
  SeModalOutputEvents,
  SeTableModule,
} from 'se-ui-components-mf-lib';
import { VehiclePayment } from '@shared/models';
import { NgIf } from '@angular/common';

export const BASE_TRANSLATE = 'SE_PADRO_CO2.MODAL.VEHICLES';
export enum TramitProcessType {
  PAGAMENTS = 'PAGAMENTS',
  RECURS = 'RECURS',
}

@Component({
  selector: 'app-executiva-alert-modal',
  standalone: true,
  imports: [SeModalModule, SeTableModule, NgIf, TranslateModule],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <se-modal
      [data]="data"
      (modalOutputEvent)="onContinue($event)"
      (modalSecondaryButtonEvent)="closeModal()"
    >
      <div [innerHTML]="message | translate"></div>
      <div
        class="col-12 d-flex flex-column content-spacing"
        *ngIf="vehicles.length > 0"
      >
        <div class="mb-2">
          {{ messageInfo | translate }}
        </div>
        <se-table
          [styleClass]="'border-table'"
          [resizable]="false"
          [columns]="vehiclesColumns"
          [data]="vehiclesRows"
        ></se-table>
      </div>
    </se-modal>
  `,
  styleUrls: ['./executiva-alert-modal.component.scss'],
})
export class ExecutivaAlertModalComponent implements OnInit {
  @Input() data: SeModal | undefined;
  @Input() vehicles: VehiclePayment[] = [];
  @Input() message = 'SE_PADRO_CO2.MODAL.EXECUTIVA_ALERT.MESSAGE_1';
  @Input() messageInfo: string = '';
  @Input() tramitProcess!: TramitProcessType;
  tramitProcessType = TramitProcessType;

  @Output() continueOutput: EventEmitter<void> = new EventEmitter<void>();

  vehiclesColumns: Column[] = [];
  vehiclesRows: Row[] = [];

  constructor(
    private readonly activatedModalService: NgbActiveModal,
    private readonly translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    this.setVehiclesColumns();
    this.setVehiclesRows();
  }

  private setVehiclesColumns(): void {
    this.vehiclesColumns = this.getVehiclesColumns();
  }

  private setVehiclesRows(): void {
    this.vehiclesRows = this.getVehiclesRows(this.vehicles);
  }

  private getVehiclesRows(vehicles: VehiclePayment[]): Row[] {
    return (vehicles ?? []).map((vehicle) => {
      return {
        data: {
          matricula: {
            value: vehicle.matricula,
          },
          marca: {
            value: vehicle.marca,
          },
          exercici: {
            value: vehicle.exercici,
          },
          quota: {
            value: vehicle.quota,
          },
          model: {
            value: vehicle.model,
          },
        },
      };
    });
  }

  private getVehiclesColumns(): Column[] {
    return [
      {
        key: 'matricula',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.MATRICULA`,
        ),
        size: 10,
      },
      {
        key: 'marca',
        header: this.translateService.instant(`${BASE_TRANSLATE}.TABLE.MARCA`),
        size: 10,
      },
      this.tramitProcess === this.tramitProcessType.PAGAMENTS
        ? {
            key: 'quota',
            header: this.translateService.instant(
              `${BASE_TRANSLATE}.TABLE.QUOTA`,
            ),
            size: 10,
            cellComponentName: 'currencyCellComponent',
          }
        : {
            key: 'model',
            header: this.translateService.instant(
              `${BASE_TRANSLATE}.TABLE.MODEL`,
            ),
            size: 10,
          },
      {
        key: 'exercici',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.EXERCICI`,
        ),
        size: 10,
      },
    ];
  }

  onContinue(event: string): void {
    if (event === SeModalOutputEvents.MAIN_ACTION) {
      this.continueOutput.emit();
    }

    this.closeModal();
  }

  closeModal(): void {
    this.activatedModalService.close();
  }
}
