import { Component, Input } from '@angular/core';
import { Nullable, SeTagTheme } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-detail-element',
  templateUrl: './detail-element.component.html',
  styleUrls: ['./detail-element.component.scss'],
})
export class DetailElementComponent {
  @Input() title: Nullable<string>;

  @Input() description: Nullable<string>;

  @Input() tooltipText: Nullable<string>;

  @Input() linkText: Nullable<string>;

  @Input() tagLabel: Nullable<string>;

  @Input() tagTheme: Nullable<SeTagTheme>;

  @Input() linkCommand: Nullable<() => unknown>;

  onLinkClick(): void {
    if (this.linkCommand) {
      this.linkCommand();
    }
  }
}
