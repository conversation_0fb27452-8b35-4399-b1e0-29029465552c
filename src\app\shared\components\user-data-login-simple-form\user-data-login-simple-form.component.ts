import { Component, Input } from '@angular/core';
import { FormGroup } from '@angular/forms';

@Component({
  selector: 'app-user-data-login-simple-form',
  template: `
    <section *ngIf="loginSimpleUserForm">
      <hr />
      <p>
        {{
          'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_3.FORM_USER_LOGIN_SIMPLE_DATA.DESCRIPTION'
            | translate
        }}
      </p>

      <form [formGroup]="loginSimpleUserForm" class="row">
        <se-input
          class="col-12 col-md-3"
          id="name"
          [label]="
            'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_3.FORM_USER_LOGIN_SIMPLE_DATA.NAME'
              | translate
          "
          formControlName="name"
        ></se-input>
        <se-input
          class="col-12 col-md-3"
          id="surname"
          [label]="
            'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_3.FORM_USER_LOGIN_SIMPLE_DATA.SURNAME'
              | translate
          "
          formControlName="surname"
        ></se-input>
      </form>
    </section>
  `,
})
export class UserDataLoginSimpleFormComponent {
  @Input() loginSimpleUserForm: FormGroup | undefined;
}
