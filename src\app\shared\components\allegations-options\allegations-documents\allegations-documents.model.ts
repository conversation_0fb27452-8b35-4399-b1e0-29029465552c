import { SeHttpResponse, type Nullable } from 'se-ui-components-mf-lib';
import { ContestableActDocument, Reasons } from '../allegations-options.model';
import { AtesaPhoneData } from '@app/core/models';

export interface ResponseGetAllegations extends SeHttpResponse {
  content: ResponseGetAllegationsContent;
}

export interface ResponseGetAllegationsContent {
  motius: Reasons[];
  documents: ContestableActDocument[];
}
export interface RequestCreateAllegacioTramit {
  idPerssCens: string;
  tipusAcces: string;
  exercici: string;
  trucadaTelefonica?: AtesaPhoneData;
  matricules: string[];
  nifRepresentant?: Nullable<string>;
}

export interface ResponseAllegacioTramit extends SeHttpResponse {
  content: AllegacioTramitContent;
}

export interface AllegacioTramitContent {
  allegacioId: string;
}

export interface TemplateFile {
  docType: string;
  idType: string;
  required?: boolean;
}
