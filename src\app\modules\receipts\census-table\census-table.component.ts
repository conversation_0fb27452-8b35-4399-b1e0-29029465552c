import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { MenuItem } from 'primeng/api';
import { PaginatorState } from 'primeng/paginator';
import { Subject, take, takeUntil } from 'rxjs';

import { Component, OnDestroy, OnInit } from '@angular/core';
import { FormControl, FormGroup } from '@angular/forms';
import { FeatureFlagService } from '@app/core/services/feature-flag';
import { DownloadExcelModalComponent } from '@app/shared/components/download-excel-modal/download-excel-modal.component';
import { PaymentTypesModalComponent } from '@app/shared/components/payment-types-modal/payment-types-modal.component';
import {
  AppRoutes,
  Co2User,
  IdentificationType,
  TABLE_SORT_NAMES,
} from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SituationsStoreService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { VechilesRowValue } from '@shared/components';
import {
  ListPadroRequest,
  ListPadroResponse,
  PadroItem,
  SortOrder,
  VehicleSelected,
  VehiclesSelectedInfo,
  YesOrNoOptionsEnum,
} from '@shared/models';
import {
  PadroListEndPointService,
  PaginationService,
  PaymentsService,
} from '@shared/services';
import {
  Column,
  Row,
  SeAuthService,
  SeButton,
  SeDeviceService,
  SeModalService,
  SortParams,
  TableSortOrder,
} from 'se-ui-components-mf-lib';
import { SeCheckbox } from 'se-ui-components-mf-lib/lib/components/checkbox/checkbox.model';
import { ReceiptsUtilsService } from '../receipts-utils.service';
import { CensusTableFiltersModalComponent } from './census-table-filters/census-table-filters-modal.component';
import { CensusTableService } from './census-table.service';

@Component({
  selector: 'app-census-table',
  templateUrl: './census-table.component.html',
  styleUrls: ['./census-table.component.scss'],
})
export class CensusTableComponent implements OnInit, OnDestroy {
  MIN_ELEMENTS_TO_SHOW_FILTERS: number = 6;
  MIN_ELEMENTS_TO_SHOW_PLATE_FILTER: number = 1;
  MIN_ELEMENTS_TO_SHOW_FILTER_BUTTONS: number = 0;

  protected filterForm: FormGroup = new FormGroup({});

  private exercise: string;

  protected tableColumns: Column[];
  protected selectedRows: Row[] = [];
  private _tableRows: Row[] = [];

  set tableRows(data: PadroItem[]) {
    this.setListPadroData(data);
  }

  get tableRows(): Row[] {
    return this._tableRows;
  }

  get rangeForm(): FormGroup {
    return this.filterForm.get('rangeForm') as FormGroup;
  }

  get showRegisterDomiciliation(): boolean {
    return this.featureFlagSrv.registerDomiciliation;
  }

  protected downloadButton = this.receiptsUtilsService.getDownloadButton();
  protected rangeFilter = this.receiptsUtilsService.getRangeFilterOptions();
  protected applyButton = this.receiptsUtilsService.getApplyButton();
  protected resetButton = this.receiptsUtilsService.getResetButton();

  protected isCoordinador: boolean = false;
  protected isLoginSimple: boolean = false;
  protected isProvisional: boolean = false;
  protected showAllegations: boolean = false;
  protected showFilters: boolean = false;
  protected showTable: boolean = false;
  protected isFilteredByPlate: boolean = false;
  protected isFilteredByLowBar: boolean = false;
  protected canSearchByPlate: boolean = false;

  protected yesOrNoOptions: SeCheckbox[] =
    this.receiptsUtilsService.getYesOrNoOptions();
  protected otherActionDropdownButtonOptions: SeButton =
    this.receiptsUtilsService.getOtherDropdownOptions();
  protected dropdownButtonAnotherActions: MenuItem[] = [];

  protected totalRecords: number = 0;
  protected itemsPerPage = this.paginationSrv.getItemsPerPage();
  protected rowsPerPageOptions = this.paginationSrv.getRowsPerPageOptions();
  protected currentPage: number = 0;
  protected sortField: TABLE_SORT_NAMES | null = null;
  protected sortOrder: SortOrder = SortOrder.NONE;

  private unsubscribe: Subject<void> = new Subject();
  private user: Co2User | undefined;

  constructor(
    private specificConfigurationService: SpecificConfigurationService,
    private listPadroEndpointService: PadroListEndPointService,
    private seAuthService: SeAuthService,
    private receiptsUtilsService: ReceiptsUtilsService,
    private loginService: LoginResponseService,
    private storageData: StorageService,
    private paymentService: PaymentsService,
    private censusTableService: CensusTableService,
    private customRouter: CustomRouterService,
    private seModalService: SeModalService,
    private paginationSrv: PaginationService,
    private featureFlagSrv: FeatureFlagService,
    readonly deviceService: SeDeviceService,
    private situationsStoreService: SituationsStoreService,
  ) {
    this.tableColumns = this.getTableColumns();
    this.setUpForm();
    this.exercise = this.specificConfigurationService.currentExercise;
  }

  ngOnInit(): void {
    this.isCoordinador =
      this.storageData.profileUser === IdentificationType.COORDINADOR;
    this.isLoginSimple =
      this.storageData.profileUser === IdentificationType.LOGIN_SIMPLE;
    this.isProvisional = this.specificConfigurationService.isProvisional;
    this.showAllegations =
      this.specificConfigurationService.inTimeToBeAlleged && this.isProvisional;
    this.user = this.loginService.user;

    if (this.user && this.seAuthService.getSessionStorageUser()) {
      this.getPadroData();
    } else {
      this.customRouter.navigateByBaseUrl(AppRoutes.IDENTIFICATION);
    }
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private setUpForm(): void {
    this.filterForm = this.receiptsUtilsService.getCommonFormGroup();
    this.filterForm.addControl('nou', new FormControl(null));
    this.filterForm.addControl('situation', new FormControl(''));
  }

  handlePageChange(event: PaginatorState): void {
    this.itemsPerPage = event.rows as number;
    const currentPage = event.page as number;

    this.getPadroData(currentPage);
  }

  getPadroData(currentPage: number = 0): void {
    this.currentPage = currentPage;

    this.listPadroEndpointService
      .getListPadro(this.getRequestToGetPadroData(currentPage))
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((data: ListPadroResponse) => {
        this.isFiltered();
        this.tableRows = data?.content?.results || [];
        this.totalRecords = data?.content?.total || 0;
        this.setVisilityTableAndFilters();
      });
  }

  private isFiltered(): void {
    this.isFilteredByLowBar = Object.entries(this.filterForm.value)
      .filter((entry) => entry[0] !== 'plate')
      .map((entry) => entry[1])
      .some((value) => {
        if (typeof value === 'object' && value) {
          return Object.values(value).some((value) => value);
        }
        return value;
      });
    this.isFilteredByPlate = !!this.filterForm.get('plate')?.value;
  }

  protected openModalFilter(): void {
    const modalRef = this.seModalService.openModal({
      title: 'SE_PADRO_CO2.MODAL.FILTER.TITLE',
      severity: 'info',
      closable: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.ACCEPT',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CLOSE',
      keyboard: true,
      hideIcon: true,
      size: 'xl',
      component: CensusTableFiltersModalComponent,
    });

    modalRef.componentInstance.filterForm = this.filterForm;

    modalRef.componentInstance.handleApplyFilter
      ?.pipe(takeUntil(this.unsubscribe))
      .subscribe(() => {
        this.getPadroData();
      });
  }

  private getRequestToGetPadroData(currentPage: number = 0): ListPadroRequest {
    const { rangeFilter, nou, domiciled, plate, situation } =
      this.filterForm.value;
    const plateValue =
      plate?.toUpperCase() ||
      this.storageData.licensePlate?.toUpperCase() ||
      this.user?.matricula?.toUpperCase();
    return {
      filter: {
        tipusAccess: this.user?.tipusAccess || IdentificationType.NOM_PROPI,
        nifTitular: null,
        idPersTitular: this.user?.idPersTitular as string,
        quotaDes: rangeFilter?.from || -1,
        quotaFins: rangeFilter?.to || -1,
        nou: this.getValueForYesOrNotOptions(nou),
        domicilat: this.getValueForYesOrNotOptions(domiciled),
        listMatriculas: plateValue ? [plateValue] : null,
        situacio: situation || null,
        exercici: this.exercise,
        provisional: this.isProvisional,
      },
      options: {
        first: currentPage + 1,
        rows: this.itemsPerPage,
        sortField: this.sortField,
        sortOrder: this.sortOrder,
      },
    };
  }

  // nou y domiciled son any, del formulario
  private getValueForYesOrNotOptions(value: null | string[]): boolean | null {
    return value
      ? value.length === 1
        ? value[0] === YesOrNoOptionsEnum.yes
          ? true
          : false
        : null
      : null;
  }

  onSelectionChange(rows: Row[]): void {
    this.selectedRows = rows;
    const vehiclesInfo: VehiclesSelectedInfo = this.getSelectedVehicles();
    const { button, actions } =
      this.censusTableService.updateOtherActionsDropdownButtonConfig(
        this.otherActionDropdownButtonOptions,
        this.selectedRows,
        vehiclesInfo,
      );
    this.otherActionDropdownButtonOptions = button;
    this.dropdownButtonAnotherActions = actions;
  }

  private getSelectedVehicles(): VehiclesSelectedInfo {
    const vehicles: VehicleSelected[] =
      this.receiptsUtilsService.getVehiclesSelectedByRows(this.selectedRows);
    return this.receiptsUtilsService.getVehiclesSelectedInfoByVehicles(
      vehicles,
      this.isProvisional,
      this.exercise,
    );
  }

  private getSelectedPlates(): VehiclesSelectedInfo {
    const plates: string[] = this.selectedRows.map(
      (row: Row) => row.data['plate']?.value,
    );

    return this.receiptsUtilsService.getVehiclesSelectedInfoByPlates(
      plates,
      this.isProvisional,
      this.exercise,
    );
  }

  onPayButton(): void {
    const vehiclesInfo: VehiclesSelectedInfo = this.getSelectedVehicles();
    this.paymentService.initPayment(vehiclesInfo);
  }

  onAllegationsButton(): void {
    this.storageData.clearReasonsSelected();
    this.storageData.clearIdTramit();
    const vehiclesInfo: VehiclesSelectedInfo = this.getSelectedPlates();
    this.storageData.setVehiclesSelected(vehiclesInfo);
    this.customRouter.navigateByBaseUrl(AppRoutes.ALLEGATIONS);
  }

  sortByColumn(sort: SortParams): void {
    if (sort.sortOrder === TableSortOrder.ASC) this.sortOrder = SortOrder.ASC;
    if (sort.sortOrder === TableSortOrder.DESC) this.sortOrder = SortOrder.DESC;
    if (
      sort.sortOrder !== TableSortOrder.ASC &&
      sort.sortOrder !== TableSortOrder.DESC
    ) {
      this.sortOrder = SortOrder.NONE;
    }
    this.sortField =
      TABLE_SORT_NAMES[sort.columnKey as keyof typeof TABLE_SORT_NAMES];
    if (this.sortField === TABLE_SORT_NAMES.domiciled) {
      this.sortField = this.isProvisional
        ? TABLE_SORT_NAMES.domiciliat
        : TABLE_SORT_NAMES.definitiveDebt;
    }
    if (this.sortField === TABLE_SORT_NAMES.situation) {
      this.sortField = this.isProvisional
        ? TABLE_SORT_NAMES.situation
        : TABLE_SORT_NAMES.definitiveSituation;
    }
    if (this.sortField === TABLE_SORT_NAMES.shares) {
      this.sortField = this.isProvisional
        ? TABLE_SORT_NAMES.shares
        : TABLE_SORT_NAMES.definitiveShares;
    }
    this.getPadroData();
  }

  private getTableColumns(): Column[] {
    return this.receiptsUtilsService.getCensusTableColumns();
  }

  private setListPadroData(data: PadroItem[]): void {
    if (data === undefined) return;
    this.situationsStoreService.situations$
      .pipe(take(1))
      .subscribe((situations) => {
        this._tableRows = this.censusTableService.getTableRowPadroData(
          data,
          situations || [],
        );
      });
  }

  onRowClick(event: VechilesRowValue): void {
    this.censusTableService.goToDetail(event);
  }

  protected openPaymentTypesModal(): void {
    if (this.specificConfigurationService.isProvisional) {
      this.seModalService.openModal({
        title: 'SE_PADRO_CO2.MODAL.PAYMENT_OPTIONS.TITLE',
        severity: 'info',
        closable: this.showRegisterDomiciliation,
        closableLabel: 'SE_PADRO_CO2.MODAL.PAYMENT_OPTIONS.ACCEPT_BTN',
        secondaryButton: true,
        secondaryButtonLabel: 'SE_PADRO_CO2.MODAL.PAYMENT_OPTIONS.CLOSE_BTN',
        keyboard: true,
        hideIcon: true,
        size: 'xl',
        component: PaymentTypesModalComponent,
      });
    }
  }

  protected openDownloadExcelModal(): NgbModalRef {
    const modalRef = this.seModalService.openModal({
      title: 'SE_PADRO_CO2.RECEIPTS.EXCEL.MODAL_TITLE',
      severity: 'info',
      closable: true,
      closableLabel: 'SE_PADRO_CO2.RECEIPTS.EXCEL.DONWLOAD_EXCEL',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
      keyboard: true,
      hideIcon: false,
      size: 'xl',
      component: DownloadExcelModalComponent,
    });
    modalRef.componentInstance.request = this.getRequestToGetPadroData();
    modalRef.componentInstance.request.options = {
      ...modalRef.componentInstance.request.options,
      first: 0, // Da igual la página, queremos todos los registros
      rows: this.totalRecords, // Establecemos el número total de registros para que los descargue todos
    };
    modalRef.componentInstance.selectedPlates = this.selectedRows.map(
      (row: Row) => row.data['plate']?.value,
    );
    return modalRef;
  }

  private setVisilityTableAndFilters(): void {
    this.showTable = this.isTableVisible();
    this.showFilters = this.isFiltersVisible();
    this.canSearchByPlate =
      this.isPlateFilterVisible() && !this.user?.matricula;
  }

  private isTableVisible(): boolean {
    return (
      this.totalRecords > this.MIN_ELEMENTS_TO_SHOW_FILTER_BUTTONS ||
      this.isFilteredByPlate ||
      this.isFilteredByLowBar
    );
  }

  private isFiltersVisible(): boolean {
    return (
      this.totalRecords > this.MIN_ELEMENTS_TO_SHOW_FILTERS ||
      this.isFilteredByLowBar
    );
  }

  private isPlateFilterVisible(): boolean {
    return (
      !this.deviceService.isMobile() &&
      (this.totalRecords > this.MIN_ELEMENTS_TO_SHOW_PLATE_FILTER ||
        this.isFilteredByPlate ||
        this.isFilteredByLowBar)
    );
  }
}
