import { Injectable } from '@angular/core';
import {
  TitularPanelData,
  TitularPanelFields,
  UserInfo,
} from '../models/titular-data-modal.model';

@Injectable({
  providedIn: 'root',
})
export class TitularDataModalService {
  private _fieldNotInformedTranslation = `SE_PADRO_CO2.MODAL.TITULAR_DATA.FIELD_NOT_INFORMED`;

  setPanelData(userData: UserInfo): TitularPanelData[] {
    return [
      {
        panelTitle:
          'SE_PADRO_CO2.MODAL.TITULAR_DATA.IDENTIFICATION_PANEL.TITLE',
        fields: this.setIdentificationFields(userData),
      },
      {
        panelTitle: 'SE_PADRO_CO2.MODAL.TITULAR_DATA.NOTIFICATIONS_PANEL.TITLE',
        fields: this.setNotificationFields(userData),
      },
      {
        panelTitle: 'SE_PADRO_CO2.MODAL.TITULAR_DATA.CONTACT_DATA_PANEL.TITLE',
        fields: this.setContactFields(userData),
      },
    ];
  }

  private setIdentificationFields(userData: UserInfo): TitularPanelFields[] {
    const baseTranslation =
      'SE_PADRO_CO2.MODAL.TITULAR_DATA.IDENTIFICATION_PANEL.FIELDS.';
    return [
      {
        label: `${baseTranslation}NAME`,
        value: userData?.nombreCompleto || '',
      },
      {
        label: `${baseTranslation}NIF`,
        value: userData?.nif || '',
      },
      {
        label: `${baseTranslation}FISCAL_ADDRESS`,
        value:
          userData?.direccionFiscal?.concatDireccion ||
          this._fieldNotInformedTranslation,
      },
    ];
  }

  private setNotificationFields(userData: UserInfo): TitularPanelFields[] {
    const baseTranslation =
      'SE_PADRO_CO2.MODAL.TITULAR_DATA.NOTIFICATIONS_PANEL.FIELDS.';
    const noficationFields = [
      {
        label: `${baseTranslation}NOTIFICATION_CHANNEL`,
        value: userData?.canalElectronico
          ? `${baseTranslation}ELECTRONIC_NOTIFICATION`
          : `${baseTranslation}POSTAL_MAIL`,
      },
    ];

    if (userData.canalElectronico) {
      noficationFields.push(
        {
          label: `${baseTranslation}SUBSCRIPTION_TYPE`,
          value: userData?.isObligado
            ? `${baseTranslation}OBLIGATORY_SUBSCRIPTION`
            : `${baseTranslation}VOLUNTARY_SUBSCRIPTION`,
        },
        {
          label: `${baseTranslation}NOTIFICATIONS_NOTICES`,
          value: userData?.isAvis
            ? `${baseTranslation}NOTICES_ACTIVATED`
            : `${baseTranslation}NOTICES_NOT_ACTIVATED`,
        },
      );
    } else {
      noficationFields.push({
        label: `${baseTranslation}POSTAL_ADDRESS`,
        value:
          userData?.direccionNotificacion?.concatDireccion ||
          userData?.direccionFiscal?.concatDireccion ||
          this._fieldNotInformedTranslation,
      });
    }

    return noficationFields;
  }

  private setContactFields(userData: UserInfo): TitularPanelFields[] {
    const baseTranslation =
      'SE_PADRO_CO2.MODAL.TITULAR_DATA.CONTACT_DATA_PANEL.FIELDS.';
    return [
      {
        label: `${baseTranslation}EMAIL`,
        value:
          userData?.notificacionNEGeneral?.email ||
          this._fieldNotInformedTranslation,
      },
      {
        label: `${baseTranslation}MOBILE`,
        value:
          userData?.notificacionNEGeneral?.telefono ||
          this._fieldNotInformedTranslation,
      },
    ];
  }
}
