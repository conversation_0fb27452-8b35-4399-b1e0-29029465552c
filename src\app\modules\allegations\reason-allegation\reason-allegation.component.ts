import { Component, OnInit } from '@angular/core';
import {
  AppRoutes,
  FunctionalModuleEnum,
  IdentificationType,
} from '@core/models';
import { CustomRouterService, StorageService } from '@core/services';
import {
  ContestableActDocument,
  Reasons,
  ReasonsFormData,
  RequestCreateAllegacioTramit,
  ResponseAllegacioTramit,
} from '@shared/components';
import { Nullable } from 'se-ui-components-mf-lib';
import { RequestUpdateAllegacioTramit } from './reason-allegation.model';
import { ReasonAllegationService } from './reason-allegation.service';
import { Subject, takeUntil } from 'rxjs';
import { ReasonAllegationEndpointService } from './reason-allegation-endpoint.service';
import { VehiclesSelectedInfo } from '@app/shared/models';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';

@Component({
  selector: 'app-reason-allegation',
  templateUrl: './reason-allegation.component.html',
})
export class ReasonAllegationComponent implements OnInit {
  vehiclesInfo: Nullable<VehiclesSelectedInfo>;
  documentsSigedaDescriptions: ContestableActDocument[] = [];
  reasonsData: Reasons[] | undefined;
  idTramit: Nullable<string>;
  functionalModule = FunctionalModuleEnum;
  reasonsFormData: Nullable<ReasonsFormData>;

  get isConveniat(): boolean {
    return this.storageData.profileUser === IdentificationType.CONVENIAT;
  }

  get isRepresentant(): boolean {
    return this.storageData.profileUser === IdentificationType.REPRESENTATIVE;
  }

  get isCivilServant(): boolean {
    return this.storageData.profileUser === IdentificationType.CIVIL_SERVANT;
  }

  get isSelfPerson(): boolean {
    return this.storageData.profileUser === IdentificationType.NOM_PROPI;
  }

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    private storageData: StorageService,
    private reasonsAllegationEndpoints: ReasonAllegationEndpointService,
    private reasonAllegationService: ReasonAllegationService,
    private customRouter: CustomRouterService,
    private procedureHeaderService: ProceduresHeaderService,
  ) {}

  ngOnInit(): void {
    this.vehiclesInfo = this.storageData.getVehiclesSelected();
    this.idTramit = this.storageData.getIdTramit();

    this.procedureHeaderService.setupAlegationsHeader(
      this.vehiclesInfo?.matriculas,
    );

    if (this.vehiclesInfo) {
      this.setAllegationTramit();
    } else {
      this.onGoBackButtonClick();
    }
  }

  private setAllegationTramit(): void {
    if (!this.idTramit) {
      const atesaPhone = this.storageData.civilServantVehiclePhone;
      const atesaDate = this.storageData.civilServantVehicleDate;
      const atesaHour = this.storageData.civilServantVehicleHour;
      const request: RequestCreateAllegacioTramit = {
        idPerssCens: this.vehiclesInfo!.idPersTitular ?? '',
        tipusAcces: this.vehiclesInfo!.tipusAccess ?? '',
        exercici: this.vehiclesInfo!.exercici ?? '',
        matricules: this.vehiclesInfo!.matriculas ?? [],
        ...(atesaPhone && atesaDate && atesaHour
          ? {
              trucadaTelefonica: {
                numeroTelefon: atesaPhone,
                data: atesaDate,
                hora: atesaHour,
              },
            }
          : {}),
        nifRepresentant: this.storageData.civilServantVehicleNifRepresentant,
      };
      this.reasonsAllegationEndpoints
        .setAllegationTramit(request)
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((result) => {
          if (result.content) {
            this.storageData.setIdTramit(result.content.allegacioId);
            this.idTramit = result.content.allegacioId;
            this.getReasonsData(result.content.allegacioId);
          }
        });
    } else {
      this.getReasonsData(this.idTramit);
    }
  }

  private getReasonsData(allegacioId: string): void {
    const request = allegacioId;
    this.reasonsAllegationEndpoints
      .getReasons(request)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((result) => {
        if (result.content) {
          this.reasonsData = result.content.motius ?? [];
          this.documentsSigedaDescriptions = result.content.documents ?? [];
        }
      });
  }

  onAllegationsOptionsChange(data: ReasonsFormData): void {
    this.reasonsFormData = data;
  }

  onGoBackButtonClick(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
    this.storageData.clearReasonsSelected();
    this.storageData.clearIdTramit();
  }

  private updateAllegationTramit(request: RequestUpdateAllegacioTramit): void {
    this.reasonsAllegationEndpoints
      .updateAllegationTramit(request)
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((result: ResponseAllegacioTramit) => {
        if (result.content) {
          this.customRouter.navigateByBaseUrl(AppRoutes.RESUM_ALLEGATION);
        }
      });
  }

  onContinueButtonClick(): void {
    if (this.idTramit) {
      const request: RequestUpdateAllegacioTramit = {
        idAllegacio: this.idTramit,
        motius: [],
      };

      if (
        this.reasonsFormData?.reasonsFormValue &&
        this.reasonsFormData?.reasonsList
      ) {
        request.motius = this.reasonAllegationService.getRequestReasonsSelected(
          this.reasonsFormData.reasonsFormValue,
          this.reasonsFormData.reasonsList,
          this.reasonsFormData.reasonsDocuments,
        );
      }

      this.storageData.setReasonsSelected(
        this.reasonsFormData?.reasonsFormValue,
      );

      this.updateAllegationTramit(request);
    }
  }
}
