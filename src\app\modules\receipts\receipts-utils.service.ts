import { Injectable } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import {
  VehicleSelected,
  VehiclesSelectedInfo,
  YesOrNoOptionsEnum,
} from '@app/shared/models';
import { PaymentsService } from '@app/shared/services';
import { DeferralPaymentsService } from '@app/shared/services/deferral-payments/deferral-payments.service';
import { IdentificationType, TABLE_SORT_NAMES } from '@core/models';
import {
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { TranslateService } from '@ngx-translate/core';
import {
  BooleanToTextCellComponent,
  TextTagCellComponent,
} from '@shared/components';
import { RecursValidationsService } from '@shared/services/recurs-validations';
import {
  Column,
  Row,
  SeAlertMessage,
  SeAlertType,
  SeButton,
  SeButtonSizeEnum,
  SeButtonThemeEnum,
} from 'se-ui-components-mf-lib';
import { SeCheckbox } from 'se-ui-components-mf-lib/lib/components/checkbox/checkbox.model';
import { SeInput } from 'se-ui-components-mf-lib/lib/components/input/input.model';

export interface ReceiptsRangeFilter {
  fromInput: SeInput;
  toInput: SeInput;
  applyButton: SeButton;
  resetButton: SeButton;
}

@Injectable({
  providedIn: 'root',
})
export class ReceiptsUtilsService {
  private vehicleDomiciliedColumns: Column[] = [
    {
      header: 'SE_PADRO_CO2.LABELS.VEHICLE',
      tooltip: false,
      size: 20,
      tooltipText: 'SE_PADRO_CO2.LABELS.VEHICLE',
      key: 'carType',
      id: TABLE_SORT_NAMES.carType,
      cellComponentName: 'defaultCellComponent',
      resizable: true,
      sortable: true,
      cellConfig: {
        ellipsis: true,
        tooltip: false,
      },
    },
    {
      header: 'SE_PADRO_CO2.LABELS.DOMICILED',
      tooltip: false,
      size: 10,
      tooltipText: 'SE_PADRO_CO2.LABELS.DOMICILED',
      key: 'domiciled',
      id: TABLE_SORT_NAMES.domiciled,
      cellComponent: BooleanToTextCellComponent,
      resizable: true,
      sortable: true,
      cellConfig: {
        ellipsis: true,
        tooltip: true,
      },
    },
  ];

  private situationSharesColumns: Column[] = [
    {
      header: 'SE_PADRO_CO2.LABELS.SITUATION',
      tooltip: false,
      size: 18,
      tooltipText: 'SE_PADRO_CO2.LABELS.SITUATION',
      key: 'situation',
      id: TABLE_SORT_NAMES.situation,
      cellComponentName: 'tagCellComponent',
      resizable: true,
      sortable: true,
      cellConfig: {
        ellipsis: true,
        tooltip: false,
        tagCell: { tagTheme: 'secondary' },
      },
    },
    {
      cellComponentName: 'currencyCellComponent',
      header: 'SE_PADRO_CO2.LABELS.SHARES',
      tooltip: false,
      size: 10,
      tooltipText: 'SE_PADRO_CO2.LABELS.SHARES',
      key: 'shares',
      id: TABLE_SORT_NAMES.shares,
      resizable: true,
      sortable: true,
      cellConfig: {
        ellipsis: true,
        tooltip: true,
        currencyCode: 'EUR',
        display: 'symbol-narrow',
        digitsInfo: '1.2-2',
        align: 'right',
      },
    },
  ];

  private rangeFilter: ReceiptsRangeFilter = {
    fromInput: {
      label: 'SE_PADRO_CO2.LABELS.FROM',
      currencyMode: true,
      currencySymbol: '€',
      placeholder: '00,00 €',
    },
    toInput: {
      label: 'SE_PADRO_CO2.LABELS.TO',
      currencyMode: true,
      currencySymbol: '€',
      placeholder: '00,00 €',
    },
    applyButton: {
      size: SeButtonSizeEnum.DEFAULT,
      btnTheme: SeButtonThemeEnum.ONLY_TEXT,
      disabled: false,
      label: 'SE_COMPONENTS.DROPDOWN.RESULTS',
    },
    resetButton: {
      size: SeButtonSizeEnum.DEFAULT,
      btnTheme: SeButtonThemeEnum.ONLY_TEXT,
      disabled: false,
      label: 'SE_COMPONENTS.DROPDOWN.RESET',
    },
  };

  private applyButton: SeButton = {
    size: SeButtonSizeEnum.DEFAULT,
    btnTheme: SeButtonThemeEnum.ONLY_TEXT,
    disabled: false,
    label: this.translateService.instant('SE_COMPONENTS.DROPDOWN.RESULTS'),
  };

  private downloadButton: SeButton = {
    btnTheme: SeButtonThemeEnum.SECONDARY,
    disabled: false,
    icon: 'matFileDownloadOutline',
    iconSize: '20px',
    size: SeButtonSizeEnum.SMALL,
  };

  private resetButton: SeButton = {
    size: SeButtonSizeEnum.DEFAULT,
    btnTheme: SeButtonThemeEnum.ONLY_TEXT,
    disabled: false,
    label: this.translateService.instant('SE_COMPONENTS.DROPDOWN.RESET'),
  };

  private readonly OTHER_DROPDOWN_OPTIONS: SeButton = {
    icon: 'matKeyboardArrowDownOutline',
    iconPosition: 'right',
    iconSize: '1rem',
    disabled: true,
  };

  constructor(
    private translateService: TranslateService,
    private loginService: LoginResponseService,
    private recursService: RecursValidationsService,
    private paymentsService: PaymentsService,
    private deferralPaymentsService: DeferralPaymentsService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private storageService: StorageService,
  ) {}

  getCensusTableColumns(): Column[] {
    return [
      {
        header: 'SE_PADRO_CO2.LABELS.PLATE',
        tooltip: false,
        tooltipText: 'SE_PADRO_CO2.LABELS.PLATE',
        key: 'plate',
        id: TABLE_SORT_NAMES.plate,
        cellComponent: TextTagCellComponent,
        resizable: true,
        sortable: true,
        cellConfig: {
          ellipsis: true,
          tooltip: false,
          tagValue: this.translateService.instant('SE_PADRO_CO2.LABELS.NEW'),
          iconName: 'matInfoOutline',
          tagCell: { tagTheme: 'gray' },
          ngStyle: {
            cursor: 'pointer',
            color: 'var(--color-blue-500)',
            'text-decoration': 'underline',
          },
        },
      },
      ...this.vehicleDomiciliedColumns,
      ...this.situationSharesColumns,
      {
        header: 'SE_PADRO_CO2.LABELS.ACTIONS',
        tooltip: false,
        tooltipText: 'SE_PADRO_CO2.LABELS.ACTIONS',
        key: 'actions',
        id: 'actions',
        size: 16,
        resizable: true,
        cellComponentName: 'iconActionsCellComponent',
        cellConfig: {
          ellipsis: true,
          iconActions: {
            icons: [],
            button: {
              btnTheme: 'onlyText',
              icon: 'matMoreVertSharp',
              size: 'small',
            },
            buttonActions: [],
          },
        },
      },
    ];
  }

  getPreviousExercisesTableColumns(): Column[] {
    return [
      {
        header: 'SE_PADRO_CO2.LABELS.PLATE',
        tooltip: false,
        tooltipText: 'SE_PADRO_CO2.LABELS.PLATE',
        key: 'plate',
        id: TABLE_SORT_NAMES.plate,
        cellComponent: TextTagCellComponent,
        resizable: true,
        sortable: true,
        cellConfig: {
          ellipsis: true,
          tooltip: true,
          tagValue: this.translateService.instant('SE_PADRO_CO2.LABELS.NEW'),
          iconName: 'matInfoOutline',
          tagCell: { tagTheme: 'gray' },
          ngStyle: {
            cursor: 'pointer',
            color: 'var(--color-blue-500)',
            'text-decoration': 'underline',
          },
        },
      },
      ...this.vehicleDomiciliedColumns,
      {
        header: 'SE_PADRO_CO2.LABELS.EXERCISE',
        tooltip: false,
        tooltipText: 'SE_PADRO_CO2.LABELS.DOMICILED',
        key: 'exercise',
        id: TABLE_SORT_NAMES.exercise,
        cellComponentName: 'defaultCellComponent',
        resizable: true,
        sortable: true,
        size: 8,
        cellConfig: {
          ellipsis: true,
          tooltip: true,
        },
      },
      ...this.situationSharesColumns,
      {
        header: 'SE_PADRO_CO2.LABELS.ACTIONS',
        tooltip: false,
        tooltipText: 'SE_PADRO_CO2.LABELS.ACTIONS',
        key: 'actions',
        id: 'actions',
        resizable: true,
        cellComponentName: 'iconActionsCellComponent',
        cellConfig: {
          ellipsis: true,
          tooltip: true,
          iconActions: {
            icons: [],
            button: {
              btnTheme: 'onlyText',
              icon: 'matMoreVertSharp',
              size: 'small',
              ariaLabel: 'SE_PADRO_CO2.BUTTONS.MORE_ACTIONS',
            },
            buttonActions: [],
          },
        },
      },
    ];
  }

  getYesOrNoOptions(): SeCheckbox[] {
    // no se usa valores booleans porque cuando seleccionas las 2 opciones no carga correctamente
    return [
      {
        label: this.translateService.instant(
          'UI_COMPONENTS.SELECT_BUTTON_BINARY.YES',
        ),
        id: YesOrNoOptionsEnum.yes,
        value: YesOrNoOptionsEnum.yes,
      },
      {
        label: this.translateService.instant(
          'UI_COMPONENTS.SELECT_BUTTON_BINARY.NO',
        ),
        id: YesOrNoOptionsEnum.no,
        value: YesOrNoOptionsEnum.no,
      },
    ];
  }

  getOtherDropdownOptions(): SeButton {
    return this.OTHER_DROPDOWN_OPTIONS;
  }

  getCommonFormGroup(): FormGroup {
    return new FormGroup({
      plate: new FormControl('', Validators.maxLength(8)),
      rangeFilter: new FormControl({
        from: '',
        to: '',
      }),
      domiciled: new FormControl(''),
    });
  }

  getVehiclesSelectedInfoByVehicles(
    vehicles: VehicleSelected[],
    provisional: boolean,
    exercici: string,
  ): VehiclesSelectedInfo {
    return {
      provisional,
      vehicles,
      exercici,
      nifTitular: this.loginService.getNifTitular(),
      idPersTitular: this.loginService.user.idPersTitular as string,
      tipusAccess:
        this.loginService.user.tipusAccess || IdentificationType.NOM_PROPI,
    };
  }

  getVehiclesSelectedInfoByPlates(
    plates: string[],
    provisional: boolean,
    exercici: string,
  ): VehiclesSelectedInfo {
    return {
      provisional,
      matriculas: plates,
      exercici,
      nifTitular: this.loginService.getNifTitular(),
      idPersTitular: this.loginService.user.idPersTitular as string,
      tipusAccess:
        this.loginService.user.tipusAccess || IdentificationType.NOM_PROPI,
    };
  }

  getRangeFilterOptions(): ReceiptsRangeFilter {
    return this.rangeFilter;
  }

  getApplyButton(): SeButton {
    return this.applyButton;
  }

  getDownloadButton(): SeButton {
    return this.downloadButton;
  }

  getResetButton(): SeButton {
    return this.resetButton;
  }

  goToResurs(vehiclesInfo: VehiclesSelectedInfo): void {
    if (vehiclesInfo) {
      this.recursService.initRecurs({ vehiclesInfo });
      this.storageService.setReasonsSelected(null); // Clear reasons selected
    }
  }

  goToDeferralPayment(vehiclesInfo: VehiclesSelectedInfo): void {
    if (vehiclesInfo) {
      this.deferralPaymentsService.initDeferralPayment(vehiclesInfo);
    }
  }

  goToReas(vehiclesInfo: VehiclesSelectedInfo): void {
    if (vehiclesInfo) {
      this.recursService.initReas({ vehiclesInfo });
    }
  }

  getVehiclesSelectedByRows(rows: Row[]): VehicleSelected[] {
    return rows.map((row: Row) => ({
      matricula: row.data['plate']?.value,
      exercici:
        row.data['exercise']?.value ??
        this.specificConfigurationSrv.currentExercise,
      idDeute: row.data['idDeute']?.value,
      quota: Number(row.data['shares']?.value ?? 0),
    }));
  }

  getSelectedPlates(rows: Row[]): VehiclesSelectedInfo {
    const plates: string[] = rows.map((row: Row) => row.data['plate']?.value);
    return this.getVehiclesSelectedInfoByPlates(
      plates,
      this.specificConfigurationSrv.isProvisional,
      this.specificConfigurationSrv.currentExercise,
    );
  }

  getVehiclesAlert(
    isLoginSimpleOrConveniat: boolean,
    showSimpleLoginAlert: boolean,
  ): SeAlertMessage | undefined {
    if (!isLoginSimpleOrConveniat) {
      return {
        title: 'SE_PADRO_CO2.DETAIL.ALERTS.INFO_NOT_ALL_VEHICLES',
        subtitle: '',
        type: SeAlertType.INFO,
        list: [],
      };
    }

    if (showSimpleLoginAlert) {
      return {
        title: 'SE_PADRO_CO2.DETAIL.ALERTS.INFO_OTHER_VEHICLES',
        subtitle: 'SE_PADRO_CO2.DETAIL.ALERTS.INFO_OTHER_VEHICLES_2',
        type: SeAlertType.WARNING,
        list: [],
      };
    }

    return;
  }
}
