import { TestBed } from '@angular/core/testing';
import type { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { EMPTY, NEVER, of, Subject } from 'rxjs';

import { AppRoutes, IdentificationType } from '@app/core/models';
import { CustomRouterService, StorageService } from '@app/core/services';
import { ExecutivaAlertModalComponent } from '@app/shared/components/executiva-alert-modal/executiva-alert-modal.component';
import { TramitProcessType } from '@app/shared/models';
import type {
  VehicleRecurs,
  VehiclesSelectedInfo,
} from '@app/shared/models/vehicles.model';
import { SeModalOutputEvents, SeModalService } from 'se-ui-components-mf-lib';
import type {
  StartRecursRequest,
  SelectedVehiclesValidationResponse,
  SelectedVehiclesReasValidationResponse,
} from './recurs-validations-endpoints.model';
import { RecursValidationsEndpointsService } from './recurs-validations-endpoints.service';
import { RecursValidationsService } from './recurs-validations.service';
import { RecursValidationTramitType } from './recurs-validations.model';
import {
  DEFAULT_INIT_PROCEDURE_REQUEST,
  DEFAULT_MODAL_CANNOT_PROCESS_REQUEST,
  DEFAULT_MODAL_REF_COMPONENT_INSTANCE,
  DEFAULT_OPEN_CSV_MODAL_REQUEST,
  DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE,
  DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
  DEFAULT_START_RECURS_REQUEST,
  DEFAULT_VEHICLES_INFO,
  DEFAULT_VEHICLES_INFO_2_PLATES,
  DEFAULT_VEHICLES_RECURRIBLES,
  DEFAULT_WARNIG_MODAL_REQUEST,
  TEST_PREVI_REA_RESPONSE,
  TEST_PREVI_RECURS_RESPONSE,
  TEST_VEHICLES_ADVERTIR_TERMINI_RESPONSE,
  TEST_VEHICLES_FORA_TERMINI_RESPONSE,
} from './recurs-validations-test.model';

describe('RecursValidationsService', () => {
  let service: RecursValidationsService;
  let endpointsServiceSpy: jasmine.SpyObj<RecursValidationsEndpointsService>;
  let storageServiceSpy: jasmine.SpyObj<StorageService>;
  let modalServiceSpy: jasmine.SpyObj<SeModalService>;
  let customRouterSpy: jasmine.SpyObj<CustomRouterService>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      providers: [
        RecursValidationsService,
        {
          provide: RecursValidationsEndpointsService,
          useValue: jasmine.createSpyObj('RecursValidationsEndpointsService', [
            'setRecurs',
            'setReas',
            'civilServantVehiclePhone',
            'civilServantVehicleDate',
            'civilServantVehicleHour',
          ]),
        },
        {
          provide: StorageService,
          useValue: jasmine.createSpyObj('StorageService', [
            'getVehiclesSelected',
            'setIdTramit',
            'setRecursVehicles',
            'setReasVehicles',
            'setVehiclesSelected',
            'setReasonsSelected',
          ]),
        },
        {
          provide: SeModalService,
          useValue: jasmine.createSpyObj('SeModalService', ['openModal']),
        },
        {
          provide: CustomRouterService,
          useValue: jasmine.createSpyObj('CustomRouterService', [
            'navigateByBaseUrl',
          ]),
        },
      ],
    });

    service = TestBed.inject(RecursValidationsService);
    endpointsServiceSpy = TestBed.inject(
      RecursValidationsEndpointsService,
    ) as jasmine.SpyObj<RecursValidationsEndpointsService>;
    storageServiceSpy = TestBed.inject(
      StorageService,
    ) as jasmine.SpyObj<StorageService>;
    modalServiceSpy = TestBed.inject(
      SeModalService,
    ) as jasmine.SpyObj<SeModalService>;
    customRouterSpy = TestBed.inject(
      CustomRouterService,
    ) as jasmine.SpyObj<CustomRouterService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('initRecurs', () => {
    it('should store the vehicles info', () => {
      const vehiclesInfo = undefined;

      service.initRecurs({ vehiclesInfo });

      expect(storageServiceSpy.setVehiclesSelected).toHaveBeenCalledWith(
        vehiclesInfo,
      );
    });

    it('should not make a request to setRecurs if vehiclesInfo is not set', () => {
      const vehiclesInfo = undefined;

      service.initRecurs({ vehiclesInfo });

      expect(endpointsServiceSpy.setRecurs).toHaveBeenCalledTimes(0);
    });

    it('should make a request to setRecurs if vehiclesInfo is set', () => {
      endpointsServiceSpy.setRecurs.and.returnValue(EMPTY);

      service.initRecurs({ vehiclesInfo: {} as VehiclesSelectedInfo });

      expect(endpointsServiceSpy.setRecurs).toHaveBeenCalledTimes(1);
    });

    it('should not navigate to appeal process if the setRecurs call response is falsy', () => {
      endpointsServiceSpy.setRecurs.and.returnValue(of());

      service.initRecurs({ vehiclesInfo: {} as VehiclesSelectedInfo });

      expect(endpointsServiceSpy.setRecurs).toHaveBeenCalledTimes(1);
      expect(storageServiceSpy.setRecursVehicles).toHaveBeenCalledTimes(0);
      expect(storageServiceSpy.setIdTramit).toHaveBeenCalledTimes(0);
      expect(customRouterSpy.navigateByBaseUrl).toHaveBeenCalledTimes(0);
    });

    it('should navigate to appeal process if CSV and term validations are valid', () => {
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };
      const response: SelectedVehiclesValidationResponse = {
        ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      service.initRecurs({ vehiclesInfo });

      expect(storageServiceSpy.setIdTramit).toHaveBeenCalledOnceWith(
        response.recursId,
      );
      expect(customRouterSpy.navigateByBaseUrl).toHaveBeenCalledOnceWith(
        AppRoutes.APPEAL_FOR_RECONSIDERATION,
      );
    });
  });

  describe('initReas', () => {
    it('should store the vehicles info', () => {
      const vehiclesInfo = undefined;

      service.initReas({ vehiclesInfo });

      expect(storageServiceSpy.setVehiclesSelected).toHaveBeenCalledWith(
        vehiclesInfo,
      );
    });

    it('should not make a request to setReas if vehiclesInfo is not set', () => {
      const vehiclesInfo = undefined;

      service.initReas({ vehiclesInfo });

      expect(endpointsServiceSpy.setReas).toHaveBeenCalledTimes(0);
    });

    it('should make a request to setReas if vehiclesInfo is set', () => {
      endpointsServiceSpy.setReas.and.returnValue(EMPTY);

      service.initReas({ vehiclesInfo: {} as VehiclesSelectedInfo });

      expect(endpointsServiceSpy.setReas).toHaveBeenCalledTimes(1);
    });

    it('should not navigate to appeal process if the setReas call response is falsy', () => {
      endpointsServiceSpy.setReas.and.returnValue(of());

      service.initReas({ vehiclesInfo: {} as VehiclesSelectedInfo });

      expect(endpointsServiceSpy.setReas).toHaveBeenCalledTimes(1);
      expect(storageServiceSpy.setReasVehicles).toHaveBeenCalledTimes(0);
      expect(storageServiceSpy.setIdTramit).toHaveBeenCalledTimes(0);
      expect(customRouterSpy.navigateByBaseUrl).toHaveBeenCalledTimes(0);
    });

    it('should navigate to appeal process if CSV and term validations are valid', () => {
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };
      const response: SelectedVehiclesReasValidationResponse = {
        ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE,
      };
      endpointsServiceSpy.setReas.and.returnValue(of(response));

      service.initReas({ vehiclesInfo });

      expect(storageServiceSpy.setIdTramit).toHaveBeenCalledOnceWith(
        response.id,
      );
      expect(customRouterSpy.navigateByBaseUrl).toHaveBeenCalledOnceWith(
        AppRoutes.REAS,
      );
    });
  });

  describe('getRequestBody', () => {
    it('should create a request to setRecurs using vehiclesInfo', () => {
      endpointsServiceSpy.setRecurs.and.returnValue(EMPTY);
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };
      const request = {
        ...DEFAULT_START_RECURS_REQUEST,
      } as StartRecursRequest;

      service.initRecurs({ vehiclesInfo });

      expect(endpointsServiceSpy.setRecurs).toHaveBeenCalledWith(request);
    });

    it('should create a request to setRecurs with "trucadaTelefonica" if the contact was made throught a phone call', () => {
      endpointsServiceSpy.setRecurs.and.returnValue(EMPTY);
      storageServiceSpy.civilServantVehiclePhone = '123456789';
      storageServiceSpy.civilServantVehicleDate = '2025-05-08' as Date & string;
      storageServiceSpy.civilServantVehicleHour = '17:00' as Date & string;
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };
      const request = {
        ...DEFAULT_START_RECURS_REQUEST,
        trucadaTelefonica: {
          numeroTelefon: storageServiceSpy.civilServantVehiclePhone,
          data: storageServiceSpy.civilServantVehicleDate,
          hora: storageServiceSpy.civilServantVehicleHour,
        },
      } as StartRecursRequest;

      service.initRecurs({ vehiclesInfo });

      expect(endpointsServiceSpy.setRecurs).toHaveBeenCalledWith(request);
    });

    it('should make a request to setRecurs with "advertimentTerminiAcceptat", "nomesVehiclesValids" and "csvNotificacio" if provided', () => {
      endpointsServiceSpy.setRecurs.and.returnValue(EMPTY);
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };
      const advertimentTerminiAcceptat = true;
      const nomesVehiclesValids = true;
      const csvNotificacio = 'csv-test';
      const request = {
        ...DEFAULT_START_RECURS_REQUEST,
        advertimentTerminiAcceptat,
        nomesVehiclesValids,
        csvNotificacio,
      } as StartRecursRequest;

      service.initRecurs({
        vehiclesInfo,
        advertimentTerminiAcceptat,
        nomesVehiclesValids,
        csvNotificacio,
      });

      expect(endpointsServiceSpy.setRecurs).toHaveBeenCalledWith(request);
    });
  });

  describe('openCSVModal', () => {
    it('should open a modal with single title if "csvNotificacioNecessari" is true and one vehicle is selected and call initRecurs if proccedure is recurs', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesValidationResponse = {
        ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
        validation: {
          ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE.validation,
          vehiclesRecurribles: [
            { matricula: 'matricula-test-1' } as VehicleRecurs,
          ],
          csvNotificacioNecessari: true,
          allCsvNotificacio: true,
        },
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const expectedCSV = 'csv-test';
      const continueOutput = new Subject<string>();
      const modalRef = {
        close: (): void => {},
        componentInstance: { multiple: undefined, continueOutput },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      const initRecursSpy = spyOn(service, 'initRecurs');
      const initReasSpy = spyOn(service, 'initReas');
      continueOutput.next(expectedCSV);

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_OPEN_CSV_MODAL_REQUEST,
        title:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_CSV.SINGLE.TITLE',
      });
      expect(modalRef.componentInstance.multiple).toBeFalse();
      expect(initRecursSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        advertimentTerminiAcceptat: undefined,
        nomesVehiclesValids: false,
        csvNotificacio: expectedCSV,
      });
      expect(closeSpy).toHaveBeenCalledTimes(1);
      expect(initReasSpy).toHaveBeenCalledTimes(0);
    });

    it('should open a modal with single title if "csvNotificacioNecessari" is true and one vehicle is selected and call initReas if proccedure is reas', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesReasValidationResponse = {
        ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE,
        validation: {
          ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE.validation,
          vehiclesRecurribles: [
            { matricula: 'matricula-test-1' } as VehicleRecurs,
          ],
          csvNotificacioNecessari: true,
          allCsvNotificacio: true,
        },
      };
      endpointsServiceSpy.setReas.and.returnValue(of(response));

      const expectedCSV = 'csv-test';
      const continueOutput = new Subject<string>();
      const modalRef = {
        close: (): void => {},
        componentInstance: { multiple: undefined, continueOutput },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initReas({ vehiclesInfo });
      const initRecursSpy = spyOn(service, 'initRecurs');
      const initReasSpy = spyOn(service, 'initReas');
      continueOutput.next(expectedCSV);

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_OPEN_CSV_MODAL_REQUEST,
      });
      expect(modalRef.componentInstance.multiple).toBeFalse();
      expect(initReasSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        advertimentTerminiAcceptat: undefined,
        nomesVehiclesValids: false,
        csvNotificacio: expectedCSV,
      });
      expect(closeSpy).toHaveBeenCalledTimes(1);
      expect(initRecursSpy).toHaveBeenCalledTimes(0);
    });

    it('should open a modal with single title if "csvNotificacioNecessari" is true and more than one vehicle is selected but allCsvNotificacio is true and call initRecurs if proccedure is recurs', () => {
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesValidationResponse = {
        ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
        validation: {
          ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE.validation,
          vehiclesRecurribles: [{} as VehicleRecurs],
          csvNotificacioNecessari: true,
          allCsvNotificacio: true,
        },
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const expectedCSV = 'csv-test';
      const continueOutput = new Subject<string>();
      const modalRef = {
        close: (): void => {},
        componentInstance: { multiple: undefined, continueOutput },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      const initRecursSpy = spyOn(service, 'initRecurs');
      const initReasSpy = spyOn(service, 'initReas');
      continueOutput.next(expectedCSV);

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_OPEN_CSV_MODAL_REQUEST,
        title:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_CSV.SINGLE.TITLE',
      });
      expect(modalRef.componentInstance.multiple).toBeFalse();
      expect(initRecursSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        advertimentTerminiAcceptat: undefined,
        nomesVehiclesValids: false,
        csvNotificacio: expectedCSV,
      });
      expect(closeSpy).toHaveBeenCalledTimes(1);
      expect(initReasSpy).toHaveBeenCalledTimes(0);
    });

    it('should open a modal with single title if "csvNotificacioNecessari" is true and more than one vehicle is selected but allCsvNotificacio is true and call initReas if proccedure is reas', () => {
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesReasValidationResponse = {
        ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE,
        validation: {
          ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE.validation,
          vehiclesRecurribles: [{} as VehicleRecurs],
          csvNotificacioNecessari: true,
          allCsvNotificacio: true,
        },
      };
      endpointsServiceSpy.setReas.and.returnValue(of(response));

      const expectedCSV = 'csv-test';
      const continueOutput = new Subject<string>();
      const modalRef = {
        close: (): void => {},
        componentInstance: { multiple: undefined, continueOutput },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initReas({ vehiclesInfo });
      const initRecursSpy = spyOn(service, 'initRecurs');
      const initReasSpy = spyOn(service, 'initReas');
      continueOutput.next(expectedCSV);

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_OPEN_CSV_MODAL_REQUEST,
      });
      expect(modalRef.componentInstance.multiple).toBeFalse();
      expect(initReasSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        advertimentTerminiAcceptat: undefined,
        nomesVehiclesValids: false,
        csvNotificacio: expectedCSV,
      });
      expect(closeSpy).toHaveBeenCalledTimes(1);
      expect(initRecursSpy).toHaveBeenCalledTimes(0);
    });

    it('should open a modal with multiple title if "csvNotificacioNecessari" is true and more than one vehicle is selected and call initRecurs if proccedure is recurs', () => {
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesValidationResponse = {
        ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
        validation: {
          ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE.validation,
          vehiclesRecurribles: [{} as VehicleRecurs],
          csvNotificacioNecessari: true,
        },
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const expectedCSV = 'csv-test';
      const continueOutput = new Subject<string>();
      const modalRef = {
        close: (): void => {},
        componentInstance: { multiple: undefined, continueOutput },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      const initRecursSpy = spyOn(service, 'initRecurs');
      const initReasSpy = spyOn(service, 'initReas');
      continueOutput.next(expectedCSV);

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_OPEN_CSV_MODAL_REQUEST,
        title:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_CSV.MULTIPLE.TITLE',
      });
      expect(modalRef.componentInstance.multiple).toBeTrue();
      expect(initRecursSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        advertimentTerminiAcceptat: undefined,
        nomesVehiclesValids: false,
        csvNotificacio: expectedCSV,
      });
      expect(closeSpy).toHaveBeenCalledTimes(1);
      expect(initReasSpy).toHaveBeenCalledTimes(0);
    });

    it('should open a modal with multiple title if "csvNotificacioNecessari" is true and more than one vehicle is selected and call initReas if proccedure is reas', () => {
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesReasValidationResponse = {
        ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE,
        validation: {
          ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE.validation,
          vehiclesRecurribles: [{} as VehicleRecurs],
          csvNotificacioNecessari: true,
        },
      };
      endpointsServiceSpy.setReas.and.returnValue(of(response));

      const expectedCSV = 'csv-test';
      const continueOutput = new Subject<string>();
      const modalRef = {
        close: (): void => {},
        componentInstance: { multiple: undefined, continueOutput },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initReas({ vehiclesInfo });
      const initRecursSpy = spyOn(service, 'initRecurs');
      const initReasSpy = spyOn(service, 'initReas');
      continueOutput.next(expectedCSV);

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_OPEN_CSV_MODAL_REQUEST,
        title:
          'SE_PADRO_CO2.REAS_PROCESS.VALIDATIONS_MODALS.MODAL_CSV.MULTIPLE.TITLE',
      });
      expect(modalRef.componentInstance.multiple).toBeTrue();
      expect(initReasSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        advertimentTerminiAcceptat: undefined,
        nomesVehiclesValids: false,
        csvNotificacio: expectedCSV,
      });
      expect(closeSpy).toHaveBeenCalledTimes(1);
      expect(initRecursSpy).toHaveBeenCalledTimes(0);
    });
  });

  describe('handleVehicleValidation', () => {
    it('should open a "Vehicles Valids Alert" modal and call initRecurs if there are vehicles out of term and there are appealable vehicles; and it is recurs proccedure', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const vehiclesRecurribles: VehicleRecurs[] = [
        ...DEFAULT_VEHICLES_RECURRIBLES,
      ];
      const response: SelectedVehiclesValidationResponse = {
        ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
        validation: {
          ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE.validation,
          vehiclesRecurribles,
          vehiclesForaTermini: true,
        },
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const continueOutput = new Subject<void>();
      const modalRef = {
        componentInstance: {
          ...DEFAULT_MODAL_REF_COMPONENT_INSTANCE,
          continueOutput,
        },
      } as NgbModalRef;
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      const initRecursSpy = spyOn(service, 'initRecurs');
      const initReasSpy = spyOn(service, 'initReas');
      continueOutput.next();

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_WARNIG_MODAL_REQUEST,
        size: 'xl',
        title:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_VEHICLES_FORA_TERMINI.TITLE',
        closable: response.validation.vehiclesRecurribles.length > 0,
        component: ExecutivaAlertModalComponent,
      });
      expect(modalRef.componentInstance.vehicles).toEqual(vehiclesRecurribles);
      expect(modalRef.componentInstance.message).toEqual(
        'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_VEHICLES_FORA_TERMINI.MESSAGE',
      );
      expect(modalRef.componentInstance.tramitProcess).toEqual(
        TramitProcessType.RECURS,
      );
      expect(storageServiceSpy.getVehiclesSelected).toHaveBeenCalledTimes(1);
      expect(initReasSpy).toHaveBeenCalledTimes(0);
      expect(initRecursSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        ...DEFAULT_INIT_PROCEDURE_REQUEST,
        nomesVehiclesValids: true,
      });
    });

    it('should open a "Vehicles Valids Alert" modal and call initReas if there are vehicles out of term and there are appealable vehicles; and it is reas proccedure', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const vehiclesRecurribles: VehicleRecurs[] = [
        ...DEFAULT_VEHICLES_RECURRIBLES,
      ];
      const response: SelectedVehiclesReasValidationResponse = {
        ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE,
        validation: {
          ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE.validation,
          vehiclesRecurribles,
          vehiclesForaTermini: true,
        },
      };
      endpointsServiceSpy.setReas.and.returnValue(of(response));

      const continueOutput = new Subject<void>();
      const modalRef = {
        componentInstance: {
          ...DEFAULT_MODAL_REF_COMPONENT_INSTANCE,
          continueOutput,
        },
      } as NgbModalRef;
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initReas({ vehiclesInfo });
      const initRecursSpy = spyOn(service, 'initRecurs');
      const initReasSpy = spyOn(service, 'initReas');
      continueOutput.next();

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_WARNIG_MODAL_REQUEST,
        size: 'xl',
        title:
          'SE_PADRO_CO2.REAS_PROCESS.VALIDATIONS_MODALS.MODAL_VEHICLES_FORA_TERMINI.TITLE',
        closable: response.validation.vehiclesRecurribles.length > 0,
        component: ExecutivaAlertModalComponent,
      });
      expect(modalRef.componentInstance.vehicles).toEqual(vehiclesRecurribles);
      expect(modalRef.componentInstance.message).toEqual(
        'SE_PADRO_CO2.REAS_PROCESS.VALIDATIONS_MODALS.MODAL_VEHICLES_FORA_TERMINI.MESSAGE',
      );
      expect(modalRef.componentInstance.tramitProcess).toEqual(
        RecursValidationTramitType.REAS,
      );
      expect(storageServiceSpy.getVehiclesSelected).toHaveBeenCalledTimes(1);
      expect(initRecursSpy).toHaveBeenCalledTimes(0);
      expect(initReasSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        ...DEFAULT_INIT_PROCEDURE_REQUEST,
        nomesVehiclesValids: true,
      });
    });

    it('should open a "Cannot Process" modal with single title if one vehicle selected and there aren\'t appealable vehicles', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_VEHICLES_FORA_TERMINI_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalRef = {
        componentInstance: {
          ...DEFAULT_MODAL_REF_COMPONENT_INSTANCE,
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_MODAL_CANNOT_PROCESS_REQUEST,
      });
    });

    it('should open a "Cannot Process" modal with multiple title if more than one vehicle selected and there aren\'t appealable vehicles', () => {
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_VEHICLES_FORA_TERMINI_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalRef = {
        componentInstance: {
          ...DEFAULT_MODAL_REF_COMPONENT_INSTANCE,
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_MODAL_CANNOT_PROCESS_REQUEST,
        subtitle:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_CANNOT_PROCESS.MULTIPLE.INFO',
      });
    });

    it('should the "Cannot Process" modal be closed if "modalOutputEvent" is fired', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_VEHICLES_FORA_TERMINI_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalOutputEvent = new Subject<void>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          ...DEFAULT_MODAL_REF_COMPONENT_INSTANCE,
          modalOutputEvent,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      modalOutputEvent.next();

      expect(closeSpy).toHaveBeenCalledTimes(1);
    });

    it('should the "Cannot Process" modal be closed if "modalSecondaryButtonEvent" is fired', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_VEHICLES_FORA_TERMINI_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalSecondaryButtonEvent = new Subject<void>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          ...DEFAULT_MODAL_REF_COMPONENT_INSTANCE,
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      modalSecondaryButtonEvent.next();

      expect(closeSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('openTerminiAlertModal', () => {
    it('should open "Termini Alert" modal with single title if "vehiclesAdvertirTermini" is true and one vehicle is selected', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_VEHICLES_ADVERTIR_TERMINI_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalRef = {
        componentInstance: {
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_WARNIG_MODAL_REQUEST,
        title:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_FORA_TERMINI.TITLE',
        subtitle:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_FORA_TERMINI.SINGLE.INFO',
      });
    });

    it('should open "Termini Alert" modal with multiple title if "vehiclesAdvertirTermini" is true and more than one vehicle are selected', () => {
      const vehiclesInfo: VehiclesSelectedInfo = {
        ...DEFAULT_VEHICLES_INFO_2_PLATES,
      };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_VEHICLES_ADVERTIR_TERMINI_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalRef = {
        componentInstance: {
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_WARNIG_MODAL_REQUEST,
        title:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_FORA_TERMINI.TITLE',
        subtitle:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_FORA_TERMINI.MULTIPLE.INFO',
      });
    });

    it('should call "initRecurs" with "advertimentTerminiAcceptat === true" if user select the main action of the "Termini Alert" modal from recurs procedure', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_VEHICLES_ADVERTIR_TERMINI_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalOutputEvent = new Subject<SeModalOutputEvents>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      const initRecursSpy = spyOn(service, 'initRecurs');
      const initReasSpy = spyOn(service, 'initReas');
      modalOutputEvent.next(SeModalOutputEvents.MAIN_ACTION);

      expect(storageServiceSpy.getVehiclesSelected).toHaveBeenCalledTimes(1);
      expect(initReasSpy).toHaveBeenCalledTimes(0);
      expect(initRecursSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        ...DEFAULT_INIT_PROCEDURE_REQUEST,
        advertimentTerminiAcceptat: true,
      });
      expect(closeSpy).toHaveBeenCalledTimes(1);
    });

    it('should call "initReas" with "advertimentTerminiAcceptat === true" if user select the main action of the "Termini Alert" modal from reas procedure', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesReasValidationResponse = {
        ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE,
        validation: {
          ...DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE.validation,
          vehiclesAdvertirTermini: true,
        },
      };
      endpointsServiceSpy.setReas.and.returnValue(of(response));

      const modalOutputEvent = new Subject<SeModalOutputEvents>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initReas({ vehiclesInfo });
      const initReasSpy = spyOn(service, 'initReas');
      const initRecursSpy = spyOn(service, 'initRecurs');
      modalOutputEvent.next(SeModalOutputEvents.MAIN_ACTION);

      expect(storageServiceSpy.getVehiclesSelected).toHaveBeenCalledTimes(1);
      expect(initRecursSpy).toHaveBeenCalledTimes(0);
      expect(initReasSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        ...DEFAULT_INIT_PROCEDURE_REQUEST,
        advertimentTerminiAcceptat: true,
      });
      expect(closeSpy).toHaveBeenCalledTimes(1);
    });

    it('should close the "Termini Alert" modal if "modalOutputEvent" emitted with a return value different than "MAIN_ACTION"', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_VEHICLES_ADVERTIR_TERMINI_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalOutputEvent = new Subject<SeModalOutputEvents>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      modalOutputEvent.next(SeModalOutputEvents.CLOSE);

      expect(closeSpy).toHaveBeenCalledTimes(1);
    });

    it('should close the "Termini Alert" modal if "modalSecondaryButtonEvent" emits', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_VEHICLES_ADVERTIR_TERMINI_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalSecondaryButtonEvent = new Subject<void>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      modalSecondaryButtonEvent.next();

      expect(closeSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('openPreviRecursModal', () => {
    it('should open "Previ recurs" modal if "previRecurs" is true and one vehicle is selected', () => {
      storageServiceSpy.profileUser = IdentificationType.NOM_PROPI;

      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_PREVI_RECURS_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalRef = {
        componentInstance: {
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        ...DEFAULT_WARNIG_MODAL_REQUEST,
        title:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_PREVI_RECURS.NOM_PROPI.TITLE',
        subtitle:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_PREVI_RECURS.NOM_PROPI.MESSAGE',
      });
    });

    it('should call "goToRecursProcess" if user select the main action of the "Previ recurs" modal and user is nomPropi or tramit is recurs', () => {
      storageServiceSpy.profileUser = IdentificationType.NOM_PROPI;

      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_PREVI_RECURS_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalOutputEvent = new Subject<SeModalOutputEvents>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });

      spyOn(service, <never>'goToRecursProcess');

      modalOutputEvent.next(SeModalOutputEvents.MAIN_ACTION);

      expect(service['goToRecursProcess']).toHaveBeenCalledOnceWith(
        response,
        false,
      );
      expect(closeSpy).toHaveBeenCalledTimes(1);
    });

    it('should close the "Previ recurs" modal if "modalOutputEvent" emitted with a return value different than "MAIN_ACTION"', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_PREVI_RECURS_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalOutputEvent = new Subject<SeModalOutputEvents>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      modalOutputEvent.next(SeModalOutputEvents.CLOSE);

      expect(closeSpy).toHaveBeenCalledTimes(1);
    });

    it('should close the "Previ recurs" modal if "modalSecondaryButtonEvent" emits', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_PREVI_RECURS_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalSecondaryButtonEvent = new Subject<void>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      modalSecondaryButtonEvent.next();

      expect(closeSpy).toHaveBeenCalledTimes(1);
    });
  });

  describe('openPreviReasModal', () => {
    it('should open "Previ reas" modal if "previReas" is true and one vehicle is selected', () => {
      storageServiceSpy.profileUser = IdentificationType.NOM_PROPI;

      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_PREVI_REA_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalRef = {
        componentInstance: {
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });

      expect(modalServiceSpy.openModal).toHaveBeenCalledOnceWith({
        severity: 'warning',
        title:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_PREVI_REAS.NOM_PROPI.TITLE',
        subtitle:
          'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_PREVI_REAS.NOM_PROPI.MESSAGE',
        closable: true,
        closableLabel: 'SE_PADRO_CO2.BUTTONS.SUBMIT_REAS',
        secondaryButton: true,
        secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
      });
    });

    it('should call "initReas" if user select the main action of the "Previ reas" modal and user is nomPropi or tramit is reas', () => {
      storageServiceSpy.profileUser = IdentificationType.NOM_PROPI;

      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };
      storageServiceSpy.getVehiclesSelected.and.returnValue(vehiclesInfo);

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_PREVI_REA_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalOutputEvent = new Subject<SeModalOutputEvents>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });

      const initReasSpy = spyOn(service, 'initReas');

      modalOutputEvent.next(SeModalOutputEvents.MAIN_ACTION);

      expect(initReasSpy).toHaveBeenCalledOnceWith({
        vehiclesInfo,
        ...DEFAULT_INIT_PROCEDURE_REQUEST,
        skipPreviReasValidation: true,
      });
      expect(closeSpy).toHaveBeenCalledTimes(1);
    });

    it('should close the "Previ reas" modal if "modalOutputEvent" emitted with a return value different than "MAIN_ACTION"', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_PREVI_REA_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalOutputEvent = new Subject<SeModalOutputEvents>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent,
          modalSecondaryButtonEvent: NEVER,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      modalOutputEvent.next(SeModalOutputEvents.CLOSE);

      expect(closeSpy).toHaveBeenCalledTimes(1);
    });

    it('should close the "Previ reas" modal if "modalSecondaryButtonEvent" emits', () => {
      const vehiclesInfo: VehiclesSelectedInfo = { ...DEFAULT_VEHICLES_INFO };

      const response: SelectedVehiclesValidationResponse = {
        ...TEST_PREVI_REA_RESPONSE,
      };
      endpointsServiceSpy.setRecurs.and.returnValue(of(response));

      const modalSecondaryButtonEvent = new Subject<void>();
      const modalRef = {
        close: () => {},
        componentInstance: {
          modalOutputEvent: NEVER,
          modalSecondaryButtonEvent,
        },
      } as NgbModalRef;
      const closeSpy = spyOn(modalRef, 'close');
      modalServiceSpy.openModal.and.returnValue(modalRef);

      service.initRecurs({ vehiclesInfo });
      modalSecondaryButtonEvent.next();

      expect(closeSpy).toHaveBeenCalledTimes(1);
    });
  });
});
