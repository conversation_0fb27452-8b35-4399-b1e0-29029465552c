/* eslint-disable @typescript-eslint/no-explicit-any */
import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DeferralPaymentsComponent } from './deferral-payments.component';
import { TranslateModule } from '@ngx-translate/core';
import { CustomRouterService, StorageService } from '@app/core/services';
import {
  DeferralPaymentsService,
  ProceduresHeaderService,
} from '@app/shared/services';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  SeButtonDropdownModule,
  SeButtonModule,
  SeInputModule,
  SeTableModule,
  SeEmptyStateModule,
  SePanelModule,
} from 'se-ui-components-mf-lib';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { CivilServantRequiredDocBlockModule } from '@app/shared/components/civil-servant-required-doc-block';

describe('DeferralPaymentsComponent', () => {
  let component: DeferralPaymentsComponent;
  let fixture: ComponentFixture<DeferralPaymentsComponent>;

  // let storageServiceSpy: jasmine.SpyObj<StorageService>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [DeferralPaymentsComponent],
      imports: [
        BrowserAnimationsModule,
        HttpClientTestingModule,
        SeButtonDropdownModule,
        SeButtonModule,
        SePanelModule,
        SeInputModule,
        SeTableModule,
        SeEmptyStateModule,
        FormsModule,
        ReactiveFormsModule,
        TranslateModule.forRoot(),
        CivilServantRequiredDocBlockModule,
      ],
      providers: [
        DeferralPaymentsService,
        {
          provide: StorageService,
          useValue: {
            getDeferralPaymentVehicles: jasmine
              .createSpy('getDeferralPaymentVehicles')
              .and.returnValue({
                total: 0,
                vehicles: [],
              }),
            clearVehiclesToBeRegistered: jasmine.createSpy(
              'clearVehiclesToBeRegistered',
            ),
          },
        },
        {
          provide: ProceduresHeaderService,
          useValue: jasmine.createSpyObj('ProceduresHeaderService', [
            'setupDeferralPaymentsHeader',
            'getProcedureHeader',
            'clearProcedureHeader',
          ]),
        },
        {
          provide: CustomRouterService,
          useValue: {
            navigateByBaseUrl: jasmine.createSpy('navigateByBaseUrl'),
          },
        },
      ],
    });
    fixture = TestBed.createComponent(DeferralPaymentsComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();

    // storageServiceSpy = TestBed.inject(StorageService) as jasmine.SpyObj<StorageService>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  describe('onDisableContinueButton', () => {
    it('should set disableContinueButton to true when called with true', () => {
      (component as any).onDisableContinueButton(true);
      expect(component['disableContinueButton']).toBeTrue();
    });

    it('should set disableContinueButton to false when called with false', () => {
      (component as any).onDisableContinueButton(false);
      expect(component['disableContinueButton']).toBeFalse();
    });
  });
});
