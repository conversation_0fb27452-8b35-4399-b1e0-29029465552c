import { Injectable, OnDestroy } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Column, Row, SeDocumentsService, SeDropdownOption, iDocumentPadoct } from 'se-ui-components-mf-lib';
import { RequestGetDocuments, ResponseGetDocuments, SigedaDocumentsDescription } from '../models/end-point.model';
import { EndPointService } from './end-points.service';
import { Subject, take } from 'rxjs';
import { lastValueFrom } from 'rxjs/internal/lastValueFrom'
@Injectable({
  providedIn: 'root'
})
export class DocsService {

  constructor(
    private translateService: TranslateService,
    private endpointService: EndPointService,
    private docsService: SeDocumentsService
  ) { }


  async getDocuments(request: RequestGetDocuments): Promise<ResponseGetDocuments> {
    return await lastValueFrom(this.endpointService.getDocuments(request).pipe(
      take(1)))
  }

  getSigedaList(sigedaType: string, tranlsations?: {[key:string]: string},): SeDropdownOption[] {
    return [
      { 
        id: sigedaType, 
        label: tranlsations ? tranlsations[sigedaType] : this.translateService.instant(`SE_DOCUMENTS_MF.SIGEDA_TYPES.${sigedaType}`) 
      }
    ]
  }

  setRow(foundDocument: SigedaDocumentsDescription, doc?: iDocumentPadoct, translations?: {[key:string]: string}): Row {
    return {
      data: {
        supplementaryDoc: { value: foundDocument?.description || this.translateService.instant(`SE_DOCUMENTS_MF.SIGEDA_TYPES.${foundDocument?.type}`) },
        description: { value: doc?.description ?? '' },
        docType: { value: this.getDocTypeTranslation(doc?.codSigedaType,translations) },
        providedDoc: { value: doc?.nom ?? '' },
        id: { value: doc?.id ?? '' },
        sigedaType: { value: foundDocument?.type ?? '' },
        subtype: { value: foundDocument?.subtype ?? '' },
      }
    }
  }
  
  private getDocTypeTranslation(docType?:string, translations?: {[key:string]: string}): string {
    if(!docType) return '';
    
    return translations?.[docType] || this.translateService.instant(`SE_DOCUMENTS_MF.SIGEDA_TYPES.${docType}`);
  }

  getTableColumns(data: any): Column[] {
    let columns = []
    if (data?.columns) {
      columns = data.columns
    } else {
      columns = [
        {
          header: 'SE_DOCUMENTS_MF.DOC_TABLE.TABLE_COLUMNS.SUPPLEMENTARY_DOCUMENT',
          key: 'supplementaryDoc',
          cellComponentName: 'defaultCellComponent',
          resizable: true,
          size: 60,
        },
        {
          header: 'SE_DOCUMENTS_MF.DOC_TABLE.TABLE_COLUMNS.DESCRIPTION',
          key: 'description',
          cellComponentName: 'defaultCellComponent',
          resizable: true,
          size: 30
        },
        {
          header: 'SE_DOCUMENTS_MF.DOC_TABLE.TABLE_COLUMNS.DOCUMENT_TYPE',
          key: 'docType',
          cellComponentName: 'defaultCellComponent',
          resizable: true,
          size: 40
        },
        {
          header: 'SE_DOCUMENTS_MF.DOC_TABLE.TABLE_COLUMNS.PROVIDED_DOCUMENTATION',
          key: 'providedDoc',
          cellComponentName: 'defaultCellComponent',
          resizable: true,
          size: 40
        }
      ]
    }
    return columns
  }

  downloadDocument(documentIds: string[], customFileName: string): void {
    this.docsService.downloadDocument(documentIds, customFileName);
  }
}
