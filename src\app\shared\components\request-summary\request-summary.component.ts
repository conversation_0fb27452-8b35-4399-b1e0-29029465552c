import { Component, Input } from '@angular/core';
import { Column, Row, SeModalService } from 'se-ui-components-mf-lib';

import { SpecificConfigurationService } from '@core/services';
import { ModalVehiclesComponent } from '../modal-vehicles/modal-vehicles.component';
import { RequestSummary } from './request-summary.model';
import { FormControl, FormGroup } from '@angular/forms';

@Component({
  selector: 'app-request-summary',
  templateUrl: './request-summary.component.html',
  styles: [
    `
      .overflow-ellipsis {
        text-overflow: ellipsis;
        overflow: hidden;
      }
    `,
  ],
})
export class RequestSummaryComponent {
  @Input() title = '';
  @Input() modalTitle = 'SE_PADRO_CO2.MODAL.VEHICLES.TITLE_ALLEGACIONS';
  @Input() reasonColumns: Column[] = [];
  @Input() reasonRows: Row[] = [];
  @Input() documentColumns: Column[] = [];
  @Input() documentRows: Row[] = [];
  @Input() reasonLabel: string = 'SE_PADRO_CO2.LABELS.REASON_ALLEGATION';
  @Input() descriptionLabel: string = 'SE_PADRO_CO2.LABELS.ALEGATIONS';
  @Input() documentLabel: string = 'SE_PADRO_CO2.LABELS.DOCUMENTS';
  @Input() data: RequestSummary | undefined;
  @Input() isAllegationsTramit: boolean = false;
  @Input() showPDFInfo: boolean = true;
  @Input() hideVehiclesModalExerciceColumn: boolean = false;
  @Input() footerMessage = 'SE_PADRO_CO2.ALLEGATIONS.INFO';

  protected exercici = this.specificConfigSrv.currentExercise;

  summaryForm: FormGroup = new FormGroup({
    allegationsDr: new FormControl(true),
  });

  constructor(
    private modalSrv: SeModalService,
    private specificConfigSrv: SpecificConfigurationService,
  ) {}

  openVehiclesDetail(): void {
    if (
      this.data?.vehicles &&
      this.data?.vehicles?.matriculas &&
      this.data?.vehicles?.matriculas?.length > 1
    ) {
      const component: ModalVehiclesComponent = this.modalSrv.openModal({
        title: this.modalTitle,
        severity: 'info',
        closable: true,
        closableLabel: 'SE_PADRO_CO2.BUTTONS.OK',
        hideIcon: true,
        size: 'xl',
        secondaryButton: false,
        component: ModalVehiclesComponent,
      }).componentInstance;
      component.vehiclesPlates = this.data.vehicles;
      component.hideExerciceColumn = this.hideVehiclesModalExerciceColumn;
    }
  }
}
