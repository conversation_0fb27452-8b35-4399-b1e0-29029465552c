import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import { SeD<PERSON>downModule, SeModalModule } from 'se-ui-components-mf-lib';
import { HelpModalComponent } from './help-modal.component';

@NgModule({
  declarations: [HelpModalComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    SeModalModule,
    SeDropdownModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class HelpModalModule {}
