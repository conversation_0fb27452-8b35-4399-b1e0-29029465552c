import { Injectable } from '@angular/core';
import {
  SpecificConfigurationService,
  StorageService,
} from '@app/core/services';
import { TranslateService } from '@ngx-translate/core';
import { Observable, take, of } from 'rxjs';
import { SeModalService } from 'se-ui-components-mf-lib';
import { ModificationBankDetailsModalComponent } from './modification-bank-details-modal.component';

@Injectable({
  providedIn: 'root',
})
export class ModificationBankDetailsService {
  constructor(
    private storageService: StorageService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private modalService: SeModalService,
    private translateService: TranslateService,
  ) {}

  checkAlertModalModificationBankDetails(): Observable<boolean> {
    const currentVehicles = this.storageService.vehiclesToBeUpdated.filter(
      (vehicle) =>
        vehicle.exerciciDomiciliacio &&
        vehicle.exerciciDomiciliacio <=
          this.specificConfigurationSrv.currentExercise,
    );
    const nextVehicles = this.storageService.vehiclesToBeUpdated.filter(
      (vehicle) =>
        vehicle.exerciciDomiciliacio &&
        vehicle.exerciciDomiciliacio >
          this.specificConfigurationSrv.currentExercise,
    );

    if (currentVehicles.length > 0 && nextVehicles.length > 0) {
      //OPEN MODAL

      const modalRef = this.modalService.openModal({
        severity: 'warning',
        title: this.translateService.instant(
          'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.BANK_DETAILS_MODAL.TITLE',
        ),
        subtitle: this.translateService.instant(
          'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.BANK_DETAILS_MODAL.SUBTITLE',
        ),
        closable: true,
        closableLabel: 'UI_COMPONENTS.BUTTONS.CONTINUE',
        secondaryButton: true,
        secondaryButtonLabel: 'UI_COMPONENTS.BUTTONS.CANCEL',
        component: ModificationBankDetailsModalComponent,
      });

      modalRef.componentInstance.nextVehicles = nextVehicles;
      modalRef.componentInstance.currentVehicles = currentVehicles;

      return modalRef.componentInstance.continueOutput.pipe(take(1));
    }

    return of(true);
  }
}
