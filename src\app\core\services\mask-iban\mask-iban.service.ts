import { Injectable } from '@angular/core';
import { Nullable } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class MaskIbanService {
  transform(value: Nullable<string>, nullValue?: string): string {
    if (!value) return nullValue ?? '-';

    const iban = value
      .trim()
      ?.replace(/\s/g, '')
      .replace(/^(.{8})(.{12})(.{4})$/, '$1**********$3');
    return iban;
  }
}
