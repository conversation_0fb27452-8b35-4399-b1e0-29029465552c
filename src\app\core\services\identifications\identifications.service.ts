import { Injectable, OnDestroy } from '@angular/core';
import {
  forkJoin,
  map,
  Observable,
  ReplaySubject,
  Subject,
  takeUntil,
  tap,
} from 'rxjs';
import { IdentificationsEndpointsService } from './identifications-endpoints.service';
import {
  LoginRequest,
  IdentificationType,
  LoginResponse,
} from '@app/core/models';
import { LoginResponseService } from '../login-response';
import {
  CO2_AGREEMENT_ID,
  CO2_TAX_ID,
  GetRespresentativeMapResponse,
  RepresentativesData,
} from './identifications.model';
import { CustomRouterService } from '../custom-navigate';
import { StorageService } from '../storage';
import { SeAuthService, SeUser } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class IdentificationsService implements OnDestroy {
  readonly isConveniat$ = new ReplaySubject<boolean>(1);

  private _isConveniat: boolean = false;

  get isConveniat(): boolean {
    return this._isConveniat;
  }

  set isConveniat(value: boolean) {
    this._isConveniat = value;
    this.isConveniat$.next(value);
  }

  private _isCoordinator: boolean = false;

  get isCoordinator(): boolean {
    return this._isCoordinator;
  }

  set isCoordinator(value: boolean) {
    this._isCoordinator = value;
  }

  private _isCivilServant: boolean = false;

  get isCivilServant(): boolean {
    return this._isCivilServant;
  }

  set isCivilServant(value: boolean) {
    this._isCivilServant = value;
  }

  isRepresentative$ = new ReplaySubject<boolean>(1);
  representativeData$ = new ReplaySubject<RepresentativesData[]>();

  private _isRepresentative: boolean = false;

  get isRepresentative(): boolean {
    return this._isRepresentative;
  }

  set isRepresentative(value: GetRespresentativeMapResponse) {
    this._isRepresentative = value.isRepresentative;
    this.isRepresentative$.next(value.isRepresentative);
    this.representativeData$.next(value.representativesData);
    this.storageSrv.listRepresentatives = value.representativesData;
  }

  private _unsubscribe: Subject<void> = new Subject();
  private _isAocAtesaCoordinador: boolean = false;

  constructor(
    private identificationsEndpointsService: IdentificationsEndpointsService,
    private loginResponseSrv: LoginResponseService,
    private customRouter: CustomRouterService,
    private storageSrv: StorageService,
    private authservice: SeAuthService,
  ) {
    const user: SeUser = this.authservice.getSessionStorageUser();
    this._isAocAtesaCoordinador = !!(
      user?.esLoginAOC &&
      (user?.esCoordinador || user?.esAtesa)
    );
  }

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }

  loadAgreementStatus(): void {
    this.identificationsEndpointsService
      .userHasAgreementDifferentFromAtesarOrCoordinator(
        CO2_AGREEMENT_ID,
        this._isAocAtesaCoordinador,
      )
      .subscribe((response) => {
        this.isConveniat = response;
      });
  }

  loadRepresentativeStatus(): void {
    forkJoin({
      generalRepresentative:
        this.identificationsEndpointsService.verifyIsRepresentative(
          this._isAocAtesaCoordinador,
        ),
      co2Representative:
        this.identificationsEndpointsService.verifyIsRepresentativeByPorcedure(
          CO2_TAX_ID,
          this._isAocAtesaCoordinador,
        ),
    })
      .pipe(takeUntil(this._unsubscribe))
      .subscribe((response) => {
        const generalResponseData =
          response?.generalRepresentative?.representativesData || [];
        const co2TaxResponseData =
          response?.co2Representative?.representativesData || [];
        const co2TaxResponseFiltered = co2TaxResponseData.filter(
          (co2Representative) =>
            !generalResponseData.find(
              (representative) =>
                representative.documentDesti ===
                co2Representative.documentDesti,
            ),
        );
        const joinRepresentativesList = generalResponseData.concat(
          co2TaxResponseFiltered,
        );

        const concatResponse: GetRespresentativeMapResponse = {
          isRepresentative:
            response?.generalRepresentative?.isRepresentative ||
            response?.co2Representative?.isRepresentative,
          representativesData: joinRepresentativesList,
        };

        this.isRepresentative = concatResponse;
      });
  }

  // se consulta despues de scoring para verificar si es el representante del NIF
  verifyIfIsRepresentative(representativeNIF: string): Observable<boolean> {
    return forkJoin({
      generalRepresentative:
        this.identificationsEndpointsService.verifyIfIsRepresentativeWithNIF(
          representativeNIF,
          this._isAocAtesaCoordinador,
        ),
      co2Representative:
        this.identificationsEndpointsService.verifyIfIsRepresentativeWithNIFByProcedure(
          representativeNIF,
          CO2_TAX_ID,
          this._isAocAtesaCoordinador,
        ),
    }).pipe(
      map((response) => {
        return response?.co2Representative || response?.generalRepresentative;
      }),
    );
  }

  loginCo2(
    body: LoginRequest,
    profileUser: IdentificationType,
    navigate?: string,
    reloadOnSamePage?: boolean,
  ): Observable<LoginResponse> {
    return this.loginResponseSrv.login(body).pipe(
      tap((response) => {
        const user = response?.content;
        if (user) {
          this.storageSrv.profileUser = profileUser;
          this.isCoordinator =
            user.tipusAccess === IdentificationType.COORDINADOR;
          this.isCivilServant =
            user.tipusAccess === IdentificationType.CIVIL_SERVANT;

          if (navigate) {
            this.customRouter.navigateByBaseUrl(
              navigate,
              undefined,
              reloadOnSamePage,
            );
          }
        }
      }),
    );
  }
}
