<div class="mb-4" appHideOnCoordinator>
  <app-info-messages
    *ngIf="infoMessage.length > 0"
    [title]="infoMessageTitle | translate"
    [text]="infoMessage"
    [image]="'calendar'"
  >
    <p *ngIf="!isProvisional">
      <strong>{{
        'SE_PADRO_CO2.RECEIPTS.NOTIFICATION_LIQUIDATION' | translate
      }}</strong>
    </p>

    <div
      class="d-flex flex-row justify-content-between justify-content-sm-start mt-1 gap-2"
    >
      <se-button
        *ngIf="showModifyDomiciliation"
        [btnTheme]="'secondary'"
        (onClick)="navigateToUpdateDomiciled()"
      >
        {{ 'SE_PADRO_CO2.BUTTONS.MODIFY_DOMICILIATE' | translate }}
      </se-button>
      <se-button
        *ngIf="showRegisterDomiciliation"
        (onClick)="navigateToCreateDomiciliation()"
        [btnTheme]="'primary'"
      >
        {{ 'SE_PADRO_CO2.BUTTONS.DOMICILIAR_PAYMENTS' | translate }}
      </se-button>
    </div>
  </app-info-messages>
</div>

<ng-container *ngTemplateOutlet="tabView"></ng-container>

<ng-container *ngIf="isLoginSimple">
  <div class="my-5 row info-messages" appHideOnCoordinator>
    <div class="col-12">
      <app-info-messages
        [title]="'SE_PADRO_CO2.LOGIN_SIMPLE.ID_CAT_TITLE' | translate"
        [text]="'SE_PADRO_CO2.LOGIN_SIMPLE.ID_CAT_SUBTITLE' | translate"
        [image]="'id_cat_logo'"
      >
        <div class="d-flex flex-column flex-md-row mt-1">
          <se-button [btnTheme]="'secondary'" (onClick)="navigateToIdCat()">{{
            'SE_PADRO_CO2.LOGIN_SIMPLE.ID_CAT_BUTTON' | translate
          }}</se-button>
        </div>
      </app-info-messages>
    </div>
  </div>
</ng-container>

<div
  *ngIf="showRegisterDomiciliation"
  class="my-5 row info-messages"
  appHideOnCoordinator
>
  <div class="col-12">
    <app-info-messages
      [title]="domiciliatedInfoMessageTitle | translate"
      [text]="domiciliatedInfoMessage"
      [image]="'note'"
    >
      <div
        class="d-flex flex-column gap-2 flex-sm-row justify-content-sm-start mt-1"
      >
        <se-button
          *ngIf="showModifyDomiciliation"
          [btnTheme]="'secondary'"
          (onClick)="navigateToUpdateDomiciled()"
          >{{
            'SE_PADRO_CO2.BUTTONS.MODIFY_DOMICILIATE' | translate
          }}</se-button
        >
        <se-button
          class="mt-md-0 mt-2 ms-md-2 ms-0"
          *ngIf="showRegisterDomiciliation"
          (onClick)="navigateToCreateDomiciliation()"
          [btnTheme]="'primary'"
          >{{
            'SE_PADRO_CO2.BUTTONS.DOMICILIAR_PAYMENTS' | translate
          }}</se-button
        >
      </div>
    </app-info-messages>
  </div>
</div>

<ng-template #tabView>
  <se-tab-view
    [activeTabIndex]="activeTabIndex"
    (onChange)="onTabChange($event)"
  >
    <!-- CENSUS TABLE -->
    <se-tab-item
      [index]="receiptTabs.CENSUS_TABLE"
      [showBadge]="!!censusTotalPendent && !isProvisional"
      [badgeTextInsideCircle]="censusTotalPendent"
      [label]="censusTabLabel | translate: { year: currentExercise }"
    >
      <app-census-table
        *ngIf="activeTabIndex === receiptTabs.CENSUS_TABLE"
      ></app-census-table>
      <div *ngIf="vehiclesAlert?.title" class="mt-4" appHideOnCoordinator>
        <se-alert
          [title]="vehiclesAlert?.title! | translate"
          [subtitle]="vehiclesAlert?.subtitle! | translate"
          [type]="vehiclesAlert?.type!"
          [closeButton]="false"
          [titleClass]="'titleClass'"
        >
        </se-alert>
      </div>
    </se-tab-item>

    <!-- PREVIOUS EXERCISES -->
    <se-tab-item
      [index]="receiptTabs.PREVIOUS_EXERCISES_TABLE"
      [showBadge]="!!previousTotalPendent"
      [badgeTextInsideCircle]="previousTotalPendent"
      [label]="'SE_PADRO_CO2.RECEIPTS.PREVIOUS_EXERCISES' | translate"
    >
      <app-previous-exercises-table
        *ngIf="activeTabIndex === receiptTabs.PREVIOUS_EXERCISES_TABLE"
      ></app-previous-exercises-table>
      <div *ngIf="vehiclesAlert?.title" class="mt-4" appHideOnCoordinator>
        <se-alert
          [title]="vehiclesAlert?.title! | translate"
          [subtitle]="vehiclesAlert?.subtitle! | translate"
          [type]="vehiclesAlert?.type!"
          [closeButton]="false"
          [titleClass]="'titleClass'"
        >
        </se-alert>
      </div>
    </se-tab-item>

    <!-- PAYMENT DOCUMENTATION -->
    <se-tab-item
      [index]="receiptTabs.ASSOCIATED_DOCUMENTATION_TABLE"
      *ngIf="!isLoginSimple && !isConveniat"
      [label]="'SE_PADRO_CO2.RECEIPTS.ASSOCIATED_DOCUMENTATION' | translate"
    >
      <app-associated-documentation-table
        *ngIf="activeTabIndex === receiptTabs.ASSOCIATED_DOCUMENTATION_TABLE"
      ></app-associated-documentation-table>
    </se-tab-item>

    <!-- <se-tab-item [label]="'SE_PADRO_CO2.RECEIPTS.REQUESTS_MADE' | translate">
      <app-requests-made-table
        *ngIf="activeTabIndex === receiptTabs.REQUESTS_MADE_TABLE"
      ></app-requests-made-table>
    </se-tab-item> -->

    <!-- PROCEDURES -->
    <se-tab-item
      [index]="receiptTabs.GESTIONS_TABLE"
      [label]="'SE_PADRO_CO2.RECEIPTS.GESTIONS.TITLE' | translate"
      *ngIf="isTabVisible()"
    >
      <app-gestions
        *ngIf="activeTabIndex === receiptTabs.GESTIONS_TABLE"
        [co2Filters]="co2FiltersGestions"
      >
      </app-gestions>
    </se-tab-item>

    <!-- PAYMENTS -->
    <se-tab-item
      *ngIf="isTabVisible()"
      [index]="receiptTabs.PAYMENTS_TABLE"
      [label]="'SE_PADRO_CO2.RECEIPTS.PAGAMENTS.TITLE' | translate"
    >
      <app-pagaments
        *ngIf="activeTabIndex === receiptTabs.PAYMENTS_TABLE"
        [co2Filters]="co2FiltersPagaments"
      >
      </app-pagaments>
    </se-tab-item>
  </se-tab-view>
</ng-template>

<!-- Integracion Xatbot-->
<div id="widget-atc-chatbot"></div>
<div id="external-scripts"></div>
<!-- Integracion Xatbot-->
