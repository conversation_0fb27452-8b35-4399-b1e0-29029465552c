<div class="app-reason-resources pt-3">
  <se-alert
    *ngIf="isSelfRepresented && showReasAlert"
    [alertClass]="'suspension-link'"
    [type]="'neutral'"
    [closeButton]="false"
    [title]="'SE_PADRO_CO2.RECURS_PROCESS.ALERT_REAS.TITLE' | translate"
    [subtitleClass]="'fw-normal'"
  >
    <p>
      {{ 'SE_PADRO_CO2.RECURS_PROCESS.ALERT_REAS.SUBTITLE' | translate }}
      <se-link
        [disabled]="false"
        (onClick)="onReasLinkClick()"
        class="suspension-link"
        [size]="'regular'"
        [linkTheme]="'secondary'"
        [target]="'_blank'"
      >
        {{ 'SE_PADRO_CO2.RECURS_PROCESS.ALERT_REAS.LINK_TEXT' | translate }}
      </se-link>
      {{ 'SE_PADRO_CO2.RECURS_PROCESS.ALERT_REAS.SUBTITLE_2' | translate }}
    </p>
    <p>{{ 'SE_PADRO_CO2.RECURS_PROCESS.ALERT_REAS.SUBTITLE_3' | translate }}</p>
  </se-alert>

  <se-alert
    *ngIf="
      vehiclesInfo &&
      vehiclesInfo.matriculas &&
      vehiclesInfo.matriculas.length > 1
    "
    [type]="'info'"
    [closeButton]="false"
    [title]="
      'SE_PADRO_CO2.RECURS_PROCESS.ALERT_MULTIPLE_VEHICLES.TITLE' | translate
    "
    [subtitle]="
      'SE_PADRO_CO2.RECURS_PROCESS.ALERT_MULTIPLE_VEHICLES.SUBTITLE' | translate
    "
    [subtitleClass]="'fw-normal'"
  >
    <p
      [innerHTML]="
        'SE_PADRO_CO2.RECURS_PROCESS.ALERT_MULTIPLE_VEHICLES.MORE_INFO'
          | translate
      "
    ></p>
  </se-alert>
  <!-- Allegations Options -->
  <app-allegations-options
    *ngIf="reasonsData"
    [title]="'SE_PADRO_CO2.RECURS_PROCESS.REASON_TITLE' | translate"
    [subtitle]="'SE_PADRO_CO2.RECURS_PROCESS.SELECT_RECURS' | translate"
    [panelDescriptionDocuments]="
      'SE_PADRO_CO2.RECURS_PROCESS.REASON_SUBTITLE' | translate
    "
    [reasonsData]="reasonsData"
    [functionalModule]="functionalModule.RECURS"
    [idTramit]="idTramit"
    [dropAreaTitlePreLinkText]="
      'SE_COMPONENTS.FILE_UPLOADER.ARROSSEGUEU_MULTIPLE' | translate
    "
    [dropAreaTitleLinkText]="
      'SE_COMPONENTS.FILE_UPLOADER.CLICK_MULTIPLE' | translate
    "
    [dropAreaTitlePostLinkText]="
      'SE_COMPONENTS.FILE_UPLOADER.CARGAR_MULTIPLE' | translate
    "
    [documentsSigedaDescriptions]="documentsSigedaDescriptions"
    [documentSectionTitle]="
      'SE_PADRO_CO2.RECURS_PROCESS.DOCUMENTS.TITLE' | translate
    "
    (allegationsOptionsData)="onResourcesOptionsChange($event)"
  ></app-allegations-options>

  <div class="mt-4">
    <mf-pagaments-dades-bancaries
      *axLazyElement
      [fileFormatSeparation]="fileFormatSeparation"
      [pagamentsData]="bankDetailInput"
      [subtitle]="
        'SE_PADRO_CO2.RECURS_PROCESS.NEW_BANK_ACCOUNT_DETAILS' | translate
      "
      [isPanel]="true"
      [collapsible]="false"
      [dropAreaTitlePreLinkText]="
        'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.DOC_SECTION.PRELINK_TEXT'
          | translate
      "
      [dropAreaTitleLinkText]="
        'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.DOC_SECTION.LINK_TEXT' | translate
      "
      (onFormChanged)="onBankDetailChange($event)"
    >
    </mf-pagaments-dades-bancaries>
  </div>
  <div class="mt-4">
    <se-alert
      *ngIf="isSuspensioAlertVisible"
      [alertClass]="'suspension-link'"
      [type]="'neutral'"
      [closeButton]="false"
      [title]="'SE_PADRO_CO2.RECURS_PROCESS.ALERT_SUSPENSIO.TITLE' | translate"
      [subtitle]="
        'SE_PADRO_CO2.RECURS_PROCESS.ALERT_SUSPENSIO.SUBTITLE' | translate
      "
      [subtitleClass]="'fw-normal'"
    >
    </se-alert>
  </div>

  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button
      type="button"
      btnTheme="secondary"
      (onClick)="onGoBackButtonClick()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate }}
    </se-button>

    <se-button
      type="submit"
      btnTheme="primary"
      (onClick)="onContinueButtonClick()"
      [disabled]="!reasonsFormData?.isReasonsFormValid || isIbanInvalid"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.CONTINUE' | translate }}
    </se-button>
  </div>
</div>
