import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { AppRoutes, IdentificationType } from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { TranslateService } from '@ngx-translate/core';
import { FileFormatsSeparation, SeAlertMessage } from 'se-ui-components-mf-lib';
import { BankDetailInput } from './model/bank-details.model';
import { PaymentsService } from '@app/shared/services';
import { VehiclesSelectedInfo } from '@app/shared/models';
import { ProceduresHeaderService } from '../../../shared/services/procedures-header';

@Component({
  selector: 'app-bank-details',
  templateUrl: './bank-details.component.html',
  styleUrls: ['./bank-details.component.scss'],
})
export class BankDetailsComponent implements OnInit {
  protected bankDetailInput: BankDetailInput | undefined;
  protected alertDomiciliationRequest: SeAlertMessage | undefined;
  protected isNextButtonDisabled = true;
  protected bodyToRequestVehiclesDetail: VehiclesSelectedInfo | undefined;
  protected domiciliationDate = this.specificConfigurationSrv.nextReceiptDate;
  private iban: string | undefined;
  private ibanDr: boolean | undefined;

  fileFormatSeparation: FileFormatsSeparation = FileFormatsSeparation.COMMA;

  get isConveniat(): boolean {
    return this.storage.profileUser === IdentificationType.CONVENIAT;
  }

  get isRepresentant(): boolean {
    return this.storage.profileUser === IdentificationType.REPRESENTATIVE;
  }

  get isCivilServant(): boolean {
    return this.storage.profileUser === IdentificationType.CIVIL_SERVANT;
  }

  get isSelfPerson(): boolean {
    return this.storage.profileUser === IdentificationType.NOM_PROPI;
  }

  constructor(
    private translate: TranslateService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private cdr: ChangeDetectorRef,
    private storage: StorageService,
    private customRouter: CustomRouterService,
    private paymentsSrv: PaymentsService,
    private loginSrv: LoginResponseService,
    private header: ProceduresHeaderService,
  ) {}

  ngOnInit(): void {
    this.setBodyToRequestVehiclesDetail();
    this.header.setupDomiciliationHeader(this.storage.vehiclesToBeRegistered);
    this.setRequestDirectDebitAlertList();
    this.setBankDetails();
  }

  protected goBack(): void {
    this.storage.clearIbanDomiciliation();
    this.storage.clearIbanDomiciliationDr();
    this.customRouter.navigateByBaseUrl(AppRoutes.DOMICILIATION);
  }

  protected onContinue(): void {
    this.storage.ibanDomiciliation = this.iban!;
    this.storage.ibanDomiciliationDR = this.ibanDr!;
    this.customRouter.navigateByBaseUrl(AppRoutes.DIRECT_DEBIT_SUMMARY);
  }

  protected onBankDetailChange(event: Event): void {
    const customEvent = event as CustomEvent<FormGroup>;
    if (customEvent.detail.value.iban) {
      this.isNextButtonDisabled = customEvent.detail.invalid;
      this.ibanDr = customEvent.detail.value.DR;

      if (!this.isNextButtonDisabled) {
        this.iban = customEvent.detail.value.iban;
      }
    }
    this.cdr.detectChanges();
  }

  private setRequestDirectDebitAlertList(): void {
    const { percentDiscount } = this.specificConfigurationSrv;

    this.alertDomiciliationRequest = {
      title: this.translate.instant(
        'SE_PADRO_CO2.DOMICILIATION_MODULE.REQUEST_DIRECT_DEBIT_PANEL.ALERT_LIST.FIRST',
        { percentDiscount },
      ),
    } as SeAlertMessage;
  }

  protected setBankDetails(): void {
    this.bankDetailInput = this.paymentsSrv.getBankDetailInput({
      iban: this.storage.ibanDomiciliation,
      dr: this.storage.ibanDomiciliationDR,
    });
  }

  protected isValidateRepresentative = (): boolean =>
    this.paymentsSrv.isValidateRepresentative();

  private setBodyToRequestVehiclesDetail(): void {
    this.bodyToRequestVehiclesDetail = {
      provisional: this.specificConfigurationSrv.isProvisional,
      matriculas:
        this.storage.vehiclesToBeRegistered?.map(
          (vehicle) => vehicle.matricula,
        ) ?? [],
      exercici: this.specificConfigurationSrv.currentExercise,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
    };
  }
}
