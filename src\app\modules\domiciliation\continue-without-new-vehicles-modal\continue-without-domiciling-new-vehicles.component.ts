import { Component, EventEmitter, Input, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  Column,
  Row,
  SeModal,
  SeModalOutputEvents,
} from 'se-ui-components-mf-lib';

import { OLD_VEHICLES_COLUMNS } from '../vehicle-selection/model/vehicle-selection.model';

@Component({
  selector: 'app-continue-without-new-vehicles-modal',
  template: `
    <se-modal
      [data]="data"
      (modalOutputEvent)="handleNavigateToBankDetails($event)"
      (modalSecondaryButtonEvent)="closeModal()"
    >
      <p>
        {{
          'SE_PADRO_CO2.DOMICILIATION_MODULE.CONTINUE_WITHOUT_DOMICILING_NEW_VEHICLES_MODAL.SUBTITLE'
            | translate
        }}
      </p>
      <p>
        {{
          'SE_PADRO_CO2.DOMICILIATION_MODULE.CONTINUE_WITHOUT_DOMICILING_NEW_VEHICLES_MODAL.SUBTITLE_2'
            | translate
        }}
      </p>
      <span>{{
        'SE_PADRO_CO2.DOMICILIATION_MODULE.CONTINUE_WITHOUT_DOMICILING_NEW_VEHICLES_MODAL.DETAIL_TABLE'
          | translate
      }}</span>
      <se-table
        [columns]="vehiclesToDomicileColumns"
        [data]="tableData"
        [cellTemplatePriorityOrder]="'row-column-cell'"
      ></se-table>
    </se-modal>
  `,
})
export class ContinueWithoutDomicilingNewVehiclesModalComponent {
  protected vehiclesToDomicileColumns: Column[] = OLD_VEHICLES_COLUMNS;

  @Input() data: SeModal | undefined;
  @Input() tableData: Row[] = [];
  @Output() handleAcceptModalOutput = new EventEmitter<void>();

  constructor(private activatedModalService: NgbActiveModal) {}

  handleNavigateToBankDetails(event: string): void {
    if (event === SeModalOutputEvents.MAIN_ACTION) {
      this.closeModal();
      this.handleAcceptModalOutput.emit();
    } else {
      this.closeModal();
    }
  }

  closeModal(): void {
    this.activatedModalService.close();
  }
}
