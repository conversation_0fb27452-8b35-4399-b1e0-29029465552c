import { IdentificationType, ValidateButtonEnum } from '@core/models';
import { SeHttpResponse } from 'se-ui-components-mf-lib';

export interface IdentificationData {
  acceptTerms: boolean;
  presenterId: string;
  userNif: string;
  presenterNif: string;
  presenterName: string;
  passiveSubjectNif: string;
  passiveSubjectName: string;
  identificationType: string | number | IdentificationType | null;
  responsibleStatement: boolean;
  validateBtn: ValidateButtonEnum;
  isResponsibleStatementShown: boolean;
  isRepresentative: boolean;
}

export interface Taxpayer {
  nif: string;
  name: string;
}

export interface CivilServantResponse extends SeHttpResponse {
  content: { [key: string]: boolean };
}
