import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  CodiCategory,
  CodiCombustible,
  DetallPadro,
  PopupEmmisions,
} from '@shared/models';
import { Column, Nullable, Row, SeModal } from 'se-ui-components-mf-lib';
import { Co2EmissionsModalConceptsData } from './models/co2-emissions-modal.model';
import { Co2EmissionsModalService } from './services/co2-emissions-modal.service';
import { DecimalPipe } from '@angular/common';
import { TranslateService } from '@ngx-translate/core';

@Component({
  selector: 'app-co2-emissions-modal',
  templateUrl: './co2-emissions-modal.component.html',
  styleUrls: ['./co2-emissions-modal.component.scss'],
  providers: [DecimalPipe],
})
export class Co2EmissionsModalComponent implements OnInit {
  @Input() data: SeModal | undefined;
  @Input() carDetail: Nullable<DetallPadro>;

  get isFormula(): boolean {
    return (
      this.carDetail?.popupEmissions === PopupEmmisions.FORMULA_FUEL ||
      this.carDetail?.popupEmissions === PopupEmmisions.FORMULA_REST
    );
  }

  get co2Emissions(): number {
    return this.getCo2Emissions();
  }

  tableData: Row[] = [];
  tableColumns: Column[] = [];
  concepts: Co2EmissionsModalConceptsData[] = [];
  formulaFuel: string = '';

  constructor(
    private activatedModalService: NgbActiveModal,
    private co2EmissionsModalService: Co2EmissionsModalService,
    private translateService: TranslateService,
  ) {}

  ngOnInit(): void {
    this.setupTableColumns();
    this.setupTableData();
    this.setupConcepts();
  }

  private setupTableColumns(): void {
    this.tableColumns = [
      {
        header: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.CONCEPT',
        tooltip: false,
        size: 12,
        key: 'concept',
        cellComponentName: 'defaultCellComponent',
        resizable: true,
      },
      {
        header: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.EXPLANATION',
        tooltip: false,
        key: 'explanation',
        cellComponentName: 'defaultCellComponent',
        resizable: true,
      },
      {
        header: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.VALUE',
        tooltip: false,
        size: 12,
        key: 'value',
        cellComponentName: 'numberCellComponent',
        resizable: true,
        cellConfig: {
          digitsInfo: '1.0-3',
        },
      },
    ];
  }

  private setupTableData(): void {
    this.tableData = [
      {
        data: {
          concept: { value: 'CC' },
          explanation: {
            value: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.CUBIC_CAPACITY',
          },
          value: { value: this.carDetail?.cilindrada },
        },
      },
      {
        data: {
          concept: { value: 'PF' },
          explanation: {
            value: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.POTENTIAL_FISCAL_POWER',
          },
          value: { value: this.carDetail?.potenciaFiscal },
        },
      },
      {
        data: {
          concept: { value: 'PN' },
          explanation: {
            value: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.MAX_NET_POWER',
          },
          value: { value: this.carDetail?.potenciaNeta },
        },
      },
      {
        data: {
          concept: { value: 'MMX' },
          explanation: {
            value: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.MAXIMUM_WEIGHT',
          },
          value: { value: this.carDetail?.massaMaxima },
        },
      },
      {
        data: {
          concept: { value: 'MOM' },
          explanation: {
            value: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.MOM',
          },
          value: { value: this.carDetail?.massaOrdreMarxa },
        },
      },
      {
        data: {
          concept: { value: 'TR' },
          explanation: {
            value: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.RUNNING_ORDER_MASS',
          },
          value: { value: this.carDetail?.tara },
        },
      },
      {
        data: {
          concept: { value: 'T' },
          explanation: {
            value: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.VEHICLE_AGE',
          },
          value: { value: this.carDetail?.antiguitat },
        },
      },
    ];
  }

  private setupConcepts(): void {
    if (this.carDetail?.popupEmissions === PopupEmmisions.FORMULA_FUEL) {
      this.setupFuelFormulaConcepts();
    }

    if (this.carDetail?.popupEmissions === PopupEmmisions.FORMULA_REST) {
      this.setupRestFormulaConcepts();
    }
  }

  private setupFuelFormulaConcepts(): void {
    if (this.carDetail?.hibridElectric) {
      this.concepts = this.co2EmissionsModalService.getHevFormula(
        this.carDetail,
      );

      this.formulaFuel = this.translateService.instant(
        'SE_PADRO_CO2.MODAL.DETAIL.HYBRID_FUEL',
      );
      return;
    }

    this.formulaFuel = this.carDetail?.combustible || '';

    if (
      this.carDetail?.categoria === CodiCategory.L3E ||
      this.carDetail?.categoria === CodiCategory.L4E ||
      this.carDetail?.categoria === CodiCategory.L5E ||
      this.carDetail?.categoria === CodiCategory.L7E
    ) {
      this.concepts = this.co2EmissionsModalService.getLCategoryFormula(
        this.carDetail,
      );

      return;
    }

    if (
      this.carDetail?.categoria === CodiCategory.M1 &&
      this.carDetail?.codiCombustible === CodiCombustible.DIESEL
    ) {
      this.concepts = this.co2EmissionsModalService.getM1DieselFormula(
        this.carDetail,
      );

      return;
    }

    if (
      this.carDetail?.categoria === CodiCategory.N1 &&
      this.carDetail?.codiCombustible === CodiCombustible.DIESEL
    ) {
      this.concepts = this.co2EmissionsModalService.getN1DieselFormula(
        this.carDetail,
      );

      return;
    }

    if (this.carDetail?.codiCombustible === CodiCombustible.GASOLINA) {
      this.concepts = this.co2EmissionsModalService.getGasolinaFormula(
        this.carDetail,
      );

      return;
    }

    this.setupRestFormulaConcepts();
  }

  private setupRestFormulaConcepts(): void {
    if (!this.carDetail) return;

    this.concepts = this.co2EmissionsModalService.getRestVehiclesFormula(
      this.carDetail,
    );
  }

  protected getCo2Emissions(): number {
    const pepe = this.concepts.reduce((previous, current) => {
      const formattedValue =
        Number(current.value) ||
        current.value?.replaceAll('.', '').replace(',', '.') ||
        0;

      if (current.constant) {
        return (
          previous +
          Number(current.constant.replace(',', '.')) * Number(formattedValue)
        );
      }

      if (!current.constant && current.operation === '+') {
        return previous + Number(formattedValue);
      }

      if (!current.constant && current.operation === '-') {
        return previous - Number(formattedValue);
      }

      return previous;
    }, 0);

    return pepe;
  }

  closeModal(): void {
    this.activatedModalService.close();
  }
}
