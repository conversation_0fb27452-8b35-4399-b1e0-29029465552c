import { SeStep } from 'se-ui-components-mf-lib';

export const getDomiciliationSteps = (translates: never): SeStep[] => [
  {
    id: 'DOMICILIATION_STEP1',
    label: translates['SELECT_VEHICLES'],
  },
  {
    id: 'DOMICILIATION_STEP2',
    label: translates['BANK_DETAILS'],
  },
  {
    id: 'DOMICILIATION_STEP3',
    label: translates['SUMMARY'],
  },
];
export const getDomiciledModificationStepsSteps = (
  translates: never,
): SeStep[] => [
  {
    id: 'DOMICILED_STEP_1',
    label: translates['MODIFY_DOMICILED'],
  },
  {
    id: 'DOMICILED_STEP_2',
    label: translates['MODIFY_SUMMARY'],
  },
];
