import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  Column,
  Row,
  SeModal,
  SeModalOutputEvents,
} from 'se-ui-components-mf-lib';
import { TableKeys } from '../model/modification.model';
import { DomiciledVehicle } from '@app/core/models';
import {
  SpecificConfigurationService,
  StorageService,
} from '@app/core/services';

export const BASE_TRANSLATE =
  'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_1.VEHICLES_TO_BE_REGISTERED';

@Component({
  selector: 'app-modification-bank-details-modal',
  template: `
    <se-modal
      [data]="data"
      [closableLabel]="'UI_COMPONENT.BUTTONS.CLOSE'"
      (modalOutputEvent)="continue($event)"
      (modalSecondaryButtonEvent)="closeModal()"
    >
      <div class="mb-3 mt-3">
        <p>
          {{
            'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.BANK_DETAILS_MODAL.CURRENT_VEHICLES'
              | translate
          }}
        </p>
        <se-table
          [styleClass]="'border-table'"
          [resizable]="false"
          [columns]="vehiclesColumns"
          [data]="currentVehiclesRows"
        ></se-table>
      </div>
      <div class="mb-3">
        <p>
          {{
            'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.BANK_DETAILS_MODAL.NEXT_VEHICLES'
              | translate
          }}
        </p>
        <se-table
          [styleClass]="'border-table'"
          [resizable]="false"
          [columns]="vehiclesColumns"
          [data]="nextVehiclesRows"
        ></se-table>
      </div>
      <div
        [innerHTML]="
          'SE_PADRO_CO2.DOMICILIATION_MODULE.MODIFICATION.BANK_DETAILS_MODAL.FOOTER'
            | translate
        "
      ></div>
    </se-modal>
  `,
})
export class ModificationBankDetailsModalComponent implements OnInit {
  vehiclesColumns: Column[] = [];
  currentVehiclesRows: Row[] = [];
  nextVehiclesRows: Row[] = [];

  @Input() data: SeModal | undefined;
  @Input() currentVehicles: DomiciledVehicle[] = [];
  @Input() nextVehicles: DomiciledVehicle[] = [];

  @Output() continueOutput: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private activatedModalService: NgbActiveModal,
    private translateService: TranslateService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private storageService: StorageService,
  ) {}

  ngOnInit(): void {
    this.setVehiclesColumns();
    this.setVehiclesRows();
  }

  private setVehiclesColumns(): void {
    this.vehiclesColumns = this.getVehiclesColumns();
  }

  private setVehiclesRows(): void {
    this.currentVehiclesRows = this.getVehiclesRows(this.currentVehicles);
    this.nextVehiclesRows = this.getVehiclesRows(this.nextVehicles);
  }

  closeModal(): void {
    this.activatedModalService.close();
  }

  continue(modalOutpuAction: string): void {
    if (modalOutpuAction === SeModalOutputEvents.MAIN_ACTION) {
      this.storageService.vehiclesToBeUpdated = this.currentVehicles;
      this.continueOutput.emit();
    }

    this.closeModal();
  }

  private getVehiclesColumns(): Column[] {
    return [
      {
        key: TableKeys.Plate,
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.CAR_REGISTRATION`,
        ),
      },
      {
        key: TableKeys.Vehicle,
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.VEHICLE`,
        ),
      },
    ];
  }

  private getVehiclesRows(vehicles: DomiciledVehicle[]): Row[] {
    return (vehicles ?? []).map((vehicle) => {
      return {
        data: {
          plate: { value: vehicle.matricula },
          vehicle: { value: `${vehicle.marca} ${vehicle.model}` },
        },
      };
    });
  }
}
