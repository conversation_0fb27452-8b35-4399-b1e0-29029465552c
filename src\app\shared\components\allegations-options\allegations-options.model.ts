import { iDocumentPadoct } from 'se-ui-components-mf-lib';
import { ReasonsFormValue } from '../../../modules/allegations/reason-allegation/reason-allegation.model';

export interface Reasons extends Reason {
  tooltip?: string;
  collapsed?: boolean;
  collapsible?: boolean;
  submotius?: SubReasons[];
  maximDocumentsAportar?: number;
  minimDocumentsAportar?: number;
}
export interface SubReasons extends Reason {
  name?: string | null;
  formControlName?: string;
  checkboxFormControlName?: string;
  freeTextFromControlName?: string;
  descripcioObligatoria?: boolean;
  checkbox?: string;
  descripcioMax?: number;
  descripcioMin?: number;
}

export interface Reason {
  id: number;
  label: string;
  description?: string;
  descripcioObligatoria?: boolean;
  unic?: boolean;
  formControlName?: string | null;
  documents: ContestableActDocument[];
}

export interface ContestableActDocument {
  type: string;
  subtype: string;
  name?: string;
  description: string;
  required?: boolean;
  allowedFiles?: string[];
  allowedSize?: number;
}

export interface ReasonsFormData {
  isReasonsFormValid: boolean;
  isFreeTextBlockShown: boolean;
  reasonsFormValue: ReasonsFormValue;
  reasonsDocuments: iDocumentPadoct[];
  reasonsList: Reasons[];
  documentsSigedaDescriptions: ContestableActDocument[];
}
