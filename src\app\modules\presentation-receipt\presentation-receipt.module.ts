import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'primeng/api';
import {
  SeButtonModule,
  SePanelModule,
  SeTagModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { PresentationReceiptComponent } from './presentation-receipt.component';

const routes: Routes = [
  {
    path: '',
    component: PresentationReceiptComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      isElementVisible: false,
    },
  },
];

@NgModule({
  declarations: [PresentationReceiptComponent],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes),
    SePanelModule,
    SeTagModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-gestions-receipt',
          url: environment.mfGestionsURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
    TranslateModule,
    SeButtonModule,
  ],
})
export class PresentationReceiptModule {}
