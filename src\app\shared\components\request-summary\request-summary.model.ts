import { Nullable } from 'se-ui-components-mf-lib';
import { Allegation, VehiclesSelectedInfo } from '@app/shared/models';

export interface RequestSummary extends Allegation {
  dataProxRebut?: Nullable<string>;
  exercici?: Nullable<number>;
  showGdprMsg?: boolean;
  iban?: string;
  ibanLabel?: string;
  singleVehicleText?: string;
  vehicles?: VehiclesSelectedInfo;
  descripcion?: string;
  checkbox?: string;
}
