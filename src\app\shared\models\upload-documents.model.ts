import { Validators } from '@angular/forms';
import { Column } from 'se-ui-components-mf-lib';
export const DEFAULT_TABLE_COLUMN_NAME: Column = {
  header: 'SE_COMPONENTS.FILE_UPLOADER.NAME',
  key: 'name',
  cellComponentName: 'defaultCellComponent',
  cellConfig: { tooltip: true, ellipsis: true },
  resizable: false,
};

export const DEFAULT_TABLE_COLUMN_SIZE: Column = {
  header: 'SE_COMPONENTS.FILE_UPLOADER.SIZE',
  key: 'size',
  size: 10,
  cellComponentName: 'defaultCellComponent',
  resizable: false,
};

export const DEFAULT_TABLE_COLUMN_DOC_TYPE_TEXT: Column = {
  header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
  key: 'docType',
  cellComponentName: 'defaultCellComponent',
  resizable: false,
};

export const DEFAULT_TABLE_COLUMN_DOC_TYPE_DROPDOWN: Column = {
  header: 'UI_COMPONENTS.ATTACH_FILES.DOCUMENT_TYPE_LABEL',
  key: 'docType',
  cellComponentName: 'dropdownCellComponent',
  resizable: false,
  cellConfig: {
    id: 'docType',
    label: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
    options: [],
    disabled: false,
    showClear: false,
    autoSelect: true,
    editable: false,
    readOnly: false,
    filter: false,
    validators: Validators.required,
    optionLabel: 'description',
    optionValue: 'description',
    placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
    required: true,
  },
};

export const DEFAULT_TABLE_COLUMN_DESCRIPTION_TEXT: Column = {
  header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION',
  key: 'description',
  cellComponentName: 'defaultCellComponent',
  cellConfig: { tooltip: true, ellipsis: true },
  resizable: false,
};

export const DEFAULT_TABLE_COLUMN_DESCRIPTION_INPUT: Column = {
  header: 'SE_COMPONENTS.FILE_UPLOADER.DESCRIPTION_OPT',
  key: 'description',
  cellComponentName: 'inputCellComponent',
  resizable: false,
  cellConfig: {
    id: 'description',
    label: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
    disabled: false,
    showClear: false,
    editable: false,
    readOnly: false,
    filter: false,
    optionLabel: 'label',
    optionValue: 'id',
    placeholder: 'UI_COMPONENTS.SELECT.PLACEHOLDER',
    required: false,
  },
};

export const TABLE_COLUMNS: Column[] = [
  { ...DEFAULT_TABLE_COLUMN_NAME },
  { ...DEFAULT_TABLE_COLUMN_SIZE },
  { ...DEFAULT_TABLE_COLUMN_DOC_TYPE_TEXT },
  { ...DEFAULT_TABLE_COLUMN_DESCRIPTION_TEXT },
];

export const MODAL_TABLE_COLUMNS: Column[] = [
  {
    ...DEFAULT_TABLE_COLUMN_NAME,
    resizable: true,
  },
  { ...DEFAULT_TABLE_COLUMN_SIZE },
  { ...DEFAULT_TABLE_COLUMN_DOC_TYPE_DROPDOWN },
  { ...DEFAULT_TABLE_COLUMN_DESCRIPTION_INPUT },
];

export const TABLE_COLUMNS_DOC_TYPE_FIRST_ELEMENT: Column[] = [
  { ...DEFAULT_TABLE_COLUMN_DOC_TYPE_TEXT },
  { ...DEFAULT_TABLE_COLUMN_NAME },
  { ...DEFAULT_TABLE_COLUMN_SIZE },
  {
    ...DEFAULT_TABLE_COLUMN_DESCRIPTION_TEXT,
    cellConfig: undefined,
  },
];

export const MODAL_TABLE_COLUMNS_DOC_TYPE_ID_OPTION_VALUE: Column[] = [
  {
    ...DEFAULT_TABLE_COLUMN_NAME,
    resizable: true,
  },
  { ...DEFAULT_TABLE_COLUMN_SIZE },
  {
    ...DEFAULT_TABLE_COLUMN_DOC_TYPE_DROPDOWN,
    cellConfig: {
      ...DEFAULT_TABLE_COLUMN_DOC_TYPE_DROPDOWN.cellConfig,
      optionLabel: 'label',
      optionValue: 'id',
    },
  },
  { ...DEFAULT_TABLE_COLUMN_DESCRIPTION_INPUT },
];

export const MODAL_TABLE_COLUMNS_DOC_TYPE_ID_OPTION_VALUE_DESCRIPTION_MAX20: Column[] =
  [
    {
      ...DEFAULT_TABLE_COLUMN_NAME,
      resizable: true,
    },
    { ...DEFAULT_TABLE_COLUMN_SIZE },
    {
      ...DEFAULT_TABLE_COLUMN_DOC_TYPE_DROPDOWN,
      cellConfig: {
        ...DEFAULT_TABLE_COLUMN_DOC_TYPE_DROPDOWN.cellConfig,
        optionLabel: 'label',
        optionValue: 'id',
      },
    },
    {
      ...DEFAULT_TABLE_COLUMN_DESCRIPTION_INPUT,
      cellConfig: {
        ...DEFAULT_TABLE_COLUMN_DESCRIPTION_INPUT.cellConfig,
        maxLength: 20,
      },
    },
  ];
