import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { BaseUrlService } from '@app/core/services/url-atc/url-atc.service';
import { AppRoutes, BASE_URL, IdentificationType } from '@core/models';
import {
  HeaderInfoService,
  IdentificationsService,
  StorageService,
} from '@core/services';
import { combineLatest, Subject, takeUntil } from 'rxjs';
import { SeAuthService, SeUser } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-identifications',
  templateUrl: './identifications.component.html',
})
export class IdentificationsComponent implements OnInit, OnDestroy {
  private unsubscribe: Subject<void> = new Subject();
  private user: SeUser | undefined;

  currentCase: IdentificationType | undefined;
  isRepresentative: boolean | undefined;
  LOGIN_CASES = IdentificationType;
  canviarContribuent: boolean = false;

  constructor(
    private readonly identificationsService: IdentificationsService,
    private readonly header: HeaderInfoService,
    private readonly storeSrv: StorageService,
    private readonly authService: SeAuthService,
    private readonly baseUrlService: BaseUrlService,
    private readonly aRoute: ActivatedRoute,
  ) {
    //Para cuando se quiera cambiar el contribuent vaya directo a la pagina correcta
    this.aRoute.queryParams.subscribe((params) => {
      this.canviarContribuent = params['canviarContribuent'] === 'true';
    });

    this.header.reset();
    if (!this.canviarContribuent) {
      this.storeSrv.clearAllData();
      this.currentCase = this.LOGIN_CASES.NO_PERMET;
      this.identificationsService.loadRepresentativeStatus();
      this.identificationsService.loadAgreementStatus();
    }
  }

  ngOnInit(): void {
    if (this.canviarContribuent) {
      this.currentCase = this.storeSrv.loginCase!;
      this.isRepresentative = this.identificationsService.isRepresentative;
      return;
    }

    this.user = this.authService.getSessionStorageUser();
    // Si se accede por gicar
    if (this.user?.esLoginGicar) {
      // Si es coordinador
      if (this.user?.esCoordinador)
        this.currentCase = this.LOGIN_CASES.COORDINADOR;
      // Si es atesa
      if (this.user?.esAtesa) this.currentCase = this.LOGIN_CASES.CIVIL_SERVANT;
      // Almacenamos la informacion del usuario
      this.storeSrv.profileUser = this.currentCase;
      this.storeSrv.loginCase = this.currentCase;
      this.isRepresentative = false;
    } else {
      this.setCurrentCase();
    }
  }

  private setCurrentCase(): void {
    combineLatest([
      this.identificationsService.isConveniat$,
      this.identificationsService.isRepresentative$,
    ])
      .pipe(takeUntil(this.unsubscribe))
      .subscribe(([isConveniat, isRepresentative]) => {
        if (this.isRedirectElMeuEspai(isConveniat, isRepresentative)) {
          this.identificationsService
            .loginCo2({}, IdentificationType.NOM_PROPI)
            .pipe(takeUntil(this.unsubscribe))
            .subscribe(() => {
              this.baseUrlService.goToElMeuEspai(
                `${BASE_URL}/${AppRoutes.RECEIPTS}`,
              );
            });

          return;
        }

        this.isRepresentative = isRepresentative;

        // Damos mas peso a la representacion pero comprobamos el caso que puede darse de que sean ambos
        if (this.isRepresentative && !isConveniat)
          this.currentCase = this.LOGIN_CASES.REPRESENTATIVE;
        // Sino, miramos si es conveniado
        else if (isConveniat) this.currentCase = this.LOGIN_CASES.CONVENIAT;

        this.storeSrv.profileUser = this.currentCase;
        this.storeSrv.loginCase = this.currentCase;
      });
  }

  private isRedirectElMeuEspai(
    isConveniat: boolean,
    isRepresentative: boolean,
  ): boolean {
    const user = this.authService.getSessionStorageUser();
    return (
      user &&
      (user.esAtesa ||
        (user.esCoordinador && !isRepresentative) ||
        (user.esEmpleadoPublico && !user.esCoordinador) ||
        (!user.esAtesa &&
          !user.esCoordinador &&
          !isConveniat &&
          !isRepresentative))
    );
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }
}
