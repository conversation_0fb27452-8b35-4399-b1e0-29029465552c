import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { SePageLayoutService, SeStep } from 'se-ui-components-mf-lib';

import { getAllegationSteps } from './allegation.model';
import {
  getDomiciledModificationStepsSteps,
  getDomiciliationSteps,
} from './domiciliation.model';
import { getRepositionSteps } from './reposition.model';
import { getReasSteps } from './reas.model';

@Injectable({
  providedIn: 'root',
})
export class StepperService {
  constructor(
    private translate: TranslateService,
    private pageLayoutService: SePageLayoutService,
  ) {}

  initializeAvailableRoutes(): void {
    this.translate.stream('SE_PADRO_CO2.STEPPER').subscribe((translates) => {
      const TP_STEPPER: { [x: string]: SeStep[] } = {
        ALLEGATION_STEPS: getAllegationSteps(translates as never),
        REPOSITION_STEPS: getRepositionSteps(translates as never),
        REAS_STEPS: getReasSteps(translates as never),
        DOMICILIATION_STEPS: getDomiciliationSteps(translates as never),
        DOMICILED_MODIFICATION_STEPS: getDomiciledModificationStepsSteps(
          translates as never,
        ),
      };

      this.pageLayoutService.setAvailableSteps(TP_STEPPER);
    });
  }
}
