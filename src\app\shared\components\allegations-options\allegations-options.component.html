<se-panel class="allegations-options-panel mb-4" [title]="title | translate">
  <p *ngIf="subtitle">{{ subtitle | translate }}</p>
  <form *ngIf="reasonsForm" [formGroup]="reasonsForm">
    <div *ngFor="let reason of reasons">
      <se-accordion
        [inputCheckbox]="{
          label: reason.label,
          id: reason.id.toString(),
          value: false,
        }"
        [inputType]="'checkbox'"
        [collapsible]="!!reason.collapsible"
        [collapsed]="!!reason.collapsed"
        [tooltip]="!!reason.tooltip"
        [tooltipText]="tooltipText"
        (onSelectionChange)="onReasonSelected($event, reason)"
        [controlName]="reason.formControlName!"
      >
        <ng-template #tooltipText>
          <div [innerHTML]="reason.tooltip"></div>
        </ng-template>
        <div
          class="row"
          *ngIf="reason.description && reason.submotius?.length === 0"
        >
          <div class="col-12 mb--3">
            <se-alert
              [type]="'info'"
              [closeButton]="false"
              [showAlertIcon]="false"
            >
              <div [innerHTML]="reason.description"></div>
            </se-alert>
          </div>
          <div
            *ngIf="!!reason?.descripcioObligatoria"
            class="mt-2"
            [ngClass]="{ 'mb--3': !reason?.submotius?.length }"
          >
            <se-textarea
              [id]="reason.id.toString()"
              [formControlName]="'free-text_' + reason.id"
              [maxlength]="1000"
              [label]="
                'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.FREE_TEXT_SUBTITLE'
                  | translate
              "
              [readonly]="false"
            >
            </se-textarea>
          </div>
        </div>
        <!-- TODO: cambiar el isCollased para que sea personalizado x c/reason -->
        <div *ngIf="!!reason?.submotius?.length">
          <div *ngFor="let subreason of reason.submotius">
            <se-radio
              [id]="subreason.id.toString()"
              [label]="subreason.label"
              [value]="subreason.id"
              [name]="subreason.name!"
              [formControlName]="subreason.formControlName!"
            ></se-radio>
          </div>
          <div
            class="mt-2"
            [ngClass]="{
              'mb--3':
                !getSubreasonSelected(reason)?.checkbox &&
                !getSubreasonSelected(reason)?.descripcioObligatoria,
            }"
            *ngIf="!!getSubreasonSelected(reason)?.description"
          >
            <se-alert
              [showAlertIcon]="!deviceService.isMobile()"
              [type]="'info'"
              [closeButton]="false"
              [showAlertIcon]="false"
            >
              <p [innerHTML]="getSubreasonSelected(reason)?.description"></p>
            </se-alert>
          </div>

          <div
            *ngIf="!!getSubreasonSelected(reason)?.checkbox"
            class="row mt-3"
          >
            <p class="col-12 text-md">
              {{
                'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.SUBREASON_CHECKBOX_LABEL'
                  | translate
              }}
            </p>
            <se-checkbox
              class="col-12"
              [formControlName]="
                getSubreasonSelected(reason)?.checkboxFormControlName || null
              "
              [label]="getSubreasonSelected(reason)?.checkbox || '' | translate"
              [id]="
                'declaration-' +
                getSubreasonSelected(reason)?.checkboxFormControlName
              "
              [tooltip]="false"
            ></se-checkbox>
          </div>
          <div
            *ngIf="!!getSubreasonSelected(reason)?.descripcioObligatoria"
            class="mb--3"
          >
            <hr />
            <se-textarea
              [id]="getSubreasonSelected(reason)?.freeTextFromControlName || ''"
              [formControlName]="
                getSubreasonSelected(reason)?.freeTextFromControlName || ''
              "
              [maxlength]="getSubreasonSelected(reason)?.descripcioMax || 1000"
              [minlength]="getSubreasonSelected(reason)?.descripcioMin || 25"
              [label]="
                'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.SUBREASON_FREE_TEXT_SUBTITLE'
                  | translate
                    : {
                        minCharacters:
                          getSubreasonSelected(reason)?.descripcioMin || 25,
                        maxCharacters:
                          getSubreasonSelected(reason)?.descripcioMax || 1000,
                      }
              "
              [readonly]="false"
            >
            </se-textarea>
          </div>
        </div>
      </se-accordion>
    </div>
  </form>
</se-panel>
<div
  [ngClass]="{
    'mt-4': documentsSelected.length > 0,
    'd-none': documentsSelected.length === 0,
  }"
>
  <!-- documentacion a aportar -->
  <mf-documents-upload-files
    *axLazyElement
    [id]="idTramit + functionalModule"
    [statusesToExclude]="statusesToExclude"
    [hasActions]="true"
    [panelMode]="true"
    [sigedaDescriptions]="documentsSigedaDescriptions"
    [key]="'0'"
    [idFunctionalModule]="functionalModule"
    [idEntity]="idTramit"
    [tableColumns]="getDocumentsTableColumns()"
    [required]="isRequiredDocuments()"
    [modalTableColumns]="getModalTableColumns()"
    [accept]="getAcceptedFiles()"
    [infoModalAlert]="infoModalAlert"
    [title]="
      documentSectionTitle || 'SE_PADRO_CO2.ALLEGATIONS.DOCUMENTS.TITLE'
        | translate
    "
    [panelDescription]="panelDescriptionDocuments"
    [dropAreaTitlePreLinkText]="dropAreaTitlePreLinkText"
    [dropAreaTitleLinkText]="dropAreaTitleLinkText"
    [dropAreaTitlePostLinkText]="dropAreaTitlePostLinkText"
    [fileFormatSeparation]="fileFormatSeparation"
    [multiple]="false"
    [documentTemplates]="documentTemplates"
    [maxFiles]="getMaxFiles()"
    [deleteFileByDocId$]="deleteFileByDocId$"
    (addedFiles)="onAllegationsAddedFiles($event)"
    [sizeLimitPerFile]="getAllowedFileSize()"
  >
  </mf-documents-upload-files>
</div>
