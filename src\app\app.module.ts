import { LazyElementsModule } from '@angular-extensions/elements';
import {
  APP_INITIALIZER,
  ApplicationRef,
  DoBootstrap,
  Injector,
  LOCALE_ID,
  NgModule,
  isDevMode,
} from '@angular/core';
import { BrowserModule } from '@angular/platform-browser';

import { AppRoutingModule } from './app-routing.module';
import { AppComponent } from './app.component';

import { DOCUMENT, registerLocaleData } from '@angular/common';
import {
  HTTP_INTERCEPTORS,
  HttpBackend,
  HttpClientModule,
} from '@angular/common/http';
import localeCa from '@angular/common/locales/ca';
import { createCustomElement } from '@angular/elements';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {
  TranslateLoader,
  TranslateModule,
  TranslateService,
} from '@ngx-translate/core';
import { CookieService } from 'ngx-cookie-service';
import { MultiTranslateHttpLoader } from 'ngx-translate-multi-http-loader';
import { PrimeNGConfig } from 'primeng/api';
import { lastValueFrom, take } from 'rxjs';
import { SeHttpInterceptorService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { DocTableModule } from './modules/doc-table/doc-table.module';
import {
  DocumentActionsComponent,
  DocumentActionsModule,
} from './modules/document-actions';
import {
  DownloadDocumentButtonComponent,
  DownloadDocumentModule,
} from './modules/download-document';
import { ModalSendDocumentationComponent } from './modules/send-documents-button/modal-send-documentation/modal-send-documentation.component';
import { SendDocumentsButtonComponent } from './modules/send-documents-button/send-documents-button.component';
import { SendDocumentsButtonModule } from './modules/send-documents-button/send-documents-button.module';
import {
  UploadFilesComponent,
  UploadFilesModule,
} from './modules/upload-files';

export function HttpLoaderFactory(
  httpBackend: HttpBackend,
): MultiTranslateHttpLoader {
  return new MultiTranslateHttpLoader(httpBackend, [
    { prefix: `${environment.baseUrlMf}/assets/i18n/`, suffix: '.json' },
    { prefix: `${environment.baseUrCommons}/i18n/`, suffix: '.json' },
  ]);
}

export function appInitializerFactory(
  translate: TranslateService,
  document: Document,
  primeNGConfig: PrimeNGConfig,
): () => Promise<unknown> {
  return () => {
    translate.addLangs(['ca', 'es']);
    translate.setDefaultLang('ca');

    const currentLang = window.location.href.includes('/es/') ? 'es' : 'ca';
    document.documentElement.lang = currentLang;

    // Set primeNG specific translations
    translate
      .get('SE_COMPONENTS.PRIMENG')
      .pipe(take(1))
      .subscribe((res) => primeNGConfig.setTranslation(res));

    return lastValueFrom(translate.use(currentLang));
  };
}

registerLocaleData(localeCa, 'ca');

@NgModule({
  declarations: [AppComponent],
  imports: [
    BrowserModule,
    HttpClientModule,
    AppRoutingModule,
    TranslateModule.forRoot({
      loader: {
        provide: TranslateLoader,
        useFactory: HttpLoaderFactory,
        deps: [HttpBackend],
      },
    }),
    LazyElementsModule,
    DocTableModule,
    SendDocumentsButtonModule,
    DownloadDocumentModule,
    DocumentActionsModule,
    UploadFilesModule,
  ],
  providers: [
    { provide: LOCALE_ID, useValue: 'ca-ES' },
    {
      provide: HTTP_INTERCEPTORS,
      useClass: SeHttpInterceptorService,
      multi: true,
    },
    CookieService,
    {
      provide: APP_INITIALIZER,
      useFactory: appInitializerFactory,
      deps: [TranslateService, DOCUMENT, PrimeNGConfig],
      multi: true,
    },
    NgbActiveModal,
  ],
  bootstrap: [],
})
export class AppModule implements DoBootstrap {
  constructor(private injector: Injector) {}

  ngDoBootstrap(appRef: ApplicationRef): void {
    if (isDevMode()) {
      appRef.bootstrap(UploadFilesComponent, 'app-root');
    }

    const elements = [
      {
        tag: 'se-documents',
        component: AppComponent,
      },
      {
        tag: 'mf-documents-upload-files',
        component: UploadFilesComponent,
      },
      {
        tag: 'mf-documents-modal-send-documentation',
        component: ModalSendDocumentationComponent,
      },
      {
        tag: 'mf-documents-send-documents-button',
        component: SendDocumentsButtonComponent,
      },
      {
        tag: 'mf-documents-download-document-button',
        component: DownloadDocumentButtonComponent,
      },
      {
        tag: 'mf-documents-docs-actions',
        component: DocumentActionsComponent,
      },
    ];

    elements.forEach(({ tag, component }) => {
      const ce = createCustomElement(component, {
        injector: this.injector,
      });

      if (!customElements.get(tag)) {
        customElements.define(tag, ce);
      }
    });
  }
}
