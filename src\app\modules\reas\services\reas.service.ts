import { Injectable } from '@angular/core';
import { ContestableActDocument } from '@app/shared/components';
import { VehiclesSelectedInfo } from '@app/shared/models';
import {
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { TranslateService } from '@ngx-translate/core';
import { merge, Observable, take, tap } from 'rxjs';
import {
  Column,
  iDocumentPadoct,
  SeAlertMessage,
  SeAlertType,
  SeModalService,
} from 'se-ui-components-mf-lib';
import {
  DocSigedaCodes,
  DocSubtypeCodes,
} from '@app/shared/components/allegations-free-text';
import {
  MODAL_TABLE_COLUMNS_DOC_TYPE_ID_OPTION_VALUE,
  TABLE_COLUMNS_DOC_TYPE_FIRST_ELEMENT,
} from '@app/shared/models/upload-documents.model';

@Injectable({
  providedIn: 'root',
})
export class ReasService {
  constructor(
    private storageData: StorageService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private loginSrv: LoginResponseService,
    private modalService: SeModalService,
    private translateService: TranslateService,
  ) {}

  getVehiclesSelectedInfo(nifTitular: string): VehiclesSelectedInfo | null {
    const recursResponse = this.storageData.getReasVehicles();

    if (
      !recursResponse?.validation?.vehiclesRecurribles ||
      recursResponse.validation.vehiclesRecurribles.length === 0
    ) {
      return null;
    }

    return {
      provisional: this.specificConfigurationSrv.isProvisional,
      matriculas: recursResponse.validation.vehiclesRecurribles.map(
        (vehicle) => vehicle.matricula,
      ),
      exercici: this.specificConfigurationSrv.currentExercise,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
      nifTitular: nifTitular,
    };
  }

  openAlertModal(): Observable<string> {
    const modalRef = this.modalService.openModal({
      severity: 'warning',
      title: this.translateService.instant(
        'SE_PADRO_CO2.REAS_PROCESS.MODAL_IBAN_WARNING.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_PADRO_CO2.REAS_PROCESS.MODAL_IBAN_WARNING.SUBTITLE',
      ),
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'UI_COMPONENTS.BUTTONS.CANCEL',
    });

    const second: Observable<string> =
      modalRef.componentInstance.modalSecondaryButtonEvent;
    const close: Observable<string> =
      modalRef.componentInstance.modalOutputEvent;

    return merge(close, second).pipe(
      take(1),
      tap(() => modalRef.close()),
    );
  }

  openLessThanNCharacterAlertModal(minCharacters: number): Observable<string> {
    const modalRef = this.modalService.openModal({
      severity: 'error',
      title: this.translateService.instant(
        'SE_PADRO_CO2.REAS_PROCESS.MODAL_LESS_N_CHARACTER_WARNING.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_PADRO_CO2.REAS_PROCESS.MODAL_LESS_N_CHARACTER_WARNING.SUBTITLE',
        { minCharacters },
      ),
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CLOSE',
    });

    const close: Observable<string> =
      modalRef.componentInstance.modalOutputEvent;

    return merge(close).pipe(
      take(1),
      tap(() => modalRef.close()),
    );
  }

  getFileTableColumns(): Column[] {
    return [...TABLE_COLUMNS_DOC_TYPE_FIRST_ELEMENT];
  }

  getAllowedFileSize(
    documentsSigedaDescriptions: ContestableActDocument[],
  ): number {
    // return de min value of allowedSize of all selected documents
    return Math.min(
      ...documentsSigedaDescriptions.map((doc) => doc.allowedSize ?? Infinity),
    );
  }

  getAcceptedFileTypes(
    documentsSigedaDescriptions: ContestableActDocument[],
  ): string[] {
    const array = documentsSigedaDescriptions
      .map((document) => document.allowedFiles ?? [])
      .flat();
    return [...new Set(array)]; //remove duplicates
  }

  getFileModalTableColumns(
    documentsSelected: ContestableActDocument[],
    uploadedDocuments: iDocumentPadoct[],
  ): Column[] {
    const modalTableColumnsObject = JSON.parse(
      JSON.stringify(MODAL_TABLE_COLUMNS_DOC_TYPE_ID_OPTION_VALUE),
    );

    const subtypes = uploadedDocuments.map(
      (doc) => doc.codeDescriptionComplementary,
    );

    if (
      modalTableColumnsObject[2]?.cellConfig &&
      modalTableColumnsObject[2]?.cellConfig['options']
    ) {
      modalTableColumnsObject[2].cellConfig['options'] = documentsSelected
        .filter((el) => !subtypes.includes(el.subtype))
        .map((el) => ({
          label: el.description,
          id: el.subtype,
        }));
    }

    return modalTableColumnsObject;
  }

  getAlertMessageDocuments(
    documentsDescriptions: ContestableActDocument[],
  ): SeAlertMessage {
    const titleTranslation = this.translateService.instant(
      'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.DOCUMENTS_ALERT.OPTIONAL',
    );
    const filterButtonTranslation = this.translateService.instant(
      'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.DOCUMENTS_ALERT.FILTER_BUTTON',
    );
    const filterButtonAlternateTranslation = this.translateService.instant(
      'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.DOCUMENTS_ALERT.FILTER_BUTTON_ALTERNATE',
    );

    return {
      title: titleTranslation,
      type: SeAlertType.INFO,
      list: this.getDocumentsListAlert(documentsDescriptions),
      minFilteredListLength: 3,
      filtered: true,
      alertClass: 'padding-normal',
      contentClass: 'flex-column',
      filterButton: {
        label: filterButtonTranslation,
        alternateLabel: filterButtonAlternateTranslation,
        btnTheme: 'trueOnlyText',
      },
    };
  }

  private getDocumentsListAlert(
    documentsDescriptions: ContestableActDocument[],
  ): string[] {
    return documentsDescriptions.map((doc) => doc.description);
  }

  getDocumentType(
    doc: iDocumentPadoct,
    documentsSigedaDescriptions: ContestableActDocument[],
  ): string | undefined {
    if (
      doc.codSigedaType === DocSigedaCodes.RECLAMACIO_JUSTIFICANT &&
      doc.codeDescriptionComplementary ===
        DocSubtypeCodes.RECLAMACIO_JUSTIFICANT
    ) {
      return this.translateService.instant(
        `SE_PADRO_CO2.SIGEDAS.${DocSigedaCodes.RECLAMACIO_JUSTIFICANT}-${DocSubtypeCodes.RECLAMACIO_JUSTIFICANT}`,
      );
    }

    return documentsSigedaDescriptions.find(
      (sigedaDoc) =>
        sigedaDoc.subtype === doc.codeDescriptionComplementary &&
        sigedaDoc.type === doc.codSigedaType,
    )?.description;
  }
}
