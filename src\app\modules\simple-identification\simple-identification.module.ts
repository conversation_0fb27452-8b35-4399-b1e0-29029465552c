import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule, Routes } from '@angular/router';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAlertModule,
  SeButtonModule,
  SeConfirmationMessageModule,
  SeInputModule,
  SePanelModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';
import { SimpleIdentificationComponent } from './simple-identification.component';
import {
  RECAPTCHA_V3_SITE_KEY,
  RecaptchaModule,
  RecaptchaV3Module,
} from 'ng-recaptcha';
import { CaptchaModule } from '@shared/components';

const routes: Routes = [
  {
    path: '',
    component: SimpleIdentificationComponent,
    data: {
      title: 'SE_PADRO_CO2.APP_TITLE',
      isElementVisible: false,
    },
  },
  {
    path: '**',
    redirectTo: '',
  },
];

@NgModule({
  declarations: [SimpleIdentificationComponent],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    RouterModule.forChild(routes),
    SeButtonModule,
    SePanelModule,
    SeAlertModule,
    SeConfirmationMessageModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-seguretat-declaracio-responsable',
          url: environment.mfSeguretatURL,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
    SeInputModule,
    SeAlertModule,
    RecaptchaV3Module,
    RecaptchaModule,
    CaptchaModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  providers: [
    {
      provide: RECAPTCHA_V3_SITE_KEY,
      useValue: environment.captchaPublicKey,
    },
  ],
})
export class SimpleLoginModule {}
