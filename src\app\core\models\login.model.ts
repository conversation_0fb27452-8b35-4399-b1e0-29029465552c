import { LoginUser } from './login-response.model';

export enum IdentificationType {
  NOM_PROPI = 'NOM_PROPI',
  REPRESENTATIVE = 'REPRESENTANT',
  CONVENIAT = 'CONVENI',
  COORDINADOR = 'COORDINADOR',
  CIVIL_SERVANT = 'CIVIL_SERVANT',
  LOGIN_SIMPLE = 'LOGIN_SIMPLE',
  NO_PERMET = 'NO_PERMET',
}

export interface LoginRequest {
  nom?: string;
  nifTitular?: string;
  nifRepresentant?: string;
  tipusAtencio?: string;
  matricula?: string;
  mobil?: string;
  idPersCens?: string;
  idPersTitular?: string;
  pin?: string;
}

export interface Co2User extends LoginUser, LoginRequest {}
