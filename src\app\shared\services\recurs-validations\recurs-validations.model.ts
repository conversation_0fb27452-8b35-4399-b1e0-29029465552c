import { VehicleRecurs, VehiclesSelectedInfo } from '@app/shared/models';
import { Nullable } from 'se-ui-components-mf-lib';
import { SelectedVehiclesValidationResponse } from './recurs-validations-endpoints.model';

export enum RecursValidationTramitType {
  RECURS = 'RECURS',
  REAS = 'REAS',
}

export interface RecursReaValidationMethodInputData {
  vehiclesInfo: Nullable<VehiclesSelectedInfo>;
  advertimentTerminiAcceptat?: Nullable<boolean>;
  skipPreviReasValidation?: Nullable<boolean>;
  nomesVehiclesValids?: Nullable<boolean>;
  csvNotificacio?: Nullable<string>;
}

export interface ValidationModalInputData {
  vehiclesRecurribles?: VehicleRecurs[];
  multiple?: boolean;
  tramit: RecursValidationTramitType;
  response?: Nullable<SelectedVehiclesValidationResponse>;
  showErrorModal?: boolean;
}
