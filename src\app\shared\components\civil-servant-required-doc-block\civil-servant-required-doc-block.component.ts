import {
  Component,
  EventEmitter,
  Input,
  OnD<PERSON>roy,
  OnInit,
  Output,
} from '@angular/core';
import {
  DOCUMENTS_STATUS_TO_EXCLUDE,
  IdentificationType,
} from '@app/core/models';
import { LoginResponseService, StorageService } from '@app/core/services';
import {
  Column,
  FileFormatsSeparation,
  SeButton,
  SeDocumentsService,
  SeModalService,
  UploadAcceptFormats,
} from 'se-ui-components-mf-lib';
import { ContestableActDocument } from '../allegations-options';
import {
  CIVIL_SERVANT_DOC_SIZE_LIMIT,
  PaymentType,
  SIGEDA_DESCRIPTION,
  SIGN_DOCUMENT_CALL_INTERVAL,
  SignDocumentRequest,
  SignDocumentRequestResponse,
  SignDocumentStatus,
  SignError,
  SignErrorCode,
} from './models/civil-servant-required-doc-block.model';
import { TranslateService } from '@ngx-translate/core';
import {
  MODAL_TABLE_COLUMNS,
  TABLE_COLUMNS_DOC_TYPE_FIRST_ELEMENT,
} from '@app/shared/models/upload-documents.model';
import { CivilServantRequiredDocBlockEndpointService } from './services/civil-servant-required-doc-block-endpoints.service';
import { Subject, switchMap, take, takeUntil } from 'rxjs';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-civil-servant-required-doc-block',
  template: `
    <se-panel
      *ngIf="showDocBlock"
      [id]="'civil-servant-required-doc-block'"
      [title]="
        'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.TITLE' | translate
      "
      [customActions]="customButtons"
    >
      <p>
        {{
          'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.SUBTITLE_1'
            | translate
        }}
      </p>
      <p>
        {{
          'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.SUBTITLE_2'
            | translate
        }}
      </p>
      <mf-documents-upload-files
        *axLazyElement
        [id]="'civil-servant-required-doc'"
        [hasActions]="true"
        [key]="0"
        [statusesToExclude]="statusesToExclude"
        [sigedaDescriptions]="sigedaDescriptions"
        [idFunctionalModule]="idFunctionalModule"
        [tableColumns]="tableColumns"
        [modalTableColumns]="modalTableColumns"
        [accept]="documentsAcceptedTypes"
        [idEntity]="idEntity"
        [fileFormatSeparation]="fileFormatSeparation"
        [hasModal]="true"
        [multiple]="false"
        [required]="true"
        [maxFiles]="1"
        [groupSizeLimit]="documentsSizeLimit"
        [sizeLimitPerFile]="documentsSizeLimit"
        [title]="''"
        [subtitleText]="
          'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.DOC_SECTION.SUBTITLE'
            | translate: { format, fileSize }
        "
        [loadFiles$]="loadFiles$"
        [dropAreaTitlePreLinkText]="
          'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.DOC_SECTION.PRELINK_TEXT'
            | translate
        "
        [dropAreaTitleLinkText]="
          'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.DOC_SECTION.LINK_TEXT'
            | translate
        "
        (addedFiles)="onAddedFiles($event)"
      ></mf-documents-upload-files>
    </se-panel>

    <ng-template #customButtons>
      <se-button
        [btnTheme]="'secondary'"
        [size]="'small'"
        [disabled]="docAdded"
        (click)="onManualSignature()"
      >
        {{
          'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.BUTTONS.MANUAL_SIGNATURE'
            | translate
        }}
      </se-button>
      <se-button
        [btnTheme]="'primary'"
        [size]="'small'"
        [disabled]="docAdded"
        (click)="onTableSignature()"
      >
        {{
          'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.BUTTONS.TABLET_SIGNATURE'
            | translate
        }}
      </se-button>
    </ng-template>
  `,
})
export class CivilServantRequiredDocBlockComponent
  implements OnInit, OnDestroy
{
  @Input({ required: true }) idEntity: string = '';
  @Input({ required: true }) idFunctionalModule: string = '';
  @Input() checkPaymentData: boolean = false;
  @Input() accountNumber: string = '';
  @Input() accountNumberFormValid: boolean = false;
  @Input() paymentType?: PaymentType;
  @Input() plates: string[] = [];
  @Input() authAlta: boolean = false;
  @Input() authPagament: boolean = false;
  @Input() authSuport: boolean = false;
  @Input() authDomiciliacio: boolean = false;
  @Input() authAjornament: boolean = false;
  @Input() authNotificacio: boolean = false;
  @Input() authAlegacio: boolean = false;
  @Input() declaracioAjornament: boolean = false;

  @Output() disableContinueButton: EventEmitter<boolean> =
    new EventEmitter<boolean>();

  documentsAcceptedTypes: string[] = [
    UploadAcceptFormats.pdf,
    UploadAcceptFormats.jpg,
    UploadAcceptFormats.doc,
    UploadAcceptFormats.docx,
  ];
  documentsSizeLimit: number = CIVIL_SERVANT_DOC_SIZE_LIMIT;
  docAdded: boolean = false;

  format: string = '';
  fileSize: string = '';

  statusesToExclude: string[] = DOCUMENTS_STATUS_TO_EXCLUDE;

  get showDocBlock(): boolean {
    return !!(
      this.storeSrv.profileUser === IdentificationType.CIVIL_SERVANT &&
      this.storeSrv.isInPersonCivilServant
    );
  }

  get tableColumns(): Column[] {
    return [...TABLE_COLUMNS_DOC_TYPE_FIRST_ELEMENT];
  }

  get modalTableColumns(): Column[] {
    const modalTableColumnsObject = JSON.parse(
      JSON.stringify(MODAL_TABLE_COLUMNS),
    );
    if (
      modalTableColumnsObject[2]?.cellConfig &&
      modalTableColumnsObject[2]?.cellConfig['options']
    ) {
      modalTableColumnsObject[2].cellConfig['options'] =
        this.sigedaDescriptions;
    }
    return modalTableColumnsObject;
  }

  get sigedaDescriptions(): ContestableActDocument[] {
    return SIGEDA_DESCRIPTION.map((doc) => {
      return {
        ...doc,
        description: this.translateService.instant(
          'SE_PADRO_CO2.SIGEDAS.TD20-025',
        ),
      };
    });
  }

  fileFormatSeparation: FileFormatsSeparation = FileFormatsSeparation.COMMA;

  private _unsubscribe: Subject<void> = new Subject<void>();

  loadFiles$: Subject<void> = new Subject<void>();

  constructor(
    private storeSrv: StorageService,
    private translateService: TranslateService,
    private endpointService: CivilServantRequiredDocBlockEndpointService,
    private loginSrv: LoginResponseService,
    private documentService: SeDocumentsService,
    private modalService: SeModalService,
  ) {}

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
  }

  ngOnInit(): void {
    this.format = this.documentService.setCommaFileFormat(
      this.documentsAcceptedTypes,
    );

    this.setFileSize();

    this.disableContinueButton.emit(this.showDocBlock);
  }

  private setFileSize(): void {
    this.fileSize = (this.documentsSizeLimit / 1000).toFixed(0);
  }

  onAddedFiles(event: Event): void {
    const doc = (event as CustomEvent).detail || [];
    this.docAdded = !!doc.length;

    this.disableContinueButton.emit(!this.docAdded);
  }

  onTableSignature(): void {
    if (!this.verifyIbanAndPaymentType()) {
      this.openWarningIbanModal();
      return;
    }

    this.signDocumentOnTablet();
  }

  private verifyIbanAndPaymentType(): boolean {
    return (
      !this.checkPaymentData ||
      (!!this.paymentType &&
        ((this.paymentType === PaymentType.CUENTA &&
          !!this.accountNumber &&
          this.accountNumberFormValid) ||
          this.paymentType !== PaymentType.CUENTA))
    );
  }

  private openWarningIbanModal(): void {
    const modalWarningRef: NgbModalRef = this.modalService.openModal({
      severity: 'warning',
      title:
        'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.WARNING_IBAN_MODAL.TITLE',
      subtitle:
        'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.WARNING_IBAN_MODAL.MESSAGE',
      closable: true,
    });

    modalWarningRef.componentInstance.modalOutputEvent
      .pipe(take(1))
      .subscribe(() => {
        modalWarningRef.close();
      });
  }

  private signDocumentOnTablet(): void {
    const progressModalRef: NgbModalRef = this.generateProgressModalRef();

    this.endpointService
      .signDocument(this.setSignDocumentRequest(true), false)
      .pipe(takeUntil(this._unsubscribe))
      .subscribe({
        next: (response: SignDocumentRequestResponse) => {
          if (
            this.isTabletSignatureResponseHandled(response, progressModalRef)
          ) {
            return;
          }

          this.handleProgressModalInterval(
            progressModalRef,
            response?.content?.id,
          );
        },
      });
  }

  private isTabletSignatureResponseHandled(
    response: SignDocumentRequestResponse,
    progressModalRef: NgbModalRef,
  ): boolean {
    if (
      response.messages &&
      response.messages?.length > 0 &&
      response.messages[0]?.subtitle
    ) {
      this.openErrorModal({
        code: response.messages[0].subtitle,
        description: response.messages[0].subtitle,
      });
      progressModalRef.close();
      return true;
    }

    if (response?.content?.error && response?.content?.errorList?.length > 0) {
      this.openErrorModal(response.content.errorList[0]);
      progressModalRef.close();
      return true;
    }

    if (response?.content?.status === SignDocumentStatus.SIGNAT) {
      this.loadFiles$.next();
      progressModalRef.close();
      return true;
    }

    if (response.content.status === SignDocumentStatus.PENDING_SIGN) {
      progressModalRef.componentInstance.message =
        'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.PROGRESS_MODAL.SIGNATURE_IN_PROGRESS_MSG';
    }

    return false;
  }

  private generateProgressModalRef(): NgbModalRef {
    const cancelButton: SeButton = {
      label: this.translateService.instant(
        'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.PROGRESS_MODAL.CANCEL_SIGNATURE',
      ),
      btnTheme: 'secondary',
      size: 'small',
    };
    const progressModalRef = this.modalService.openProgressModal(
      SIGN_DOCUMENT_CALL_INTERVAL,
      'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.PROGRESS_MODAL.SEND_DOCUMENT_TO_SIGN_MSG',
      undefined,
      undefined,
      cancelButton,
    );

    progressModalRef.componentInstance.onCustomButton
      .pipe(takeUntil(this._unsubscribe))
      .subscribe(() => {
        this.handleCancelProgressModalResponse(progressModalRef);
      });

    return progressModalRef;
  }

  private handleCancelProgressModalResponse(
    progressModalRef: NgbModalRef,
  ): void {
    this._unsubscribe.next();
    progressModalRef.close();
  }

  private handleProgressModalInterval(
    progressModalRef: NgbModalRef,
    docId: string,
  ): void {
    progressModalRef.componentInstance.intervalOutput
      .pipe(
        takeUntil(this._unsubscribe),
        switchMap(() => this.endpointService.checkDocumentStatus(docId)),
      )
      .subscribe({
        next: (response: SignDocumentRequestResponse) => {
          if (
            this.isTabletSignatureResponseHandled(response, progressModalRef)
          ) {
            return;
          }

          return;
        },
        error: () => progressModalRef.close(),
        complete: () => progressModalRef.close(),
      });
  }

  private openErrorModal(error: SignError): void {
    const { errorTitleBase, errorMessageBase } = this.getErrorTitleMsg(error);

    const modalErrorRef: NgbModalRef = this.modalService.openModal({
      severity: 'error',
      title: errorTitleBase,
      subtitle: errorMessageBase,
      closable: true,
    });

    modalErrorRef.componentInstance.modalOutputEvent
      .pipe(take(1))
      .subscribe(() => {
        modalErrorRef.close();
        this.scrollToDocumentArea();
      });
  }

  private getErrorTitleMsg(error: SignError): {
    errorTitleBase: string;
    errorMessageBase: string;
  } {
    let errorTitle =
      'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.ERRORS.TITLE';
    let errorMessage =
      'SE_PADRO_CO2.PUBLIC_SERVANT.REQUIRED_DOC_BLOCK.ERRORS.MESSAGE';

    switch (error.code) {
      case SignErrorCode.REBUTJAT:
        errorTitle += '.REBUTJAT';
        errorMessage += '.REBUTJAT';
        break;
      case SignErrorCode.GENERATE_DOCUMENT_ERROR:
        errorTitle += '.GENERATE_DOCUMENT_ERROR';
        errorMessage += '.GENERATE_DOCUMENT_ERROR';
        break;
      default:
        errorTitle += '.DEFAULT';
        errorMessage += '.DEFAULT';
        break;
    }

    errorTitle = this.translateService.instant(errorTitle);
    errorMessage = this.translateService.instant(errorMessage);

    return { errorTitleBase: errorTitle, errorMessageBase: errorMessage };
  }

  private scrollToDocumentArea(): void {
    const divElement: HTMLElement = document.getElementById(
      'civil-servant-required-doc-block',
    ) as HTMLElement;
    if (divElement) {
      divElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  }

  onManualSignature(): void {
    if (!this.verifyIbanAndPaymentType()) {
      this.openWarningIbanModal();
      return;
    }

    this.endpointService
      .signDocument(this.setSignDocumentRequest(false))
      .pipe(takeUntil(this._unsubscribe))
      .subscribe({
        next: (response) => {
          this.handleManualSignatureResponse(response);
        },
      });
  }

  private handleManualSignatureResponse(
    response: SignDocumentRequestResponse,
  ): void {
    if (
      response.messages &&
      response?.messages?.length > 0 &&
      response.messages[0]?.subtitle
    ) {
      this.openErrorModal({
        code: response.messages[0].subtitle,
        description: response.messages[0].subtitle,
      });

      return;
    }

    if (response?.content?.error && response?.content?.errorList?.length > 0) {
      this.openErrorModal(response.content.errorList[0]);

      return;
    }

    if (
      response?.content?.base64File &&
      response?.content?.format &&
      response?.content?.nom
    ) {
      this.documentService.openFile(
        response.content.base64File,
        response.content.format,
        response.content.nom,
      );
    }
  }

  private setSignDocumentRequest(sendToSign: boolean): SignDocumentRequest {
    return {
      idTramit: this.idEntity,
      sendToSign: sendToSign,
      idFuncionalModule: this.idFunctionalModule,
      nif: this.loginSrv.user.nifTitular || '',
      repNif: this.loginSrv.user.nifRepresentant || '',
      matricules: this.plates,
      authAlta: this.authAlta,
      authPagament: this.authPagament,
      authSuport: this.authSuport,
      authDomiciliacio: this.authDomiciliacio,
      authAjornament: this.authAjornament,
      authNotificacio: this.authNotificacio,
      authAlegacio: this.authAlegacio,
      declaracioAjornament: this.declaracioAjornament,
      numCompte: this.accountNumber,
    };
  }
}
