import {
  ChangeDetectorRef,
  Component,
  EventEmitter,
  Input,
  OnDestroy,
  OnInit,
  Output,
} from '@angular/core';
import { FormGroup } from '@angular/forms';
import {
  ContestableActDocument,
  Reasons,
  ReasonsFormData,
  SubReasons,
} from '@shared/components/allegations-options/allegations-options.model';
import {
  Column,
  FileFormatsSeparation,
  iDocumentPadoct,
  Nullable,
  SeAlertMessage,
  SeDeviceService,
} from 'se-ui-components-mf-lib';
import { StorageService } from '@core/services';
import { ReasonsFormValue } from '@modules/allegations/reason-allegation/reason-allegation.model';
import { AllegationsDocumentsService } from './allegations-documents/allegations-documents.service';
import {
  Subject,
  Subscription,
  distinctUntilChanged,
  pairwise,
  takeUntil,
} from 'rxjs';
import { AllegationsOptionsService } from './allegations-options.service';
import { TranslateService } from '@ngx-translate/core';
import { TemplateFile } from './allegations-documents';
import { DOCUMENTS_STATUS_TO_EXCLUDE } from '@app/core/models';

const enum FORM_STATUS {
  VALID = 'VALID',
  INVALID = 'INVALID',
}

const ALTRES = 12;

@Component({
  selector: 'app-allegations-options',
  templateUrl: './allegations-options.component.html',
  styles: [
    `
      .mb--3 {
        margin-bottom: -1rem;
      }
    `,
  ],
})
export class AllegationsOptionsComponent implements OnInit, OnDestroy {
  @Input({ required: true }) title!: string;
  @Input() subtitle: string = '';

  @Input() panelDescriptionDocuments: string = '';

  @Input({ required: true }) set reasonsData(reasonsData: Reasons[]) {
    this.reasons = this.allegationsOptsService.getReasonsFormated(reasonsData);
    this.allegationsOptsService.addAllegationControlAndAppendItToList(
      reasonsData,
    );
  }
  @Input({ required: true }) set documentsSigedaDescriptions(
    value: ContestableActDocument[],
  ) {
    this.allegationsDocService.setDocumentsSigedaDescriptions(value);
  }

  get documentsSigedaDescriptions(): ContestableActDocument[] {
    return this.allegationsDocService.documentsSigedaDescriptions;
  }

  @Input({ required: true }) functionalModule!: string;

  @Input() idTramit: Nullable<string>;
  @Input() dropAreaTitlePreLinkText: string | undefined;
  @Input() dropAreaTitleLinkText: string | undefined;
  @Input() dropAreaTitlePostLinkText: string | undefined;
  @Input() documentSectionTitle: string | undefined;
  @Output() allegationsOptionsData: EventEmitter<ReasonsFormData> =
    new EventEmitter<ReasonsFormData>();

  reasons: Reasons[] = [];
  reasonsSelected: Reasons[] = [];

  allegationsDocuments: iDocumentPadoct[] = [];

  documentsSelected: ContestableActDocument[] = [];
  deleteFileByDocId$: Subject<string> = new Subject();
  documentTemplates: TemplateFile[] = [];

  fileFormatSeparation: FileFormatsSeparation = FileFormatsSeparation.COMMA;
  statusesToExclude: string[] = DOCUMENTS_STATUS_TO_EXCLUDE;

  get reasonsForm(): FormGroup {
    return this.allegationsOptsService.reasonsForm;
  }

  infoModalAlert: SeAlertMessage | undefined;

  private unsubscribe: Subject<void> = new Subject();
  private subreasonsSubscriptions: Subscription[] = [];

  constructor(
    private allegationsDocService: AllegationsDocumentsService,
    private allegationsOptsService: AllegationsOptionsService,
    private storageData: StorageService,
    private translate: TranslateService,
    private cdr: ChangeDetectorRef,
    protected readonly deviceService: SeDeviceService,
  ) {
    this.allegationsOptsService.initForm();
  }

  ngOnInit(): void {
    const reasonsSelected = this.storageData.getReasonsSelected();
    if (reasonsSelected) {
      this.setValuesOnForm(reasonsSelected);
    }

    this.reasonsForm.statusChanges
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((status) => {
        if (status === FORM_STATUS.VALID || status === FORM_STATUS.INVALID) {
          this.emitAllegationsOptionsData();
        }
      });
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
    this.deleteFileByDocId$.complete();
  }

  /*------------ FORM CONTROL METHODS ------------*/
  private setValuesOnForm(formValues: ReasonsFormValue): void {
    this.reasonsForm.patchValue(formValues);

    Object.entries(formValues)
      .filter(([, value]) => value !== null && value !== false)
      .forEach(([key, value]) => {
        const reasonId = Number(key.split('_')[1]);

        if (key.startsWith('reason_')) {
          const reason = this.getReasonById(reasonId);
          if (reason) this.onReasonSelected(true, reason);
        } else if (key.startsWith('subreason_')) {
          const subId = Number(value);
          this.onSubreasonSelected(subId, reasonId, true);
        }
      });
  }

  private onValueChangesSubreason(reasonId: number, selected: boolean): void {
    if (!selected) {
      this.subreasonsSubscriptions[reasonId]?.unsubscribe();
      return;
    }

    const subs = this.reasonsForm
      .get(`subreason_${reasonId}`)
      ?.valueChanges.pipe(
        takeUntil(this.unsubscribe),
        distinctUntilChanged(),
        pairwise(),
      )
      .subscribe(async ([prev, next]: [number | null, number | null]) => {
        if (prev) {
          const nextDocs = this.getSubreasonById(reasonId, next!).documents;
          const prevDocs = this.getSubreasonById(reasonId, prev).documents;
          const differenceList =
            this.allegationsDocService.getDifferenceDocumentsList(
              nextDocs,
              prevDocs,
            );
          //Check if the previous documents can be removed, and show the modal
          if (
            this.checkIfDocumentsCanBeRemoved(differenceList, reasonId) &&
            (await this.allegationsOptsService.openAlertRemoveDocumentModal())
          ) {
            this.reasonsForm.get(`subreason_${reasonId}`)?.setValue(prev);
            return;
          }

          const prevSubreason = this.getSubreasonById(reasonId, prev);
          this.allegationsOptsService.handleSubreasonCheckboxFreeTextRequiredControl(
            prevSubreason,
            false,
          );

          //Remove de previous documents uploaded
          this.setDocumentsRequired(differenceList, false, reasonId);
          //Check if the next documents are required
          this.setDocumentsRequired(nextDocs, selected, reasonId);
        }
        //Select the new subreason option
        this.onSubreasonSelected(next!, reasonId, true);
      });

    this.subreasonsSubscriptions[reasonId] = subs!;
  }

  /*------------ ON METHODS ------------*/
  // reason selected
  async onReasonSelected(isSelected: boolean, reason: Reasons): Promise<void> {
    const reasonControlName = `reason_${reason.id}`;
    const subreasonControlName = `subreason_${reason.id}`;
    const freeTextControlName = `free-text_${reason.id}`;

    if (isSelected) {
      this.reasonsSelected.push(reason);
    } else {
      if (
        this.checkIfReasonHasDocumentsToBeRemoved(reason) &&
        (await this.allegationsOptsService.openAlertRemoveDocumentModal())
      ) {
        // reset the reason control
        this.reasonsForm.get(reasonControlName)?.setValue(true);
        return;
      }

      this.reasonsSelected = this.reasonsSelected.filter(
        (item) => item.id !== reason.id,
      );
    }

    this.allegationsOptsService.setControlRequired(
      reasonControlName,
      isSelected,
    );

    this.allegationsOptsService.setControlRequired(
      freeTextControlName,
      isSelected && !!reason.descripcioObligatoria,
    );

    if (isSelected && reason.descripcioObligatoria && reason.unic) {
      this.allegationsOptsService.setFreeTextValidations(freeTextControlName);
    }

    //Has submotius
    if (reason.submotius && reason.submotius.length > 0) {
      this.onValueChangesSubreason(reason.id, isSelected);
      this.allegationsOptsService.setControlEnabled(
        subreasonControlName,
        isSelected,
      );

      if (isSelected) {
        this.allegationsOptsService.setControlRequired(
          subreasonControlName,
          true,
        );
      } else {
        //Not selected and have submotius
        this.setDocumentsRequired(
          this.allegationsOptsService.getSubreasonSelected(reason)?.documents ??
            [],
          isSelected,
          reason.id,
        );
        this.allegationsOptsService.resetControl(subreasonControlName);

        this.handleSubreasonsRequiredControls(reason.id, false);
      }
    } else {
      //No submotius
      this.setDocumentsRequired(reason.documents, isSelected, reason.id);
    }

    this.allegationsOptsService.handleExclusiveReason(
      this.reasons,
      reason,
      isSelected,
      this.reasonsSelected.length,
    );
    this.allegationsOptsService.updateFormValidity();
    reason.collapsed = !isSelected;
    this.emitAllegationsOptionsData();
  }

  onAllegationsAddedFiles(event: Event): void {
    const documentsAdded: iDocumentPadoct[] =
      (event as CustomEvent).detail || [];
    //remove document
    if (
      this.allegationsDocService.getIfDocumentIsAddedOrRemove(
        this.allegationsDocuments,
        documentsAdded,
      ) < 0
    ) {
      this.allegationsDocService.removeDocumentControlValue(
        this.allegationsDocuments,
        documentsAdded,
      );
      //add documents
    } else {
      documentsAdded.forEach((doc) => {
        const documentsControlName = `documents_${doc.codeDescriptionComplementary}`;
        //add document
        this.reasonsForm.get(documentsControlName)?.setValue(doc);
      });
    }

    this.allegationsDocuments = documentsAdded;
    this.emitAllegationsOptionsData();
  }

  /*------------ PUBLIC METHODS ------------*/
  getSubreasonSelected = (reason: Reasons): SubReasons | undefined =>
    this.allegationsOptsService.getSubreasonSelected(reason);

  isRequiredDocuments = (): boolean =>
    this.documentsSelected.some((document) => document.required);

  getModalTableColumns = (): Column[] =>
    this.allegationsDocService.getFileModalTableColumns(
      this.documentsSelected,
      this.allegationsDocuments,
    );

  getDocumentsTableColumns = (): Column[] =>
    this.allegationsDocService.getFileTableColumns();

  getAcceptedFiles = (): string[] =>
    this.allegationsDocService.getAcceptedFileTypes();

  getAllowedFileSize = (): number =>
    this.allegationsDocService.getAllowedFileSize();

  getMaxFiles(): number {
    const selectedDocumentsLength = this.documentsSelected.length;
    return selectedDocumentsLength <= 10 ? selectedDocumentsLength : 10;
  }

  getDocumentTemplates(): TemplateFile[] {
    if (this.deviceService.isMobile() || this.reasonsSelected[0]?.id === ALTRES)
      return [];

    return this.documentsSelected.map((doc) => {
      return {
        description: '',
        docType:
          doc.description +
          (!doc.required
            ? this.translate.instant(
                'SE_PADRO_CO2.ALLEGATIONS.DOCUMENTS.OPTIONAL',
              )
            : ''),
        required: doc.required,
        idType: doc.subtype,
      };
    });
  }

  /*------------ PRIVATE METHODS ------------*/
  private onSubreasonSelected(
    subreasonId: number,
    reasonId: number,
    isSelected: boolean,
  ): void {
    const subreason = this.getSubreasonById(reasonId, subreasonId);
    if (!subreason) return;

    this.handleSubreasonsRequiredControls(
      reasonId,
      isSelected && !!subreason.descripcioObligatoria,
    );

    this.setDocumentsRequired(subreason.documents, isSelected, reasonId);
    this.emitAllegationsOptionsData();
  }

  private getDocumentSelectedBySubtype(
    subtype: string,
  ): ContestableActDocument | undefined {
    return this.documentsSelected.find((doc) => doc.subtype === subtype);
  }

  private handleDocumentSelected(
    index: number,
    reasonId: number,
    document: ContestableActDocument,
  ): void {
    const required =
      this.allegationsOptsService.checkIfDocumentIsRequired(
        document,
        reasonId,
        this.reasonsSelected,
      ) || !!document.required;
    this.documentsSelected[index].required = required;
  }

  private handleDocumentDeselected(
    index: number,
    reasonId: number,
    found: ContestableActDocument,
  ): void {
    if (
      this.allegationsOptsService.checkIfDocumentCanBeRemoved(
        found,
        reasonId,
        this.reasonsSelected,
      )
    ) {
      this.removeUploadedDocument(found);
      this.documentsSelected.splice(index, 1);
    } else {
      this.documentsSelected[index].required =
        this.allegationsOptsService.checkIfDocumentIsRequired(
          found,
          reasonId,
          this.reasonsSelected,
        );
    }
  }

  private handleSubreasonsRequiredControls(
    reasonId: number,
    isRequired: boolean,
  ): void {
    const reason = this.getReasonById(reasonId);
    if (!reason) return;

    reason?.submotius?.forEach((subreason) => {
      this.allegationsOptsService.handleSubreasonCheckboxFreeTextRequiredControl(
        subreason,
        isRequired,
      );
    });
  }

  private setDocumentsRequired(
    documents: ContestableActDocument[],
    isSelected: boolean,
    reasonId: number,
  ): void {
    documents.forEach((document) => {
      const found = this.getDocumentSelectedBySubtype(document.subtype);
      let required = false;

      //found document --> edit or remove
      if (found) {
        const index = this.documentsSelected.findIndex(
          (doc) => doc.subtype === found.subtype,
        );
        if (isSelected) {
          this.handleDocumentSelected(index, reasonId, document);
        } else {
          this.handleDocumentDeselected(index, reasonId, found);
        }

        const indexMutatedDocumentsSelected = this.documentsSelected.findIndex(
          (doc) => doc.subtype === found.subtype,
        );
        required =
          !!this.documentsSelected[indexMutatedDocumentsSelected]?.required;

        //not found, add
      } else if (
        !found &&
        isSelected &&
        this.documentsSigedaDescriptions.some(
          (doc) => doc.subtype === document.subtype,
        )
      ) {
        required = !!document.required;
        const newDoc = {
          required,
          ...this.documentsSigedaDescriptions.find(
            (doc) => doc.subtype === document.subtype,
          )!,
        };

        this.documentsSelected.push(newDoc);
      }

      const documentsControlName = `documents_${document.subtype}`;

      if (!isSelected && !required) {
        this.allegationsOptsService.resetControl(documentsControlName);
      } else {
        this.allegationsOptsService.setControlRequired(
          documentsControlName,
          required,
        );
      }
    });

    this.documentTemplates = this.getDocumentTemplates();
    this.setAlertMessageDocuments();
    this.cdr.detectChanges();
  }

  private checkIfReasonHasDocumentsToBeRemoved(reason: Reasons): boolean {
    const reasonDocuments =
      this.allegationsOptsService.getReasonsDocuments(reason);
    return this.checkIfDocumentsCanBeRemoved(reasonDocuments, reason.id);
  }

  private checkIfDocumentsCanBeRemoved(
    docs: ContestableActDocument[],
    reasonId: number,
  ): boolean {
    return docs.some((document) => {
      const found = this.getUploadedDocumentBySubtype(document.subtype);
      if (!found) return false;

      const doc = this.getDocumentSelectedBySubtype(document.subtype);
      return this.allegationsOptsService.checkIfDocumentCanBeRemoved(
        doc,
        reasonId,
        this.reasonsSelected,
      );
    });
  }

  private getUploadedDocumentBySubtype(
    subtype: string,
  ): iDocumentPadoct | undefined {
    return this.allegationsDocuments.find(
      (doc) => doc.codeDescriptionComplementary === subtype,
    );
  }

  private removeUploadedDocument(document: ContestableActDocument): void {
    const found = this.allegationsDocuments.find(
      (doc) =>
        doc.codSigedaType === document.type &&
        doc.codeDescriptionComplementary === document.subtype,
    );
    if (found) {
      this.deleteFileByDocId$.next(found.id);
    }
  }

  private getReasonById(id: number): Reasons | undefined {
    return this.reasons.find((reason) => reason.id === id);
  }

  private getSubreasonById(reasonId: number, subreasonId: number): SubReasons {
    return this.getReasonById(reasonId)?.submotius?.find(
      (subreason) => subreason.id === subreasonId,
    ) as SubReasons;
  }

  private setAlertMessageDocuments(): void {
    this.infoModalAlert = this.allegationsDocService.getAlertMessageDocuments(
      this.documentsSelected,
    );
  }

  private emitAllegationsOptionsData(): void {
    const valid =
      this.allegationsDocuments.length >=
      this.allegationsDocService.getMinDocumentsRequired(this.reasonsSelected);

    const eventData: ReasonsFormData =
      this.allegationsOptsService.getReasonsFormDataEmit(
        this.allegationsDocuments,
        this.reasonsSelected,
        this.documentsSigedaDescriptions,
        valid,
      );

    this.allegationsOptionsData.emit(eventData);
  }
}
