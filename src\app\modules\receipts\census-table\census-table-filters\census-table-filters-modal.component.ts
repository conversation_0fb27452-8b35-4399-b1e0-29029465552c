import { Component, EventEmitter, Input, Output } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { SeModal, SeModalOutputEvents } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-census-table-filters-modal',
  styleUrls: ['./census-table-filters.component.scss'],
  template: ` <se-modal
    [data]="data"
    (modalOutputEvent)="onContinue($event)"
    (modalSecondaryButtonEvent)="closeModal()"
  >
    <app-census-table-filters [filterForm]="filterForm">
    </app-census-table-filters>
  </se-modal>`,
})
export class CensusTableFiltersModalComponent {
  @Input() data: SeModal | undefined;
  @Input() filterForm: FormGroup | undefined;
  @Output() handleApplyFilter: EventEmitter<void> = new EventEmitter<void>();

  constructor(private readonly activatedModalService: NgbActiveModal) {}

  protected onContinue(event: string): void {
    if (event === SeModalOutputEvents.MAIN_ACTION) {
      this.handleApplyFilter.emit();
    }

    this.closeModal();
  }

  protected closeModal(): void {
    this.activatedModalService.close();
  }
}
