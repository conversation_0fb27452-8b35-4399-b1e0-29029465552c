import { Component, Input } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { DetallPadro } from '@shared/models';
import { Column, Nullable, Row, SeModal } from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-tax-period-modal',
  templateUrl: './tax-period-modal.component.html',
  styleUrls: ['./tax-period-modal.component.scss'],
})
export class TaxPeriodModalComponent {
  @Input() data: SeModal | undefined;

  @Input() set carDetail(data: Nullable<DetallPadro>) {
    if (data) {
      this.setRows(data);
    }
  }

  rows: Row[] = [];

  tableColumns: Column[] = [
    {
      header: 'SE_PADRO_CO2.LABELS.FROM',
      tooltip: false,
      size: 20,
      key: 'from',
      cellComponentName: 'defaultCellComponent',
      resizable: true,
      cellConfig: {},
    },
    {
      header: 'SE_PADRO_CO2.LABELS.TO',
      tooltip: false,
      key: 'to',
      cellComponentName: 'defaultCellComponent',
      resizable: true,
      cellConfig: {},
    },
    {
      header: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.DAYS',
      tooltip: false,
      size: 20,
      key: 'days',
      cellComponentName: 'defaultCellComponent',
      resizable: true,
    },
  ];

  total: number = 0;

  constructor(
    private activatedModalService: NgbActiveModal,
    private translateService: TranslateService,
  ) {}

  setRows(detail: DetallPadro): void {
    detail.desDe.forEach((from, index) => {
      this.rows.push({
        data: {
          from: { value: from },
          to: { value: detail.finsA[index] },
          days: {
            value: `${detail.numeroDies[index]} ${this.translateService.instant('SE_PADRO_CO2.DETAIL.LABELS.DAYS')}`,
          },
        },
      });
      this.total += detail.numeroDies[index];
    });
  }

  closeModal(): void {
    this.rows = [];
    this.total = 0;
    this.activatedModalService.close();
  }
}
