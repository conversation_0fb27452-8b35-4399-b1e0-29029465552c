import type { IdentificationType } from '@app/core/models';
import type { SearchResource } from '@app/shared/models';
import type { SeHttpResponse } from 'se-ui-components-mf-lib';

export type DownloadExcelRequestBody = SearchResource<
  {
    tipusAccess?: IdentificationType;
    soloTotal?: boolean;
    soloPendent?: boolean;
    nifTitular?: string;
    idPersTitular?: string;
    quotaDes?: number;
    quotaFins?: number;
    nou?: boolean;
    domicilat?: boolean;
    matricula?: string;
    listMatriculas: string[];
    situacio?: string[];
    exercici?: string;
    provisional?: boolean;
  },
  DownloadExcelFields
>;

export type DownloadExcelRequest = {
  headers?: { fields?: string[] };
  body?: DownloadExcelRequestBody;
};

export type DownloadExcelResponse = SeHttpResponse<{
  base64File: string;
  mimeType: string;
  fileName: string;
}>;

export enum DownloadExcelFields {
  plate = 'matricula',
  car = 'vehicle',
  domiciled = 'domiciliat',
  situation = 'situacio',
  new = 'nou',
  shares = 'quota',
  taxYear = 'exercici',
}
