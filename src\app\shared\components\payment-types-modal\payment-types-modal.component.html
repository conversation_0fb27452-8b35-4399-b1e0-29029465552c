<se-modal
  [data]="data"
  (modalOutputEvent)="goToDomiciled($event)"
  (modalSecondaryButtonEvent)="closeModal()"
>
  <div class="row">
    <div class="col-12 mb-4">
      <span
        [innerHTML]="
          subtitle
            | translate
              : {
                  startDay: paymentStartDay,
                  startMonth: paymentStartMonth,
                  startYear: paymentStartYear,
                  endDay: paymentEndDay,
                  endMonth: paymentEndMonth,
                  endYear: paymentEndYear,
                  modifyAccountEndDay: modifyAccountEndDay,
                  modifyAccountEndMonth: modifyAccountEndMonth,
                  modifyAccountEndYear: modifyAccountEndYear,
                }
        "
      >
      </span>
    </div>

    <ng-container *ngIf="domiciledTitle || domiciledSubtitle">
      <hr />
      <div class="col-12 mb-4">
        <p>
          <strong>
            {{ domiciledTitle | translate }}
          </strong>
        </p>
        <p
          class="my-0"
          [innerHTML]="
            domiciledSubtitle
              | translate
                : {
                    day,
                    month,
                    discount: specificConfigurationService.percentDiscount,
                    paymentDay: paymentEndDay,
                    paymentMonth: paymentEndMonth,
                    paymentYear: paymentEndYear,
                  }
          "
        ></p>
      </div>
    </ng-container>

    <ng-container *ngIf="paymentTitle || paymentSubtitle">
      <hr />
      <div class="col-12 mb-4">
        <p>
          <strong>
            {{
              paymentTitle
                | translate: { day: paymentStartDay, month: paymentStartMonth }
            }}
          </strong>
        </p>
        <span>
          {{
            paymentSubtitle
              | translate
                : {
                    startDay: paymentStartDay,
                    startMonth: paymentStartMonth,
                    endDay: paymentEndDay,
                    endMonth: paymentEndMonth,
                  }
          }}
        </span>
      </div>
    </ng-container>
  </div>
</se-modal>
