import { DocumentsUpload, Motius, Vehicle } from '@app/shared/models';

export interface RequestUpdateResourcesTramit {
  motiu: Motius;
  ibanNumeroCompte?: string;
  declaracioNumeroCompte?: boolean;
  documentNumeroCompte?: DocumentsUpload;
}

export interface ReasResumResponse {
  titular?: string;
  quota?: number;
  ibanNumeroCompte?: string;
  motius?: Motius;
  vehicles?: Vehicle[];
  declaracioNumeroCompte?: boolean;
}

export const MIN_CHARACTER_LENGTH = 25;
