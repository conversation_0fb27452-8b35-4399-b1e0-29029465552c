import { Injectable } from '@angular/core';
import {
  ContestableActDocument,
  Reasons,
  ReasonsFormData,
  SubReasons,
} from './allegations-options.model';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import {
  iDocumentPadoct,
  SeModalOutputEvents,
  SeModalService,
  SeValidations,
} from 'se-ui-components-mf-lib';
import { TranslateService } from '@ngx-translate/core';
import { take, Observable, tap, merge, firstValueFrom } from 'rxjs';
import { StorageService } from '@app/core/services';

@Injectable({
  providedIn: 'root',
})
export class AllegationsOptionsService {
  _reasonsForm: FormGroup = this.fb.group({});

  get reasonsForm(): FormGroup {
    return this._reasonsForm;
  }

  constructor(
    private modalService: SeModalService,
    private translateService: TranslateService,
    private storageData: StorageService,
    private fb: FormBuilder,
  ) {}

  //********** FORM FUNCTIONS **********//
  initForm(): void {
    this._reasonsForm = this.fb.group(
      {},
      {
        validators: this.atLeastOneReasonValidator.bind(this),
      },
    );
  }

  addAllegationControl(controlName: string): void {
    this.reasonsForm.addControl(controlName, new FormControl(null));
  }

  addAllegationControlAndAppendItToList(allegations: Reasons[]): void {
    allegations.forEach((allegation) => {
      this.addAllegationControl(`reason_${allegation.id}`);
      this.addAllegationControl(`free-text_${allegation.id}`);
      this.addAllegationControl(`documents_${allegation.id}`);

      if (allegation.submotius && allegation.submotius.length > 0) {
        this.addAllegationControl(`subreason_${allegation.id}`);
        this.addAllegationControl(`subreason_free-text_${allegation.id}`);
        this.addAllegationControl(`subreason_checkbox_${allegation.id}`);
        this.setControlEnabled(`subreason_${allegation.id}`, false);
      }
    });
  }

  setControlRequired(
    controlName: string,
    isRequired: boolean,
    isRequiredTrue: boolean = false,
    markAsDirtyTouched: boolean = false,
  ): void {
    const control = this.reasonsForm.get(controlName);
    isRequired
      ? control?.setValidators(
          isRequiredTrue ? Validators.requiredTrue : Validators.required,
        )
      : control?.clearValidators();

    if (markAsDirtyTouched) {
      control?.markAsTouched();
      control?.markAsDirty();
    }

    control?.updateValueAndValidity();
  }

  setFreeTextValidations(controlName: string, minLength: number = 25): void {
    const control = this.reasonsForm.get(controlName);
    control?.setValidators([
      SeValidations.listValidations([
        { validator: Validators.required },
        {
          validator: Validators.minLength(minLength),
          translation:
            'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.ERRORS_MIN_LENGTH_TEXTAREA',
          translateParams: { minlength: minLength },
        },
      ]),
    ]);
  }

  setControlEnabled(controlName: string, isEnabled: boolean): void {
    const control = this.reasonsForm.get(controlName);
    isEnabled ? control?.enable() : control?.disable();
  }

  resetControl(controlName: string): void {
    const control = this.reasonsForm.get(controlName);
    control?.reset();
    control?.clearValidators();
    control?.updateValueAndValidity();
  }

  updateFormValidity(): void {
    this.reasonsForm.updateValueAndValidity();
  }

  private atLeastOneReasonValidator(
    form: FormGroup,
  ): { [key: string]: boolean } | null {
    return Object.keys(form.controls).some(
      (name) => name.startsWith('reason_') && form.get(name)?.value,
    )
      ? null
      : { atLeastOneReasonRequired: true };
  }

  //********** REASONS METHODS **********//
  getSubreasonSelected(reason: Reasons): SubReasons | undefined {
    const subreasons = reason.submotius ?? [];

    return subreasons.find(
      (item) => item.id === this.getSubreasonSelectedId(reason.id),
    );
  }

  getSubreasonSelectedId(reasonId: number): number | null {
    return this.reasonsForm.get('subreason_' + reasonId)?.value;
  }

  getReasonsDocuments(reason: Reasons): ContestableActDocument[] {
    return reason.submotius && reason.submotius.length > 0
      ? (this.getSubreasonSelected(reason)?.documents ?? [])
      : reason.documents;
  }

  // Function to get the reasons formated
  getReasonsFormated(allegations: Reasons[]): Reasons[] {
    return allegations.map((allegation) => {
      return this.createReasonsObject(allegation);
    });
  }

  private createReasonsObject(allegation: Reasons): Reasons {
    return {
      ...allegation,
      formControlName: `reason_${allegation.id}`,
      collapsed: true,
      collapsible: this.hasContentReason(allegation),
      submotius: allegation.submotius?.map((submotiu) => {
        return {
          ...submotiu,
          name: `reason_${allegation.id}`,
          formControlName: `subreason_${allegation.id}`,
          checkboxFormControlName: `subreason_checkbox_${allegation.id}`,
          freeTextFromControlName: `subreason_free-text_${allegation.id}`,
        };
      }),
    };
  }

  hasContentReason(reason: Reasons): boolean {
    return (
      !!reason.description ||
      reason.documents?.length > 0 ||
      !!reason.submotius?.length
    );
  }

  //********** OTHER METHODS **********//
  private openAlertModal(): Observable<string> {
    const modalRef = this.modalService.openModal({
      severity: 'warning',
      title: this.translateService.instant(
        'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.MODAL_ALERT_REMOVE_SELECTED.TITLE',
      ),
      subtitle: this.translateService.instant(
        'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.MODAL_ALERT_REMOVE_SELECTED.SUBTITLE',
      ),
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'UI_COMPONENTS.BUTTONS.CANCEL',
    });

    const second: Observable<string> =
      modalRef.componentInstance.modalSecondaryButtonEvent;
    const close: Observable<string> =
      modalRef.componentInstance.modalOutputEvent;

    return merge(close, second).pipe(
      take(1),
      tap(() => modalRef.close()),
    );
  }

  async openAlertRemoveDocumentModal(): Promise<boolean> {
    const result = await firstValueFrom(this.openAlertModal());
    return result !== SeModalOutputEvents.MAIN_ACTION;
  }

  getReasonsFormDataEmit(
    reasonsDocuments: iDocumentPadoct[],
    reasonsList: Reasons[],
    documentsSigedaDescriptions: ContestableActDocument[],
    valid: boolean,
  ): ReasonsFormData {
    const eventData: ReasonsFormData = {
      isReasonsFormValid: this.reasonsForm.valid && valid,
      isFreeTextBlockShown: false,
      reasonsFormValue: this.reasonsForm.getRawValue(),
      reasonsDocuments,
      reasonsList,
      documentsSigedaDescriptions,
    };

    this.storageData.setReasonsSelected(this.reasonsForm.getRawValue());

    return eventData;
  }

  checkIfDocumentCanBeRemoved(
    document: ContestableActDocument | undefined,
    reasonId: number,
    reasonsSelected: Reasons[],
  ): boolean {
    if (!document) return false;

    const otherReasons = reasonsSelected.filter((r) => r.id !== reasonId);

    return (
      otherReasons.length === 0 ||
      !otherReasons.some((reason) => {
        const reasonDocuments = this.getReasonsDocuments(reason);
        //If the document is not in the reason/subreason selected, remove it
        return reasonDocuments.some((doc) => doc.subtype === document.subtype);
      })
    );
  }

  checkIfDocumentIsRequired(
    document: ContestableActDocument | undefined,
    reasonId: number,
    reasonsSelected: Reasons[],
  ): boolean {
    if (!document) return false;

    const otherReasons = reasonsSelected.filter((r) => r.id !== reasonId);

    return otherReasons.some((reason) => {
      const reasonDocuments = this.getReasonsDocuments(reason);
      return reasonDocuments.some(
        (doc) => doc.subtype === document.subtype && !!doc.required,
      );
    });
  }

  handleExclusiveReason(
    reasons: Reasons[],
    selectedReason: Reasons,
    isSelected: boolean,
    totalReasonsSelected: number,
  ): void {
    reasons.forEach((reason) => {
      let enabled: boolean;

      if (isSelected) {
        //selected + exclusive => disable all other reasons
        //selected but not exclusive => enable all other reasons
        enabled = selectedReason.unic
          ? reason.id === selectedReason.id
          : !reason.unic;
        this.setControlEnabled(`reason_${reason.id}`, enabled);
      } else if (totalReasonsSelected === 0) {
        //not any selected => enable all reasons
        this.setControlEnabled(`reason_${reason.id}`, true);
      }
    });
  }

  handleSubreasonCheckboxFreeTextRequiredControl(
    subreason: SubReasons,
    isRequired: boolean,
  ): void {
    if (
      !subreason.checkboxFormControlName ||
      !subreason.freeTextFromControlName
    )
      return;

    if (isRequired) {
      this.setControlRequired(
        subreason?.checkboxFormControlName,
        true,
        true,
        true,
      );
      this.setFreeTextValidations(
        subreason?.freeTextFromControlName,
        subreason?.descripcioMin || 25,
      );

      return;
    }

    this.resetControl(subreason?.checkboxFormControlName);
    this.resetControl(subreason?.freeTextFromControlName);
  }
}
