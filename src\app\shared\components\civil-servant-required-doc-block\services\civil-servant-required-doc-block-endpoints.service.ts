import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import {
  SignDocumentRequest,
  SignDocumentRequestResponse,
} from '../models/civil-servant-required-doc-block.model';

@Injectable({
  providedIn: 'root',
})
export class CivilServantRequiredDocBlockEndpointService {
  constructor(private httpService: SeHttpService) {}

  signDocument(
    request: SignDocumentRequest,
    spinner: boolean = true,
  ): Observable<SignDocumentRequestResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `/autoritzacio/generar`,
      method: 'post',
      body: request,
      spinner,
      suppressErrorMessage: true,
    };

    return this.httpService.post<SignDocumentRequestResponse>(httpRequest);
  }

  checkDocumentStatus(id: string): Observable<SignDocumentRequestResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlDocuments,
      url: `/v2/${id}`,
      method: 'get',
      spinner: false,
      suppressErrorMessage: true,
    };

    return this.httpService.get<SignDocumentRequestResponse>(httpRequest);
  }
}
