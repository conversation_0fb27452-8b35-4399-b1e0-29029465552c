import { BASE_URL_EL_MEU_ESPAI_CO2 } from './../../models/routes.model';
import { Injectable } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';
import { StorageService } from '../storage';
import {
  BASE_URL,
  BASE_URL_SECURED,
  IdentificationType,
} from '@app/core/models';

@Injectable({
  providedIn: 'root',
})
export class CustomRouterService {
  get inElMeuEspai(): boolean {
    return window.location.href.includes('/el-meu-espai-atc');
  }

  constructor(
    private router: Router,
    private storeService: StorageService,
  ) {}

  navigateByBaseUrl(
    route: string,
    extras: NavigationExtras = {},
    reloadOnSamePage: boolean = false,
  ): void {
    const currentRoute = this.getCurrentAppRoute() + '/' + route;

    if (
      this.router.routerState.snapshot.url === '/' + currentRoute &&
      reloadOnSamePage
    ) {
      window.location.href = currentRoute;
    } else {
      this.router.navigate([currentRoute], extras);
    }
  }

  private getCurrentAppRoute(): string {
    return this.storeService.profileUser === IdentificationType.LOGIN_SIMPLE
      ? BASE_URL
      : this.inElMeuEspai
        ? BASE_URL_EL_MEU_ESPAI_CO2
        : BASE_URL_SECURED;
  }
}
