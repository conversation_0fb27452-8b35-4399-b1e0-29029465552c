import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import {
  Column,
  Nullable,
  RequestDownloadFile,
  Row,
  SeDocumentsService,
} from 'se-ui-components-mf-lib';
import {
  CellConfig,
  LinkCallbackReturn,
} from 'se-ui-components-mf-lib/lib/components/table/cells/cells.model';
import { RequestSummary } from '@shared/components';
import {
  DocumentsUpload,
  Motius,
  VehiclesSelectedInfo,
} from '@app/shared/models';
import { ReasResum } from '../models/summary-reas.model';

const BASE_TRANSLATE = 'SE_PADRO_CO2.REAS_PROCESS';

@Injectable({
  providedIn: 'root',
})
export class SummaryReasService {
  constructor(
    private translateService: TranslateService,
    private docService: SeDocumentsService,
  ) {}

  getDocumentRows(allegacion: Nullable<Motius>): Row[] {
    return (
      allegacion?.documents.map((doc) => {
        return this.getRowDocument(doc);
      }) || []
    );
  }

  getRowDocument(doc: DocumentsUpload): Row {
    return {
      data: {
        documentType: {
          value: doc.documentType || '',
        },
        documentDescription: {
          value: doc.description || '',
        },
        documentName: {
          value: '',
          cellComponentName: 'linkCellComponent',
          cellConfig: this.getButtonCellDownloadConfig(doc),
        },
      },
    };
  }

  private getButtonCellDownloadConfig(
    doc: DocumentsUpload | undefined,
  ): CellConfig | undefined {
    return {
      tooltip: true,
      ellipsis: false,
      linkCell: {
        linkConfig: {
          label:
            doc?.filename ??
            this.translateService.instant(`${BASE_TRANSLATE}.TABLE.ZIP`),
          linkTheme: 'secondary',
          iconName: 'matFileDownloadOutline',
          iconPosition: 'right',
          size: 'semibold',
        },
        linkCallback: (): LinkCallbackReturn | Promise<LinkCallbackReturn> => {
          return new Promise(() => {
            this.downloadDocuments(
              [doc?.id || ''],
              doc?.filename ??
                this.translateService.instant(
                  `${BASE_TRANSLATE}.TABLE.DOCUMENT`,
                ),
            );
          });
        },
      },
    };
  }

  downloadDocuments(idsDoc: string[], docName: string): void {
    if (idsDoc.length === 1) {
      const request: RequestDownloadFile = { id: idsDoc[0] };
      this.docService.downloadFile(request, docName);
      return;
    }
    // Si hi ha més d'un document, es descarrega un zip
    this.docService.downloadDocumentsZIP(idsDoc, docName);
  }

  getDocumentColumns(): Column[] {
    return [
      {
        key: 'documentType',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.DOCUMENT_TYPE`,
        ),
        size: 10,
      },
      {
        key: 'documentDescription',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.DOCUMENT_DESCRIPTION`,
        ),
        size: 10,
      },
      {
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.DOCUMENT_NAME`,
        ),
        key: 'documentName',
        size: 10,
        resizable: false,
      },
    ];
  }

  setDataResponseInDataObject(
    content: ReasResum,
    vehiclesInfo: Nullable<VehiclesSelectedInfo>,
    isLoginSimple: boolean,
    showGdprMsg: boolean = true,
  ): RequestSummary | undefined {
    const matricules: string[] =
      content.vehicles?.map((v) => v.matricula) || [];

    if (content.vehicles && vehiclesInfo) {
      return {
        ...(!isLoginSimple ? { titular: content.titular } : {}),
        quota: content.quota,
        showGdprMsg: showGdprMsg,
        exercici: content.exercici,
        singleVehicleText: `${content.vehicles[0].matricula}, ${content.vehicles[0].marca} ${content.vehicles[0].model}`,
        vehicles: {
          matriculas: matricules,
          provisional: vehiclesInfo.provisional,
          exercici: vehiclesInfo.exercici,
          idPersTitular: vehiclesInfo.idPersTitular,
        },
        descripcion: content.motius
          ?.filter((m) => !!m.descripcio)
          .map((m) => m.descripcio)
          .join('<br> '),
      };
    }
    return undefined;
  }
}
