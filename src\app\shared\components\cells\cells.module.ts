import { CommonModule, DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgIcon } from '@ng-icons/core';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeButtonDropdownModule,
  SeButtonModule,
  SeTagModule,
} from 'se-ui-components-mf-lib';
import { BooleanToTextCellComponent } from './boolean-to-text-cell-template.component';
import { IconTextComponent } from './icon-text-cell-template.component';
import { TextTagCellComponent } from './tag-cell-template.component';
import { HideOnCoordinatorDirective } from '@core/directives';

@NgModule({
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    FormsModule,
    ReactiveFormsModule,
    SeButtonDropdownModule,
    SeButtonModule,
    SeTagModule,
    HideOnCoordinatorDirective,
    NgIcon,
  ],
  declarations: [
    TextTagCellComponent,
    BooleanToTextCellComponent,
    IconTextComponent,
  ],
  providers: [DatePipe],
})
export class CellsModule {}
