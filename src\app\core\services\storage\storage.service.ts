import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import {
  IdentificationType,
  DomiciledVehicle,
  ResourceProcessDocument,
} from '@core/models';
import {
  ProcessingNotificationsData,
  Vehicle,
  VehiclesSelectedInfo,
} from '@shared/models';
import {
  CalculateDeferralPaymentResponse,
  CalculatePaymentResponse,
} from '@shared/services';
import { SeDataStorageService, Nullable } from 'se-ui-components-mf-lib';
import { PaymentData } from '@modules/payments-process';
import { ProcessNameToDisplayInPresentationReceipt } from '@modules/presentation-receipt';
import { ReceiptTabs } from '@modules/receipts/models';
import { ReasonsFormValue } from '@modules/allegations/reason-allegation/reason-allegation.model';
import {
  SelectedVehiclesReasValidationResponse,
  SelectedVehiclesValidationResponse,
} from '@shared/services/recurs-validations';
import { NAME_USER_CO2_STORAGE } from '../login-response';
import { FeatureFlagService } from '../feature-flag';
import { RepresentativesData } from '../identifications';
import { formatDate } from '@angular/common';

const PAYMENT_DATA_STORAGE_DATA = 'payment-data-storage-data';
const RESOURCE_PROCESS_STORAGE_DATA = 'resource-process-storage-data';
const VEHICLES_SELECTED_STORAGE_DATA = 'vehicles-selected-storage-data';
const REASONS_SELECTED_STORAGE_DATA = 'reasons-selected-storage-data';
const ID_TRAMIT_PROCESS = 'id-tramit-process';
const TAXPAYER_NAME_STORAGE = 'co2-taxpayer-name';
const TAXPAYER_NIF_STORAGE = 'co2-taxpayer-nif';
const DOMICILIATION_VEHICLES_TO_BE_REGISTERED_STORAGE =
  'co2-domiciliation-vehicles-to-be-registered';
const DOMICILIATION_VEHICLES_TO_BE_UPDATED_STORAGE =
  'co2-domiciliation-vehicles-to-be-updated';
const DOMICILIATION_IBAN_STORAGE = 'co2-domiciliation-iban';
const MODIFY_ACCOUNT_RADIO_BUTTON = 'co2-modify-account-radio-button';
const DOMICILIATION_IBAN_DR_STORAGE = 'co2-domiciliation-iban-dr';
const TAB_SELECTED_RECEIPT_STORAGE = 'co2-tab-selected-receipt';
const DOMICILIATION_IS_UNSUBSCRIBE = 'co2-domiciliation-is-unsubscribe';
const PROCESS_NAME_TO_DISPLAY_PRESENTATION_RECEIPT =
  'co2-process-name-to-display-presentation-receipt';
const VEHICLES_PAYMENT_STORAGE_DATA = 'vehicles-payment-storage-data';
const VEHICLES_RECURS_STORAGE_DATA = 'vehicles-recurs-storage-data';
const VEHICLES_REAS_STORAGE_DATA = 'vehicles-reas-storage-data';
export const PROCESSING_NOTIFICATION_STORAGE_DATA =
  'processing-notification-storage-data';
export const PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA =
  'processing-notification-storage-address-data';
// login
const IS_CIVIL_SERVANT = 'co2-is-civil-servant';
const IS_IN_PERSON_CIVIL_SERVANT = 'co2-is-in-person-civil-servant';
const CIVIL_SERVANT_VEHICLE_NIF_TITULAR =
  'co2-civil-servant-vehicle-nif-titular';
const CIVIL_SERVANT_VEHICLE_NAME_TITULAR =
  'co2-civil-servant-vehicle-name-titular';
const CIVIL_SERVANT_VEHICLE_NIF_REPRESENTANT =
  'co2-civil-servant-vehicle-nif-representant';
const CIVIL_SERVANT_VEHICLE_PHONE = 'co2-civil-servant-vehicle-phone';
const CIVIL_SERVANT_VEHICLE_DATE = 'co2-civil-servant-vehicle-date';
const CIVIL_SERVANT_VEHICLE_HOUR = 'co2-civil-servant-vehicle-hour';
const COORDINADOR_NIF_CONTRIBUENT = 'co2-coordinador-nif-contribuent';
const LIST_REPRESENTATIVES = 'co2-representant-representatives';
const PROFILE_USER = 'co2-profile-user';
const LOGIN_CASE = 'co2-login-case';
const LICENSE_PLATE = 'co2-license-plate';

@Injectable({
  providedIn: 'root',
})
export class StorageService {
  constructor(
    private dataStorageService: SeDataStorageService,
    private featureFlagService: FeatureFlagService,
    @Inject(LOCALE_ID) private locale: string,
  ) {}

  get taxpayerName(): string {
    return this.dataStorageService.getItem(TAXPAYER_NAME_STORAGE);
  }
  set taxpayerName(name: string) {
    this.dataStorageService.setItem(TAXPAYER_NAME_STORAGE, name);
  }

  get taxpayerNIF(): string {
    return this.dataStorageService.getItem(TAXPAYER_NIF_STORAGE);
  }

  set taxpayerNIF(nif: string) {
    this.dataStorageService.setItem(TAXPAYER_NIF_STORAGE, nif);
  }

  get paymentData(): PaymentData {
    return this.dataStorageService.getItem(PAYMENT_DATA_STORAGE_DATA);
  }

  set paymentData(data: PaymentData) {
    this.dataStorageService.setItem(PAYMENT_DATA_STORAGE_DATA, data);
  }

  setVehiclesSelected(data: Nullable<VehiclesSelectedInfo>): void {
    this.dataStorageService.setItem(VEHICLES_SELECTED_STORAGE_DATA, data);
  }

  getVehiclesSelected(): Nullable<VehiclesSelectedInfo> {
    const data = this.dataStorageService.getItem(
      VEHICLES_SELECTED_STORAGE_DATA,
    );

    return data || null;
  }

  setReasonsSelected(data: Nullable<ReasonsFormValue>): void {
    this.dataStorageService.setItem(REASONS_SELECTED_STORAGE_DATA, data);
  }

  getReasonsSelected(): Nullable<ReasonsFormValue> {
    const data = this.dataStorageService.getItem(REASONS_SELECTED_STORAGE_DATA);

    return data || null;
  }

  setRecursVehicles(data: SelectedVehiclesValidationResponse): void {
    this.dataStorageService.setItem(VEHICLES_RECURS_STORAGE_DATA, data);
  }

  getRecursVehicles(): SelectedVehiclesValidationResponse {
    const data = this.dataStorageService.getItem(VEHICLES_RECURS_STORAGE_DATA);

    return data || {};
  }

  setReasVehicles(data: SelectedVehiclesReasValidationResponse): void {
    this.dataStorageService.setItem(VEHICLES_REAS_STORAGE_DATA, data);
  }

  getReasVehicles(): SelectedVehiclesReasValidationResponse {
    const data = this.dataStorageService.getItem(VEHICLES_REAS_STORAGE_DATA);

    return data || {};
  }

  setNotificationsData(data: ProcessingNotificationsData | null): void {
    this.dataStorageService.setItem(PROCESSING_NOTIFICATION_STORAGE_DATA, data);
  }

  getNotificationsData(): ProcessingNotificationsData | null {
    const data = this.dataStorageService.getItem(
      PROCESSING_NOTIFICATION_STORAGE_DATA,
    );

    return data || null;
  }

  deleteNotificationsData(): void {
    this.dataStorageService.deleteItem(PROCESSING_NOTIFICATION_STORAGE_DATA);
    this.dataStorageService.deleteItem(
      PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA,
    );
  }

  setPaymentVehicles(data: CalculatePaymentResponse): void {
    this.dataStorageService.setItem(VEHICLES_PAYMENT_STORAGE_DATA, data);
  }

  getPaymentVehicles(): CalculatePaymentResponse {
    const data = this.dataStorageService.getItem(VEHICLES_PAYMENT_STORAGE_DATA);
    return data || {};
  }

  setDeferralPaymentVehicles(data: CalculateDeferralPaymentResponse): void {
    this.dataStorageService.setItem(VEHICLES_PAYMENT_STORAGE_DATA, data);
  }

  getDeferralPaymentVehicles(): CalculateDeferralPaymentResponse {
    const data = this.dataStorageService.getItem(VEHICLES_PAYMENT_STORAGE_DATA);
    return data || {};
  }

  setIdTramit(idTramit: Nullable<string>): void {
    this.dataStorageService.setItem(ID_TRAMIT_PROCESS, idTramit);
  }

  getIdTramit(): Nullable<string> {
    const data = this.dataStorageService.getItem(ID_TRAMIT_PROCESS);

    return data || null;
  }

  setResourceProcessDocumentData(data: ResourceProcessDocument | null): void {
    this.dataStorageService.setItem(RESOURCE_PROCESS_STORAGE_DATA, data);
  }

  getResourceProcessDocumentData(): Nullable<ResourceProcessDocument> {
    const data = this.dataStorageService.getItem(RESOURCE_PROCESS_STORAGE_DATA);

    return data || null;
  }

  // para flujo de alta de domiciliacion, se guardan los vehiculos del primer step ↓↓
  get vehiclesToBeRegistered(): Nullable<Vehicle[]> {
    return this.dataStorageService.getItem(
      DOMICILIATION_VEHICLES_TO_BE_REGISTERED_STORAGE,
    );
  }

  set vehiclesToBeRegistered(vehicles: Nullable<Vehicle[]>) {
    this.dataStorageService.setItem(
      DOMICILIATION_VEHICLES_TO_BE_REGISTERED_STORAGE,
      vehicles,
    );
  }
  // para flujo de alta de domiciliacion, se guardan los vehiculos del primer step ↑↑

  get vehiclesToBeUpdated(): DomiciledVehicle[] {
    return this.dataStorageService.getItem(
      DOMICILIATION_VEHICLES_TO_BE_UPDATED_STORAGE,
    );
  }

  set vehiclesToBeUpdated(vehicles: DomiciledVehicle[]) {
    this.dataStorageService.setItem(
      DOMICILIATION_VEHICLES_TO_BE_UPDATED_STORAGE,
      vehicles,
    );
  }

  // iban que ingresa el usuario en alta de domiciliacion 2ª step para mostrar en el resumen ↓↓
  get ibanDomiciliation(): string {
    return this.dataStorageService.getItem(DOMICILIATION_IBAN_STORAGE);
  }

  set ibanDomiciliation(iban: string) {
    const formatedIban = iban?.replace(/\s/g, '');
    this.dataStorageService.setItem(DOMICILIATION_IBAN_STORAGE, formatedIban);
  }

  // iban que ingresa el usuario en alta de domiciliacion 2ª step para mostrar en el resumen ↓↓
  get ibanDomiciliationDR(): boolean {
    return this.dataStorageService.getItem(DOMICILIATION_IBAN_DR_STORAGE);
  }

  set ibanDomiciliationDR(value: boolean) {
    this.dataStorageService.setItem(DOMICILIATION_IBAN_DR_STORAGE, value);
  }
  // iban que ingresa el usuario en alta de domiciliacion 2ª step para mostrar en el resumen ↑↑

  get modifyAccountRadioButton(): string {
    return this.dataStorageService.getItem(MODIFY_ACCOUNT_RADIO_BUTTON);
  }

  set modifyAccountRadioButton(value: string) {
    this.dataStorageService.setItem(MODIFY_ACCOUNT_RADIO_BUTTON, value);
  }

  // tab seleccionado de la pantalla receipts, la principal ↓↓
  get tabSelectedInReceiptPage(): ReceiptTabs {
    return this.dataStorageService.getItem(TAB_SELECTED_RECEIPT_STORAGE);
  }

  set tabSelectedInReceiptPage(tab: ReceiptTabs) {
    this.dataStorageService.setItem(TAB_SELECTED_RECEIPT_STORAGE, tab);
  }
  // tab seleccionado de la pantalla receipts, la principal ↑↑

  get isUnsubscribe(): boolean {
    return this.dataStorageService.getItem(DOMICILIATION_IS_UNSUBSCRIBE);
  }

  set isUnsubscribe(value: boolean) {
    this.dataStorageService.setItem(DOMICILIATION_IS_UNSUBSCRIBE, value);
  }

  get processNameToDisplayPresentationReceipt(): ProcessNameToDisplayInPresentationReceipt | null {
    return this.dataStorageService.getItem(
      PROCESS_NAME_TO_DISPLAY_PRESENTATION_RECEIPT,
    );
  }

  set processNameToDisplayPresentationReceipt(
    value: ProcessNameToDisplayInPresentationReceipt | null,
  ) {
    this.dataStorageService.setItem(
      PROCESS_NAME_TO_DISPLAY_PRESENTATION_RECEIPT,
      value,
    );
  }

  get isInPersonCivilServant(): Nullable<boolean> {
    return !!this.dataStorageService.getItem(IS_IN_PERSON_CIVIL_SERVANT);
  }
  set isInPersonCivilServant(value: Nullable<boolean>) {
    this.dataStorageService.setItem(IS_IN_PERSON_CIVIL_SERVANT, value);
  }

  get civilServantVehicleNifTitular(): Nullable<string> {
    return this.dataStorageService.getItem(CIVIL_SERVANT_VEHICLE_NIF_TITULAR);
  }
  set civilServantVehicleNifTitular(value: Nullable<string>) {
    this.dataStorageService.setItem(CIVIL_SERVANT_VEHICLE_NIF_TITULAR, value);
  }

  get civilServantVehicleNameTitular(): Nullable<string> {
    return this.dataStorageService.getItem(CIVIL_SERVANT_VEHICLE_NAME_TITULAR);
  }
  set civilServantVehicleNameTitular(value: Nullable<string>) {
    this.dataStorageService.setItem(CIVIL_SERVANT_VEHICLE_NAME_TITULAR, value);
  }

  get civilServantVehicleNifRepresentant(): Nullable<string> {
    return this.dataStorageService.getItem(
      CIVIL_SERVANT_VEHICLE_NIF_REPRESENTANT,
    );
  }

  set civilServantVehicleNifRepresentant(value: Nullable<string>) {
    this.dataStorageService.setItem(
      CIVIL_SERVANT_VEHICLE_NIF_REPRESENTANT,
      value,
    );
  }

  get civilServantVehiclePhone(): Nullable<string> {
    return this.dataStorageService.getItem(CIVIL_SERVANT_VEHICLE_PHONE);
  }
  set civilServantVehiclePhone(value: Nullable<string>) {
    this.dataStorageService.setItem(CIVIL_SERVANT_VEHICLE_PHONE, value);
  }

  get civilServantVehicleDate(): Nullable<string> {
    return this.dataStorageService.getItem(CIVIL_SERVANT_VEHICLE_DATE);
  }
  set civilServantVehicleDate(value: Nullable<Date>) {
    const date = value ? formatDate(value, 'yyyy-MM-dd', this.locale) : value;
    this.dataStorageService.setItem(CIVIL_SERVANT_VEHICLE_DATE, date);
  }

  get civilServantVehicleHour(): Nullable<string> {
    return this.dataStorageService.getItem(CIVIL_SERVANT_VEHICLE_HOUR);
  }
  set civilServantVehicleHour(value: Nullable<Date>) {
    const hour = value ? formatDate(value, 'HH:mm', this.locale) : value;
    this.dataStorageService.setItem(CIVIL_SERVANT_VEHICLE_HOUR, hour);
  }

  get coordinadorNifContribuent(): Nullable<string> {
    return this.dataStorageService.getItem(COORDINADOR_NIF_CONTRIBUENT);
  }

  set coordinadorNifContribuent(value: Nullable<string>) {
    this.dataStorageService.setItem(COORDINADOR_NIF_CONTRIBUENT, value);
  }

  get listRepresentatives(): Nullable<RepresentativesData[]> {
    return this.dataStorageService.getItem(LIST_REPRESENTATIVES);
  }
  set listRepresentatives(value: Nullable<RepresentativesData[]>) {
    this.dataStorageService.setItem(LIST_REPRESENTATIVES, value);
  }

  // la matricula de inicio de sesion para filtrar la tabla del inicio
  get licensePlate(): Nullable<string> {
    return this.dataStorageService.getItem(LICENSE_PLATE);
  }
  set licensePlate(value: Nullable<string>) {
    this.dataStorageService.setItem(LICENSE_PLATE, value);
  }

  get profileUser(): Nullable<IdentificationType> {
    return this.dataStorageService.getItem(PROFILE_USER);
  }

  set profileUser(value: Nullable<IdentificationType>) {
    this.dataStorageService.setItem(PROFILE_USER, value);
  }

  get loginCase(): Nullable<IdentificationType> {
    return this.dataStorageService.getItem(LOGIN_CASE);
  }

  set loginCase(value: Nullable<IdentificationType>) {
    this.dataStorageService.setItem(LOGIN_CASE, value);
  }

  // CLEAR DATA

  clearIbanDomiciliation(): void {
    this.dataStorageService.deleteItem(DOMICILIATION_IBAN_STORAGE);
  }

  clearIbanDomiciliationDr(): void {
    this.dataStorageService.deleteItem(DOMICILIATION_IBAN_DR_STORAGE);
  }

  clearModifyAccountRadioButton(): void {
    this.dataStorageService.deleteItem(MODIFY_ACCOUNT_RADIO_BUTTON);
  }

  clearIsUnsubscribe(): void {
    this.dataStorageService.deleteItem(DOMICILIATION_IS_UNSUBSCRIBE);
  }

  clearVehiclesToBeUpdated(): void {
    this.dataStorageService.deleteItem(
      DOMICILIATION_VEHICLES_TO_BE_UPDATED_STORAGE,
    );
  }

  clearVehiclesToBeRegistered(): void {
    this.dataStorageService.deleteItem(
      DOMICILIATION_VEHICLES_TO_BE_REGISTERED_STORAGE,
    );
  }

  clearReasonsSelected(): void {
    this.dataStorageService.deleteItem(REASONS_SELECTED_STORAGE_DATA);
  }

  clearResourcesTramitInfo(): void {
    this.dataStorageService.deleteItem(VEHICLES_RECURS_STORAGE_DATA);
  }

  clearResourcesReasTramitInfo(): void {
    this.dataStorageService.deleteItem(VEHICLES_REAS_STORAGE_DATA);
  }

  clearIdTramit(): void {
    this.dataStorageService.deleteItem(ID_TRAMIT_PROCESS);
  }

  clearModificationData(): void {
    this.clearIbanDomiciliation();
    this.clearIbanDomiciliationDr();
    this.clearIsUnsubscribe();
    this.clearVehiclesToBeUpdated();
    this.clearModifyAccountRadioButton();
  }

  clearDirectDebitRegistrationData(): void {
    this.clearIbanDomiciliation();
    this.dataStorageService.deleteItem(
      DOMICILIATION_VEHICLES_TO_BE_REGISTERED_STORAGE,
    );
    this.dataStorageService.deleteItem(RESOURCE_PROCESS_STORAGE_DATA);
  }

  clearAllData(): void {
    this.dataStorageService.deleteItem(TAXPAYER_NAME_STORAGE);
    this.dataStorageService.deleteItem(TAXPAYER_NIF_STORAGE);
    this.dataStorageService.deleteItem(PAYMENT_DATA_STORAGE_DATA);
    this.dataStorageService.deleteItem(VEHICLES_SELECTED_STORAGE_DATA);
    this.dataStorageService.deleteItem(ID_TRAMIT_PROCESS);
    this.dataStorageService.deleteItem(
      DOMICILIATION_VEHICLES_TO_BE_REGISTERED_STORAGE,
    );
    this.dataStorageService.deleteItem(
      DOMICILIATION_VEHICLES_TO_BE_UPDATED_STORAGE,
    );
    this.dataStorageService.deleteItem(DOMICILIATION_IBAN_STORAGE);
    this.dataStorageService.deleteItem(TAB_SELECTED_RECEIPT_STORAGE);
    this.dataStorageService.deleteItem(DOMICILIATION_IS_UNSUBSCRIBE);
    this.dataStorageService.deleteItem(
      PROCESS_NAME_TO_DISPLAY_PRESENTATION_RECEIPT,
    );
    this.dataStorageService.deleteItem(VEHICLES_PAYMENT_STORAGE_DATA);

    //feature flag
    this.featureFlagService.clearModifyDomiciliation();
    this.featureFlagService.clearRegisterDomiciliation();
    this.featureFlagService.clearPedingPayments();

    //user
    this.dataStorageService.deleteItem(NAME_USER_CO2_STORAGE);
    this.dataStorageService.deleteItem(IS_CIVIL_SERVANT);
    this.dataStorageService.deleteItem(IS_IN_PERSON_CIVIL_SERVANT);
    this.dataStorageService.deleteItem(CIVIL_SERVANT_VEHICLE_NIF_REPRESENTANT);
    this.dataStorageService.deleteItem(CIVIL_SERVANT_VEHICLE_NIF_TITULAR);
    this.dataStorageService.deleteItem(CIVIL_SERVANT_VEHICLE_NAME_TITULAR);
    this.dataStorageService.deleteItem(CIVIL_SERVANT_VEHICLE_PHONE);
    this.dataStorageService.deleteItem(CIVIL_SERVANT_VEHICLE_DATE);
    this.dataStorageService.deleteItem(CIVIL_SERVANT_VEHICLE_HOUR);
    this.dataStorageService.deleteItem(COORDINADOR_NIF_CONTRIBUENT);
    this.dataStorageService.deleteItem(LIST_REPRESENTATIVES);
    this.dataStorageService.deleteItem(PROFILE_USER);
    this.dataStorageService.deleteItem(LOGIN_CASE);
    this.dataStorageService.deleteItem(LICENSE_PLATE);

    // Recurs and Reas
    this.clearResourcesTramitInfo();
    this.clearResourcesReasTramitInfo();
  }
}
