import { Component, Input } from '@angular/core';
import { AccountNumberSummary } from './account-number-summary.model';
import { FormControl, FormGroup } from '@angular/forms';
import {
  RequestDownloadFile,
  SeDocumentsService,
} from 'se-ui-components-mf-lib';

@Component({
  selector: 'app-account-number-summary',
  templateUrl: './account-number-summary.component.html',
  styles: [
    `
      .overflow-ellipsis {
        text-overflow: ellipsis;
        overflow: hidden;
      }
    `,
  ],
})
export class AccountNumberSummaryComponent {
  @Input() data: AccountNumberSummary | undefined;
  @Input() title: string = '';

  recursSummaryForm: FormGroup = new FormGroup({
    ibanDr: new FormControl(true),
  });

  constructor(private docService: SeDocumentsService) {}

  downloadDocumentNumeroCompte(): void {
    if (!this.data?.documentNumeroCompte) {
      return;
    }
    this.downloadDocuments(
      [this.data.documentNumeroCompte?.id || ''],
      this.data.documentNumeroCompte?.filename || '',
    );
  }

  downloadDocuments(idsDoc: string[], docName: string): void {
    if (idsDoc.length === 1) {
      const request: RequestDownloadFile = { id: idsDoc[0] };
      this.docService.downloadFile(request, docName);
      return;
    }
    // Si hi ha més d'un document, es descarrega un zip
    this.docService.downloadDocumentsZIP(idsDoc, docName);
  }
}
