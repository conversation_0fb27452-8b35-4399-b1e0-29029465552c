import {
  Component,
  ComponentFactoryResolver,
  ComponentRef,
  EventEmitter,
  Input,
  Output,
  ViewChild,
  ViewContainerRef,
} from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { PrimeNGConfig } from 'primeng/api';
import { Subscription } from 'rxjs';
import {
  WcComponentEvent,
  WcComponentInput,
  iWcComponent,
} from 'se-ui-components-mf-lib';
import { MfActionCourseService } from './core/services/mf-action-course.service';
import { DocTableComponent } from './modules/doc-table/doc-table.component';
import { SendDocumentsButtonComponent } from './modules/send-documents-button/send-documents-button.component';
import { UploadFilesComponent } from './modules/upload-files';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
})
export class AppComponent {
  // Webcomponent > View container reference
  @ViewChild('seDocuments', { static: true, read: ViewContainerRef })
  seDocuments!: ViewContainerRef;

  // Available components paths
  public readonly webComponents: { [x: string]: iWcComponent } = {
    /* ❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗ **ATENCIÓN** ❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗
    ==========================================================
    No utilices esta forma (obsoleta) de definir custom elements. Crea un custom
    element para cada componente que quieras exportar y úsalo en otro micro
    pasándole sus propios atributos de entrada y capturando sus propios eventos
    de salida. Para hacerlo abre el fichero "app.module.ts" y añade tu custom
    element en la lista "elements" de la función "ngDoBootstrap".

    Ejemplo:

    En "se-seguretat-mf" se usan los componentes "scoring" y
    "declaracio-responsable" como custom elements con la directiva
    "*axLazyElement" de la librería "@angular-extensions/elements".
    =========================================================
    ❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗❗ */

    'doc-table': {
      path: 'doc-table',
      component: DocTableComponent,
    },
    'send-documents-button': {
      path: 'send-documents-button',
      component: SendDocumentsButtonComponent,
    },
    'mf-upload-files': {
      path: 'mf-upload-files',
      component: UploadFilesComponent,
    },
  };

  // Loaded module
  public loadedModule!: ComponentRef<DocTableComponent>;

  // WebComponent input data
  @Input() set input(inputData: WcComponentInput) {
    console.log('SE Documents > set input - value: ', inputData);
    console.log(
      'SE Documents > set input - JSON.stringify(value): ',
      JSON.stringify(inputData),
    );

    if (this.seDocuments) {
      this.loadComponent(inputData);
    }
  }
  // WebComponent output
  @Output() output: EventEmitter<WcComponentEvent> =
    new EventEmitter<WcComponentEvent>();

  constructor(
    private translateService: TranslateService,
    private configPrimeNG: PrimeNGConfig,
    private mfActionService: MfActionCourseService,
    public vcRef: ViewContainerRef,
    private resolver: ComponentFactoryResolver,
  ) {
    console.log('Webcomponent: SE Documents > constructor');

    // Set translations
    this.translateService.addLangs(['ca', 'es']);
    this.translateService.setDefaultLang('ca');

    // Webcomponent > Load module language
    this.updateLanguage();
  }

  /**
   * LOAD LANGUAGE
   * @description Load a specific language based on the browser url
   */
  updateLanguage = (): void => {
    if (window.location.href.includes('/es/')) {
      this.translateService.use('es');
    } else {
      this.translateService.use('ca');
    }
  };

  loadComponent = (inputData: WcComponentInput): void => {
    let componentEvents: Subscription = new Subscription();

    // Destroy and unsubscribe from the component
    if (this.loadedModule) {
      this.loadedModule.destroy();
    }

    if (componentEvents) {
      componentEvents.unsubscribe();
    }

    // Save input data
    this.mfActionService.setData(inputData);

    const component = inputData.component;
    const wc = this.webComponents[component!];
    const componentFactory =
      this.resolver.resolveComponentFactory<DocTableComponent>(wc.component);
    this.vcRef.clear();
    this.loadedModule = this.vcRef.createComponent(componentFactory);

    /* Webcomponent output */
    const webComponentInstance = this.loadedModule.instance;

    // if there is any subscription subscribe to it
    if (webComponentInstance.output) {
      componentEvents = webComponentInstance.output.subscribe(
        (componentEvent: WcComponentEvent) => {
          console.log('Webcomponent: Documents - Event: ', componentEvent);
          this.output.emit(componentEvent);
        },
      );
    }
  };
}
