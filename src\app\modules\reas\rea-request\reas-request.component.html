<div class="app-reason-resources pt-3">
  <app-allegations-free-text
    *ngIf="idTramit"
    [procedureId]="idTramit"
    [allegationValue]="allegationFormValue"
    [functionalModule]="functionalModule.REAS"
    (freeTextForm)="onFreeTextAreaChange($event)"
  >
  </app-allegations-free-text>

  <div class="mt-4">
    <mf-documents-upload-files
      *axLazyElement
      [id]="'reas-documents' + idTramit + functionalModule"
      [hasActions]="true"
      [panelMode]="true"
      [statusesToExclude]="statusesToExclude"
      [sigedaDescriptions]="documentsSigedaDescriptions"
      [key]="'0'"
      [idFunctionalModule]="functionalModule.REAS"
      [idEntity]="idTramit"
      [tableColumns]="getDocumentsTableColumns()"
      [required]="false"
      [modalTableColumns]="getModalTableColumns()"
      [accept]="getAcceptedFiles()"
      [infoPanelAlert]="showInfoPanelAlert ? this.infoPanelAlert : undefined"
      [title]="'SE_PADRO_CO2.REAS_PROCESS.DOCUMENTS.TITLE' | translate"
      [panelDescription]="
        'SE_PADRO_CO2.REAS_PROCESS.REASON_SUBTITLE' | translate
      "
      [multiple]="false"
      [fileFormatSeparation]="fileFormatSeparation"
      [maxFiles]="10"
      (addedFiles)="onAllegationsAddedFiles($event)"
      [sizeLimitPerFile]="getAllowedFileSize()"
    >
    </mf-documents-upload-files>
  </div>

  <div class="mt-4">
    <mf-pagaments-dades-bancaries
      *axLazyElement
      [fileFormatSeparation]="fileFormatSeparation"
      [pagamentsData]="bankDetailInput"
      [subtitle]="
        'SE_PADRO_CO2.REAS_PROCESS.NEW_BANK_ACCOUNT_DETAILS' | translate
      "
      [isPanel]="true"
      [collapsible]="false"
      [dropAreaTitlePreLinkText]="
        'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.DOC_SECTION.PRELINK_TEXT'
          | translate
      "
      [dropAreaTitleLinkText]="
        'SE_PADRO_CO2.ALLEGATIONS_FREE_TEXT.DOC_SECTION.LINK_TEXT' | translate
      "
      (onFormChanged)="onBankDetailChange($event)"
    >
    </mf-pagaments-dades-bancaries>
  </div>
  <div class="mt-4">
    <se-alert
      *ngIf="isSuspensioAlertVisible"
      [alertClass]="'suspension-link'"
      [type]="'neutral'"
      [closeButton]="false"
      [title]="'SE_PADRO_CO2.REAS_PROCESS.ALERT_SUSPENSIO.TITLE' | translate"
      [subtitle]="
        'SE_PADRO_CO2.REAS_PROCESS.ALERT_SUSPENSIO.SUBTITLE' | translate
      "
      [subtitleClass]="'fw-normal'"
    >
    </se-alert>
  </div>

  <div
    class="d-flex flex-column row-gap-2 flex-sm-row justify-content-sm-between mt-4"
  >
    <se-button
      type="button"
      btnTheme="secondary"
      (onClick)="onGoBackButtonClick()"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.RETURN_LIST' | translate }}
    </se-button>

    <se-button
      type="submit"
      btnTheme="primary"
      (onClick)="onContinueButtonClick()"
      [disabled]="!reasonsFormData?.isAllegationsFormValid || isIbanInvalid"
    >
      {{ 'SE_PADRO_CO2.BUTTONS.CONTINUE' | translate }}
    </se-button>
  </div>
</div>
