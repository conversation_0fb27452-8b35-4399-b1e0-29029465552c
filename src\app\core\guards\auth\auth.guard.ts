import { inject } from '@angular/core';
import { Router, type CanActivateFn } from '@angular/router';
import { SeAuthService } from 'se-ui-components-mf-lib';

import { BASE_URL, BASE_URL_SECURED } from '../../models';

export const authGuard: CanActivateFn = () => {
  const authService = inject(SeAuthService);
  const router = inject(Router);

  if (authService.getSessionStorageUser()) {
    return true;
  }

  if (window.location.href.includes('secured')) {
    router.navigate([BASE_URL_SECURED]);
  } else {
    router.navigate([BASE_URL]);
  }

  return false;
};
