@import 'bootstrap/scss/functions';
@import 'bootstrap/scss/variables';
@import 'bootstrap/scss/mixins/breakpoints';

:host {
  ::ng-deep {
    th:last-of-type {
      justify-items: end !important;
    }

    // se-dropdown-filter
    .dropdown-header {
      height: 40px;

      .dropdown-title {
        padding: 4px !important;
      }
    }

    se-range-filter {
      .se-dropdown-filter {
        width: 100% !important;
        margin-bottom: 1rem;

        @include media-breakpoint-up(md) {
          width: auto;
        }
      }
    }

    se-dropdown-filter {
      .se-dropdown-filter {
        max-width: initial !important;
        margin-bottom: 1rem;
        width: 100% !important;

        @include media-breakpoint-up(md) {
          max-width: 16rem !important;
        }
      }
    }
  }
}

.search-filter {
  background-color: var(--color-gray-200);
  border: 1px solid var(--color-gray-300);

  &--box {
    width: 100%;

    // @media (min-width: 768px) {
    @include media-breakpoint-up(md) {
      width: 14rem;
    }
  }
}
