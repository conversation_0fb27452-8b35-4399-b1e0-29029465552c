import { Injectable } from '@angular/core';
import { IdentificationType, StatusCodes } from '@app/core/models';
import { StorageService } from '@app/core/services';
import { TagColorService } from '@app/core/services/tag-color';
import { VechilesRowValue } from '@app/shared/components';
import { PadroItem } from '@app/shared/models';
import { TranslateService } from '@ngx-translate/core';
import { MenuItemCommandEvent, MenuItem } from 'primeng/api';
import {
  IconActionsCellConfig,
  Row,
  SeDropdownOption,
  type SeButton,
} from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class PreviousExercisesService {
  constructor(
    private translateService: TranslateService,
    private storageService: StorageService,
    private tagColorService: TagColorService,
  ) {}

  getTableRowPadroData(
    data: PadroItem[],
    situationOptions: SeDropdownOption[],
    goToDetail: (row: VechilesRowValue) => void,
    goToPagament: (row: VechilesRowValue) => void,
  ): Row[] {
    return data?.map((item: PadroItem): Row => {
      return {
        data: {
          plate: {
            value: item.matricula,
            cellConfig: {
              tagCell: {
                tagTheme: this.tagColorService.getPlateTagColor(
                  item.nou,
                  item.modificat,
                ),
              },
              tagValue: this.tagColorService.getPlateTagValue(
                item.nou,
                item.modificat,
              ),
            },
          },
          carType: { value: item.vehicle },
          domiciled: { value: item.domicilat },
          exercise: { value: item.exercici },
          situation: {
            value:
              situationOptions.find((situ) => situ.id === item.codiSituacio)
                ?.label || item.situacio,
            cellConfig: {
              tagCell: {
                tagTheme: this.tagColorService.getStatusCodeColor(
                  item.codiSituacio,
                ),
              },
            },
            cellComponentName: 'tagCellComponent',
          },
          shares: { value: item.quota },
          codiSituacio: { value: item.codiSituacio },
          nou: { value: item.nou },
          modificat: { value: item.modificat },
          idDeute: { value: item.idDeute },
          actions: {
            value: '',
            cellComponentName: 'iconActionsCellComponent',
            cellConfig: this.getActionCellConfig(
              item,
              goToDetail,
              goToPagament,
            ),
          },
        },
      };
    });
  }

  private getActionCellConfig(
    item: PadroItem,
    goToDetail: (row: VechilesRowValue) => void,
    goToPagament: (row: VechilesRowValue) => void,
  ): IconActionsCellConfig {
    const cellConfig: IconActionsCellConfig = {
      ellipsis: true,
      iconActions: {
        icons: [
          {
            title: 'Euro',
            label: 'SE_PADRO_CO2.BUTTONS.PAY',
            name: 'matEuroOutline',
          },
        ],
        button: {
          btnTheme: 'onlyText',
          icon: 'matMoreVertSharp',
          size: 'small',
          ariaLabel: 'SE_PADRO_CO2.BUTTONS.MORE_ACTIONS',
        },
        buttonActions: [
          {
            label: this.translateService.instant('SE_PADRO_CO2.LABELS.DETAIL'),
          },
        ],
      },
    };

    return this.getIconActions(cellConfig, item, goToDetail, goToPagament);
  }

  getIconActions(
    cellConfig: IconActionsCellConfig,
    item: PadroItem,
    goToDetail: (row: VechilesRowValue) => void,
    goToPagament: (row: VechilesRowValue) => void,
  ): IconActionsCellConfig {
    const [payLabel, viewLabel] = Object.values(
      this.translateService.instant([
        'SE_PADRO_CO2.BUTTONS.PAY',
        'SE_PADRO_CO2.BUTTONS.DETAIL',
      ]),
    ) as string[];

    cellConfig.iconActions.icons = [
      {
        title: viewLabel,
        label: viewLabel,
        name: 'matRemoveRedEyeOutline',
        command: (row: VechilesRowValue): void => goToDetail(row),
      },
    ];

    cellConfig.iconActions.buttonActions = [
      {
        label: viewLabel,
        command: (response: MenuItemCommandEvent): void =>
          goToDetail((response?.item as MenuItem)['data'] as VechilesRowValue),
      },
    ];

    //appHideOnCoordinator
    if (this.storageService.profileUser !== IdentificationType.COORDINADOR) {
      if (
        item.codiSituacio === StatusCodes.Pending ||
        item.codiSituacio === StatusCodes.Executive
      ) {
        cellConfig.iconActions.buttonActions.push({
          label: payLabel,
          command: (response: MenuItemCommandEvent): void =>
            goToPagament(
              (response?.item as MenuItem['data'])['data'] as VechilesRowValue,
            ),
        });

        cellConfig.iconActions.icons.push({
          title: payLabel,
          label: payLabel,
          name: 'matEuroOutline',
          command: (row: VechilesRowValue): void => goToPagament(row),
        });
      }
    }

    return cellConfig;
  }

  updateOtherActionsDropdownButtonConfig(
    button: SeButton,
    rows: Row[],
  ): { button: SeButton; actions: MenuItem[] } {
    return {
      button: { ...button, disabled: rows.length === 0 },
      actions: [
        // Afegir noves accions açí ↓ ↓ ↓
        // { label: '', command: (): void => {} },
      ],
    };
  }
}
