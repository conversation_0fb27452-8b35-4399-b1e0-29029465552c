import { StatusCodes } from '@app/core/models';

export interface Vehicle {
  matricula: string;
  marca: string;
  model: string;
  categoria?: string;
  co2?: number;
  quota?: number;
  nou?: boolean;
}

export interface VehiclePayment extends Vehicle {
  exercici: string;
  recarrega: number;
  total: number;
  notificacioPendent: boolean;
  dataFiVoluntaria: string;
}

export interface VehicleRecurs extends Vehicle {
  exercici: string;
  codiSituacio: StatusCodes;
}

export interface VehiclesSelectedInfo {
  provisional: boolean;
  matriculas?: string[];
  vehicles?: VehicleSelected[];
  exercici?: string;
  nifTitular?: string;
  idPersTitular: string;
  tipusAccess?: string;
}

export interface VehicleSelected {
  matricula: string;
  exercici: string;
  idDeute?: string;
  quota?: number;
}
