import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { authGuard } from '@core/guards';
import {
  AppRoutes,
  BASE_URL,
  BASE_URL_EL_MEU_ESPAI_CO2,
  BASE_URL_SECURED,
} from '@core/models';

const childRoutes: Routes = [
  {
    path: AppRoutes.RECEIPTS,
    canActivate: [authGuard],
    loadChildren: () =>
      import(`./modules/receipts/receipts.module`).then(
        (module) => module.ReceiptsModule,
      ),
  },
  {
    path: AppRoutes.DETAIL,
    canActivate: [authGuard],
    data: {
      queryParams: [
        'idPersTitular',
        'tipusAccess',
        'matricula',
        'exercici',
        'provisional',
        'situacio',
      ],
    },
    loadChildren: () =>
      import(`./modules/detail-padro/detail-padro.module`).then(
        (module) => module.DetailPadroModule,
      ),
  },
  {
    path: AppRoutes.PAYMENTS_PROCESS,
    canActivate: [authGuard],
    loadChildren: () =>
      import(`./modules/payments-process/payments-process.module`).then(
        (module) => module.PagamentsModule,
      ),
  },
  {
    path: AppRoutes.DOMICILIATION,
    canActivate: [authGuard],
    loadChildren: () =>
      import(`./modules/domiciliation/domiciliation.module`).then(
        (module) => module.DomiciliationModule,
      ),
  },
  {
    path: AppRoutes.ALLEGATIONS,
    canActivate: [authGuard],
    loadChildren: () =>
      import(`./modules/allegations/allegations.module`).then(
        (module) => module.AllegationsModule,
      ),
  },
  {
    path: AppRoutes.PRESENTATION_RECEIPT,
    canActivate: [authGuard],
    loadChildren: () =>
      import(`./modules/presentation-receipt/presentation-receipt.module`).then(
        (module) => module.PresentationReceiptModule,
      ),
  },
  {
    path: AppRoutes.APPEAL_FOR_RECONSIDERATION,
    canActivate: [authGuard],
    loadChildren: () =>
      import(
        `./modules/appeal-for-reconsideration/appeal-for-reconsideration.module`
      ).then((module) => module.AppealForReconsiderationModule),
  },
  {
    path: AppRoutes.REAS,
    canActivate: [authGuard],
    loadChildren: () =>
      import(`./modules/reas/reas.module`).then((module) => module.ReasModule),
  },
  {
    path: AppRoutes.DEFERRAL_PAYMENTS,
    canActivate: [authGuard],
    loadChildren: () =>
      import(`./modules/deferral-payments/deferral-payments.module`).then(
        (module) => module.DeferralPaymentsModule,
      ),
  },
];

const childSecuredRoutes: Routes = [
  ...childRoutes,
  { path: '', redirectTo: AppRoutes.IDENTIFICATION, pathMatch: 'full' },
  {
    path: 'secured/padro-co2',
    redirectTo: AppRoutes.IDENTIFICATION,
    pathMatch: 'full',
  },
  {
    path: AppRoutes.IDENTIFICATION,
    loadChildren: () =>
      import(`./modules/identifications/identifications.module`).then(
        (module) => module.IdentificationModule,
      ),
  },
  { path: '**', redirectTo: AppRoutes.IDENTIFICATION },
];

// no se puede poner a login simple en el bloque de secured - childRoutes sin login simple
const childRoutesWithLoginSimple: Routes = [
  ...childRoutes,
  { path: '', redirectTo: AppRoutes.LOGIN_SIMPLE, pathMatch: 'full' },
  { path: 'padro-co2', redirectTo: AppRoutes.LOGIN_SIMPLE, pathMatch: 'full' },
  {
    path: AppRoutes.LOGIN_SIMPLE,
    loadChildren: () =>
      import(
        `./modules/simple-identification/simple-identification.module`
      ).then((module) => module.SimpleLoginModule),
  },
  { path: '**', redirectTo: AppRoutes.LOGIN_SIMPLE },
];

const routes: Routes = [
  {
    path: '',
    redirectTo: BASE_URL_SECURED,
    pathMatch: 'full',
  },
  {
    path: BASE_URL_SECURED,
    children: childSecuredRoutes,
  },
  {
    path: BASE_URL,
    children: childRoutesWithLoginSimple,
  },
  {
    path: BASE_URL_EL_MEU_ESPAI_CO2,
    children: childRoutes,
  },
  {
    path: '**',
    redirectTo: BASE_URL_SECURED,
  },
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule],
})
export class AppRoutingModule {}
