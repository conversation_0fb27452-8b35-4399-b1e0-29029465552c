import { Component, EventEmitter, Input, Output } from '@angular/core';
import { SeCheckbox } from 'se-ui-components-mf-lib/lib/components/checkbox/checkbox.model';
import { ReceiptsUtilsService } from '../../receipts-utils.service';
import { FormGroup } from '@angular/forms';
import { DeviceService, SeButton } from 'se-ui-components-mf-lib';
import {
  SituationsStoreService,
  SpecificConfigurationService,
} from '@app/core/services';

@Component({
  selector: 'app-census-table-filters',
  styleUrls: ['./census-table-filters.component.scss'],
  template: `
    <form
      *ngIf="filterForm"
      class="search-filter px-4 pt-3 flex-nowrap"
      [formGroup]="filterForm"
    >
      <div class="row">
        <div *ngIf="!isProvisional" class="col-md-auto col-12">
          <se-dropdown-filter
            [placeholder]="'SE_PADRO_CO2.LABELS.SITUATION' | translate"
            [formControlName]="'situation'"
            [options]="(situationOptions | async) || []"
            [applyButton]="getButtonAttributes(applyButton)"
            [resetButton]="getButtonAttributes(resetButton)"
            (onApply)="onApplyFilter()"
          ></se-dropdown-filter>
        </div>

        <div
          class="col-md-auto col-12"
          [ngClass]="{ 'ps-md-0': !isProvisional }"
        >
          <se-range-filter
            formControlName="rangeFilter"
            [fromInput]="rangeFilter.fromInput"
            [toInput]="rangeFilter.toInput"
            [showClear]="true"
            [closeFilterOnResetButton]="true"
            [applyButton]="getButtonAttributes(rangeFilter.applyButton)"
            [resetButton]="getButtonAttributes(rangeFilter.resetButton)"
            (onApply)="onApplyFilter()"
          ></se-range-filter>
        </div>

        <div class="col-md-auto col-12 ps-md-0">
          <se-dropdown-filter
            [placeholder]="'SE_PADRO_CO2.LABELS.NEW' | translate"
            formControlName="nou"
            [options]="yesOrNoOptions"
            [showSelectedOptions]="true"
            [applyButton]="getButtonAttributes(applyButton)"
            [resetButton]="getButtonAttributes(resetButton)"
            (onApply)="onApplyFilter()"
          ></se-dropdown-filter>
        </div>

        <div class="col-md-auto col-12 ps-md-0">
          <se-dropdown-filter
            formControlName="domiciled"
            [placeholder]="'SE_PADRO_CO2.LABELS.DOMICILED' | translate"
            [options]="yesOrNoOptions"
            [showSelectedOptions]="true"
            [applyButton]="getButtonAttributes(applyButton)"
            [resetButton]="getButtonAttributes(resetButton)"
            (onApply)="onApplyFilter()"
          ></se-dropdown-filter>
        </div>
      </div>
    </form>
  `,
})
export class CensusTableFiltersComponent {
  protected applyButton = this.receiptsSrv.getApplyButton();
  protected resetButton = this.receiptsSrv.getResetButton();
  protected rangeFilter = this.receiptsSrv.getRangeFilterOptions();
  protected situationOptions = this.situationsStoreService.situations$;
  protected yesOrNoOptions: SeCheckbox[] = this.receiptsSrv.getYesOrNoOptions();
  protected isProvisional: boolean =
    this.specificConfigurationService.isProvisional;

  @Input() filterForm: FormGroup | undefined;
  @Output() handleApplyFilter: EventEmitter<void> = new EventEmitter<void>();

  constructor(
    private receiptsSrv: ReceiptsUtilsService,
    private deviceSrv: DeviceService,
    private situationsStoreService: SituationsStoreService,
    private specificConfigurationService: SpecificConfigurationService,
  ) {}

  protected onApplyFilter = (): void => {
    this.handleApplyFilter.emit();
  };

  protected getButtonAttributes(value: SeButton): SeButton | undefined {
    return this.deviceSrv.isDesktop() ? value : undefined;
  }
}
