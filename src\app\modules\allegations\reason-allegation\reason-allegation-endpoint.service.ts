import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import { RequestUpdateAllegacioTramit } from './reason-allegation.model';
import {
  RequestCreateAllegacioTramit,
  ResponseAllegacioTramit,
  ResponseGetAllegations,
} from '@app/shared/components';

@Injectable({
  providedIn: 'root',
})
export class ReasonAllegationEndpointService {
  constructor(private httpService: SeHttpService) {}

  setAllegationTramit(
    request: RequestCreateAllegacioTramit,
  ): Observable<ResponseAllegacioTramit> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `allegacions/inici`,
      method: 'post',
      body: request,
    };

    return this.httpService.post<ResponseAllegacioTramit>(httpRequest);
  }

  getReasons(idTramit: string): Observable<ResponseGetAllegations> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `${idTramit}/motius`,
      method: 'get',
    };

    return this.httpService.post<ResponseGetAllegations>(httpRequest);
  }

  updateAllegationTramit(
    request: RequestUpdateAllegacioTramit,
  ): Observable<ResponseAllegacioTramit> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `allegacions/update`,
      method: 'post',
      body: request,
    };

    return this.httpService.post<ResponseAllegacioTramit>(httpRequest);
  }
}
