export interface ContestableActDocument {
  type: string;
  subtype?: string;
  name?: string;
  description?: string;
  required?: boolean;
  allowedFiles?: string[];
  allowedSize?: number;
}

export interface DeclarationTypes {
  model: string;
  descripcio: string;
  codiImpost: string;
}

export interface PutTaxYearAndModelDeclaration {
  idTramit: string;
  model: string;
  codiImpost: string;
  exercici: string;
}

export interface PutCsvUploaded {
  model: string;
  exercici: string;
  substitutiva: boolean;
  fitxer: {
    idPadoct: string;
    nom: string;
    pes: number;
    descripcio: string;
    tipusDocument: string;
  };
}
