import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { TranslateModule } from '@ngx-translate/core';
import {
  SeAccordionModule,
  SeAlertModule,
  SeCheckboxModule,
  SePanelModule,
  SeRadioModule,
  SeTextareaModule,
  SpinnerComponent,
} from 'se-ui-components-mf-lib';
import { AllegationsOptionsComponent } from '@shared/components/allegations-options/allegations-options.component';
import { LazyElementsModule } from '@angular-extensions/elements';
import { environment } from '@environments/environment';

@NgModule({
  declarations: [AllegationsOptionsComponent],
  imports: [
    CommonModule,
    FormsModule,
    TranslateModule.forChild(),
    SeCheckboxModule,
    ReactiveFormsModule,
    SeAccordionModule,
    SeRadioModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-documents-upload-files',
          url: environment.wcUrlDocumentsJs,
          loadingComponent: SpinnerComponent,
          preload: true,
        },
      ],
    }),
    SeAlertModule,
    SePanelModule,
    SeTextareaModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  exports: [AllegationsOptionsComponent],
})
export class AllegationsOptionsModule {}
