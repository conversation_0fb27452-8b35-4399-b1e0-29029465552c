import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Routes } from '@angular/router';
import {
  RequestSummaryModule,
  UserDataLoginSimpleFormModule,
} from '@app/shared/components';
import { TranslateModule } from '@ngx-translate/core';
import { SharedModule } from 'primeng/api';
import {
  SeButtonModule,
  SePanelModule,
  SeTableModule,
  SeAlertModule,
  SeLinkModule,
} from 'se-ui-components-mf-lib';
import { ResumAllegationComponent } from './resum-allegation/resum-allegation.component';
import { ReasonAllegationComponent } from './reason-allegation/reason-allegation.component';
import { AllegationsOptionsModule } from '@app/shared/components/allegations-options';
import { CivilServantRequiredDocBlockModule } from '@app/shared/components/civil-servant-required-doc-block';

export const routes: Routes = [
  {
    path: '',
    component: ReasonAllegationComponent,
    data: {
      title: 'SE_PADRO_CO2.ALLEGATIONS.TITLE',
      stepId: 'ALLEGATION_STEP1',
      stepperId: 'ALLEGATION_STEPS',
    },
  },
  {
    path: 'resum',
    component: ResumAllegationComponent,
    data: {
      title: 'SE_PADRO_CO2.ALLEGATIONS.TITLE',
      stepId: 'ALLEGATION_STEP2',
      stepperId: 'ALLEGATION_STEPS',
    },
  },
];

@NgModule({
  declarations: [ReasonAllegationComponent, ResumAllegationComponent],
  imports: [
    CommonModule,
    SharedModule,
    RouterModule.forChild(routes),
    TranslateModule,
    SeButtonModule,
    SePanelModule,
    SeTableModule,
    SeAlertModule,
    SeLinkModule,
    SeAlertModule,
    RequestSummaryModule,
    CivilServantRequiredDocBlockModule,
    AllegationsOptionsModule,
    UserDataLoginSimpleFormModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class AllegationsModule {}
