:host .border se-table ::ng-deep th {
  background-color: var(--color-gray-200);

  &:last-of-type {
    text-align: right !important;
  }

  &:nth-child(2) {
    text-align: right !important;
  }
}

:host {
  .filter-container {
    display: flex;
    align-items: center;
    border-top-right-radius: 0;
    border-top-left-radius: 0;
    font-family: var(--font-primary);
    font-weight: var(--font-regular);
    font-size: var(--text-lg);
    padding: 0 1.5rem;
    min-height: 48px;
    border: none;
    color: var(--color-gray-700);
    background-color: var(--color-blue-100);

    strong {
      font-size: 14px;
      font-weight: 600;
      line-height: 20px;
      text-align: left;
      text-underline-position: from-font;
      text-decoration-skip-ink: none;
    }
  }

  .grey-back {
    width: 100%;
    padding: 16px 0;
    background-color: var(--color-gray-200);
    border-bottom: solid 1px var(--color-gray-300);
  }
}
