import { LazyElementsModule } from '@angular-extensions/elements';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { NgIconsModule } from '@ng-icons/core';
import { matAttachmentOutline, matDeleteOutline } from '@ng-icons/material-icons/outline';
import { TranslateModule } from '@ngx-translate/core';
import { SePanelModule, SeTableModule, SeUiComponentsMfLibModule, SpinnerComponent } from 'se-ui-components-mf-lib';
import { AddDocLinkComponent } from './add-doc-link/add-doc-link.component';
import { AddDocModalComponent } from './add-doc-modal/add-doc-modal.component';
import { DocTableComponent } from './doc-table.component';
import { environment } from 'src/environments/environment';


@NgModule({
  declarations: [
    DocTableComponent,
    AddDocLinkComponent,
    AddDocModalComponent
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SeUiComponentsMfLibModule,
    LazyElementsModule.forFeature({
      elementConfigs: [
        {
          tag: 'mf-documents',
          url: environment.wcUrlDocumentsJs,
          loadingComponent: SpinnerComponent,
          preload: false,
        },
      ],
    }),
    SePanelModule,
    SeTableModule,
    NgIconsModule.withIcons({
      matAttachmentOutline,
      matDeleteOutline
    })
  ],
  exports: [
    DocTableComponent
  ]
})
export class DocTableModule { }