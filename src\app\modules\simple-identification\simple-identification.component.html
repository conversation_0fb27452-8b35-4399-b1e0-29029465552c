<!-- panel de nif, matricula y móvil -->
<form
  class="mt-2 d-flex flex-column"
  [formGroup]="simpleLoginForm"
  *ngIf="showSimpleForm"
>
  <se-panel [title]="'SE_PADRO_CO2.LOGIN_SIMPLE.TITLE' | translate">
    <!-- TODO condition ngif when endpoint response -->
    <se-alert
      *ngIf="false"
      [title]="'SE_PADRO_CO2.ERRORS.NIF_NOT_FOUND' | translate"
      [type]="'error'"
      [closeButton]="false"
    >
    </se-alert>
    <p>{{ 'SE_PADRO_CO2.LOGIN_SIMPLE.SUBTITLE_1' | translate }}</p>
    <div class="row">
      <se-input
        class="col-12 col-md-3"
        formControlName="nifTitular"
        [label]="'UI_COMPONENTS.PASSIVE_SUBJECT.NIF'"
        [type]="'text'"
        [id]="'nif-id'"
      ></se-input>
      <se-input
        class="col-12 col-md-3"
        formControlName="matricula"
        [maxLength]="9"
        [label]="'SE_PADRO_CO2.LABELS.PLATE'"
        [type]="'text'"
        [id]="'plate-id'"
      ></se-input>
      <p>{{ 'SE_PADRO_CO2.LOGIN_SIMPLE.SUBTITLE_2' | translate }}</p>
      <se-input
        class="col-md-3 col-12"
        formControlName="mobil"
        [label]="'SE_PADRO_CO2.LOGIN_SIMPLE.PHONE' | translate"
        [type]="'text'"
        [id]="'phone-id'"
      >
      </se-input>
    </div>
    <app-captcha
      [_captchaPublicApiKey]="captchaKey"
      [_reset]="resetCaptcha"
      [enterprise]="true"
      [language]="currentLang"
      (_responseEvent)="captchaResponse($event)"
      (_expireEvent)="captchaExpired()"
    >
    </app-captcha>
  </se-panel>
  <div class="d-flex flex-column flex-sm-row justify-content-sm-end mt-4">
    <se-button
      type="btn"
      [btnTheme]="'primary'"
      [disabled]="!simpleLoginForm.valid || !this.recaptchaValue"
      (click)="requestSMS()"
    >
      <!-- TODO condition disabled -->
      {{ 'SE_PADRO_CO2.BUTTONS.SMS' | translate }}
    </se-button>
  </div>
</form>

<!-- panel de captcha y código de verificación -->
<form
  class="mt-2 d-flex flex-column"
  [formGroup]="verificationCode"
  *ngIf="!showSimpleForm"
>
  <se-panel [title]="'SE_PADRO_CO2.LOGIN_SIMPLE.TITLE' | translate">
    <!-- TODO condition ngif when endpoint response -->
    <se-alert
      *ngIf="false"
      [title]="'SE_PADRO_CO2.ERRORS.NIF_NOT_FOUND' | translate"
      [type]="'error'"
      [closeButton]="false"
    >
    </se-alert>
    <p>
      {{ 'SE_PADRO_CO2.LOGIN_SIMPLE.VERIFICATION_CODE_SUBTITLE' | translate }}
    </p>
    <div class="row align-items-center">
      <se-input
        class="col-12 col-md-3"
        formControlName="code"
        [label]="'SE_PADRO_CO2.LOGIN_SIMPLE.VERIFICATION_CODE_LABEL'"
        [type]="'text'"
        [id]="'plate-id'"
      ></se-input>
    </div>

    <app-captcha
      [_captchaPublicApiKey]="captchaKey"
      [_reset]="resetCaptcha"
      [enterprise]="true"
      [language]="currentLang"
      (_responseEvent)="captchaResponse($event)"
      (_expireEvent)="captchaExpired()"
    >
    </app-captcha>
  </se-panel>

  <div class="d-flex flex-column flex-sm-row justify-content-sm-end mt-4">
    <se-button
      type="btn"
      [btnTheme]="'primary'"
      [disabled]="!verificationCode.valid || !this.recaptchaValue"
      (click)="confirmCode()"
    >
      {{ 'UI_COMPONENTS.BUTTONS.CONTINUE' | translate }}
    </se-button>
  </div>
</form>

<se-alert
  *ngIf="isSuccessAlertShown"
  class="simple-login__success-alert"
  [type]="'success'"
  [closeButton]="false"
  [title]="'SE_PADRO_CO2.LOGIN_SIMPLE.ALERT_TITLE' | translate"
>
</se-alert>
