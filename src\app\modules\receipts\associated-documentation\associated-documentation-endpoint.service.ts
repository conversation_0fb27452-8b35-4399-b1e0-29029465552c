import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import {
  AssociatedDocumentsRequest,
  AssociatedDocumentsResponse,
} from './associated-documentation.model';

@Injectable({
  providedIn: 'root',
})
export class AssociatedDocumentsService {
  constructor(private httpService: SeHttpService) {}

  public getAssociatedDocuments(
    request: AssociatedDocumentsRequest,
  ): Observable<AssociatedDocumentsResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `documents-associats`,
      method: 'post',
      body: request,
    };
    return this.httpService.post<AssociatedDocumentsResponse>(httpRequest);
  }
}
