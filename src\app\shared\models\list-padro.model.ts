import type { IdentificationType, TABLE_SORT_NAMES } from '@core/models';
import type { Nullable, SeHttpResponse } from 'se-ui-components-mf-lib';
import type { SearchResource } from './search-resource.model';

export type ListPadroRequest = SearchResource<
  FilterListPadro,
  TABLE_SORT_NAMES
>;

export interface FilterListPadro {
  tipusAccess?: Nullable<IdentificationType>;
  soloTotal?: Nullable<boolean>;
  soloPendent?: Nullable<boolean>;
  nifTitular?: Nullable<string>;
  idPersTitular?: Nullable<string>;
  quotaDes?: Nullable<number>;
  quotaFins?: Nullable<number>;
  nou?: Nullable<boolean>;
  domicilat?: Nullable<boolean>;
  listMatriculas?: Nullable<string[]>;
  situacio?: Nullable<string[]>;
  exercici?: Nullable<string>;
  provisional?: Nullable<boolean>;
}

export type ListPadroResponse = SeHttpResponse<ListPadro>;

export interface ListPadro {
  results: PadroItem[];
  total: number;
  size: number;
}

export interface PadroItem {
  nifTitular: string;
  quota: number;
  nou: boolean;
  modificat: boolean;
  domicilat: boolean;
  matricula: string;
  vehicle: string;
  situacio: string;
  exercici: string;
  provisional: boolean;
  codiSituacio: string;
  idDeute: string;
}

export interface SolicitudRequest {
  nifTitular: string;
  tipusTramitacio: string;
  tipusActuacio: string;
}

export type SolicitudResponse = SeHttpResponse<Solicitud[]>;

export interface Solicitud {
  tipusGestio: string;
  dataHora: string;
  presentador: string;
  estat: string;
  idJustificant: string;
}
