import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA, NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SeModalModule, SeTableModule } from 'se-ui-components-mf-lib';
import { Co2EmissionsModalComponent } from './co2-emissions-modal.component';

@NgModule({
  declarations: [Co2EmissionsModalComponent],
  imports: [
    CommonModule,
    TranslateModule.forChild(),
    SeModalModule,
    SeTableModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
})
export class Co2EmissionsModalModule {}
