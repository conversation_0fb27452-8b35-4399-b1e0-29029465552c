import { Injectable } from '@angular/core';
import {
  Column,
  iDocumentPadoct,
  SeAlertMessage,
  SeAlertType,
} from 'se-ui-components-mf-lib';
import { ContestableActDocument, Reasons } from '../allegations-options.model';
import { TranslateService } from '@ngx-translate/core';
import { AllegationsOptionsService } from '../allegations-options.service';
import {
  MODAL_TABLE_COLUMNS_DOC_TYPE_ID_OPTION_VALUE_DESCRIPTION_MAX20,
  TABLE_COLUMNS_DOC_TYPE_FIRST_ELEMENT,
} from '@app/shared/models/upload-documents.model';

@Injectable({
  providedIn: 'root',
})
export class AllegationsDocumentsService {
  _documentsSigedaDescriptions: ContestableActDocument[] = [];

  get documentsSigedaDescriptions(): ContestableActDocument[] {
    return this._documentsSigedaDescriptions;
  }

  constructor(
    private readonly translate: TranslateService,
    private allegationsOptsService: AllegationsOptionsService,
  ) {}

  setDocumentsSigedaDescriptions(value: ContestableActDocument[]): void {
    this._documentsSigedaDescriptions = value;
  }

  getFileTableColumns(): Column[] {
    return [...TABLE_COLUMNS_DOC_TYPE_FIRST_ELEMENT];
  }

  getAllowedFileSize(): number {
    // return de min value of allowedSize of all selected documents
    return Math.min(
      ...this._documentsSigedaDescriptions.map(
        (doc) => doc.allowedSize ?? Infinity,
      ),
    );
  }

  getAcceptedFileTypes(): string[] {
    const array = this._documentsSigedaDescriptions
      .map((document) => document.allowedFiles ?? [])
      .flat();
    return [...new Set(array)]; //remove duplicates
  }

  getFileModalTableColumns(
    documentsSelected: ContestableActDocument[],
    uploadedDocuments: iDocumentPadoct[],
  ): Column[] {
    const modalTableColumnsObject = JSON.parse(
      JSON.stringify(
        MODAL_TABLE_COLUMNS_DOC_TYPE_ID_OPTION_VALUE_DESCRIPTION_MAX20,
      ),
    );

    const subtypes = uploadedDocuments.map(
      (doc) => doc.codeDescriptionComplementary,
    );

    if (
      modalTableColumnsObject[2]?.cellConfig &&
      modalTableColumnsObject[2]?.cellConfig['options']
    ) {
      modalTableColumnsObject[2].cellConfig['options'] = documentsSelected
        .filter((el) => !subtypes.includes(el.subtype))
        .map((el) => ({
          label: el.description,
          id: el.subtype,
        }));
    }

    return modalTableColumnsObject;
  }

  getDocumentAddedOrRemove(
    uploadedDocuments: iDocumentPadoct[],
    newDocuments: iDocumentPadoct[],
  ): iDocumentPadoct | undefined {
    const isSameDocument = (a: iDocumentPadoct, b: iDocumentPadoct): boolean =>
      a.codSigedaType === b.codSigedaType &&
      a.codeDescriptionComplementary === b.codeDescriptionComplementary;

    let bigger: iDocumentPadoct[], smaller: iDocumentPadoct[];
    //add document
    if (newDocuments.length > uploadedDocuments.length) {
      bigger = newDocuments;
      smaller = uploadedDocuments;
      //remove document
    } else {
      bigger = uploadedDocuments;
      smaller = newDocuments;
    }

    const difference = bigger.filter(
      (a) => !smaller.some((b) => isSameDocument(a, b)),
    );

    return difference.length > 0 ? difference[0] : undefined;
  }

  getIfDocumentIsAddedOrRemove(
    uploadedDocuments: iDocumentPadoct[],
    newDocuments: iDocumentPadoct[],
  ): number {
    // 1 = add document, -1 = remove document
    return newDocuments.length - uploadedDocuments.length;
  }

  getDifferenceDocumentsList(
    next: ContestableActDocument[],
    prev: ContestableActDocument[],
  ): ContestableActDocument[] {
    return prev.filter(
      (docA: ContestableActDocument) =>
        !next.some(
          (docB: ContestableActDocument) =>
            docA.type === docB.type && docA.subtype === docB.subtype,
        ),
    );
  }

  getAlertMessageDocuments(
    documentsSelected: ContestableActDocument[],
  ): SeAlertMessage {
    const requiredText = this.getDocumentsListAlert(
      'REQUIRED',
      documentsSelected,
      true,
    );
    const optionalText = this.getDocumentsListAlert(
      'OPTIONAL',
      documentsSelected,
      false,
    );

    return {
      title: '',
      type: SeAlertType.INFO,
      list: [],
      content: requiredText + optionalText,
    };
  }

  private getDocumentsListAlert(
    label: string,
    documentsSelected: ContestableActDocument[],
    required: boolean,
  ): string {
    let result = '';
    const translation = this.translate.instant(
      'SE_PADRO_CO2.ALLEGATIONS_OPTIONS.DOCUMENTS_ALERT.' + label,
    );
    const list = documentsSelected
      .filter((doc) => doc.required === required)
      .map((doc) => doc.description)
      .join('</li><li>');

    if (list) result = translation + '<ul><li>' + list + '</li></ul>';

    return result;
  }

  getMinDocumentsRequired = (reasonsSelected: Reasons[]): number => {
    const min = reasonsSelected.reduce(
      (acc, reason) => acc + (reason.minimDocumentsAportar ?? 0),
      0,
    );
    return min;
  };

  removeDocumentControlValue(
    uploadedDocuments: iDocumentPadoct[],
    newDocuments: iDocumentPadoct[],
  ): void {
    const current = this.getDocumentAddedOrRemove(
      uploadedDocuments,
      newDocuments,
    );
    const documentsControlName = `documents_${current?.codeDescriptionComplementary}`;
    this.allegationsOptsService.reasonsForm
      .get(documentsControlName)
      ?.setValue(null);
  }
}
