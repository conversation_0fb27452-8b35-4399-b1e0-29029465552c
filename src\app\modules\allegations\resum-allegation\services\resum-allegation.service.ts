import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';

import {
  Column,
  Nullable,
  RequestDownloadFile,
  Row,
  SeDocumentsService,
} from 'se-ui-components-mf-lib';
import { AllegationResum } from '../models/resum-allegation.model';
import {
  CellConfig,
  LinkCallbackReturn,
} from 'se-ui-components-mf-lib/lib/components/table/cells/cells.model';
import { RequestSummary } from '@shared/components';
import {
  DocumentsUpload,
  Motius,
  VehiclesSelectedInfo,
} from '@app/shared/models';

const BASE_TRANSLATE = 'SE_PADRO_CO2.ALLEGATIONS';

@Injectable({
  providedIn: 'root',
})
export class ResumAllegationService {
  constructor(
    private translateService: TranslateService,
    private docService: SeDocumentsService,
  ) {}

  getAllegacionsRows(allegacions: Nullable<Motius[]>): Row[] {
    return (allegacions ?? []).map((allegacio) => {
      return this.getRowAllegation(allegacio);
    });
  }

  private getRowAllegation(allegacio: Motius): Row {
    return {
      data: {
        motiu: {
          value: allegacio.motiu,
        },
        submotiu: {
          value: allegacio.submotiu,
        },
        action: {
          value: '',
          cellComponentName:
            allegacio.documents.length > 0
              ? 'linkCellComponent'
              : 'defaultCellComponent',
          cellConfig: this.getButtonCellDownloadConfig(allegacio),
        },
      },
    };
  }

  getDocumentRow(doc: DocumentsUpload): Row {
    return {
      data: {
        documentType: {
          value: doc.documentType || '',
        },
        documentDescription: {
          value: doc.description || '',
        },
        documentName: {
          value: '',
          cellComponentName: 'linkCellComponent',
          cellConfig: this.getButtonCellDownloadConfig(undefined, doc),
        },
      },
    };
  }

  private getButtonCellDownloadConfig(
    allegacio?: Motius,
    docRow?: DocumentsUpload,
  ): CellConfig | undefined {
    if (allegacio?.documents.length === 0) return;
    let doc: DocumentsUpload | undefined;
    const ids = allegacio?.documents.map((doc) => doc.id);

    if (allegacio?.documents.length === 1) doc = allegacio.documents[0];
    if (docRow) doc = docRow;

    return {
      tooltip: true,
      ellipsis: false,
      linkCell: {
        linkConfig: {
          label:
            doc?.filename ??
            this.translateService.instant(`${BASE_TRANSLATE}.TABLE.ZIP`),
          linkTheme: 'secondary',
          iconName: 'matFileDownloadOutline',
          iconPosition: 'right',
          size: 'semibold',
        },
        linkCallback: (): LinkCallbackReturn | Promise<LinkCallbackReturn> => {
          return new Promise(() => {
            this.downloadDocuments(
              ids || [doc?.id || ''],
              doc?.filename ??
                this.translateService.instant(
                  `${BASE_TRANSLATE}.TABLE.DOCUMENT`,
                ),
            );
          });
        },
      },
    };
  }

  downloadDocuments(idsDoc: string[], docName: string): void {
    if (idsDoc.length === 1) {
      const request: RequestDownloadFile = { id: idsDoc[0] };
      this.docService.downloadFile(request, docName);
      return;
    }
    // Si hi ha més d'un document, es descarrega un zip
    this.docService.downloadDocumentsZIP(idsDoc, docName);
  }

  getAllegacionsColumns(): Column[] {
    return [
      {
        key: 'motiu',
        header: this.translateService.instant(`${BASE_TRANSLATE}.TABLE.MOTIU`),
        size: 10,
      },
      {
        key: 'submotiu',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.SUBMOTIU`,
        ),
        size: 10,
      },
      {
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.DOCUMENT`,
        ),
        key: 'action',
        size: 10,
        resizable: false,
      },
    ];
  }

  getDocumentColumns(): Column[] {
    return [
      {
        key: 'documentType',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.DOCUMENT_TYPE`,
        ),
        size: 10,
      },
      {
        key: 'documentDescription',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.DOCUMENT_DESCRIPTION`,
        ),
        size: 10,
      },
      {
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.DOCUMENT_NAME`,
        ),
        key: 'documentName',
        size: 10,
        resizable: false,
      },
    ];
  }

  setDataResponseInDataObject(
    content: AllegationResum,
    vehiclesInfo: Nullable<VehiclesSelectedInfo>,
    isLoginSimple: boolean,
    showGdprMsg: boolean = true,
  ): RequestSummary | undefined {
    const matricules: string[] =
      content.vehicles?.map((v) => v.matricula) || [];

    if (content.vehicles && vehiclesInfo) {
      return {
        ...(!isLoginSimple ? { titular: content.titular } : {}),
        quota: content.quota,
        categoria: content.vehicles[0].categoria,
        co2: content.vehicles[0].co2,
        exercici: content.exercici,
        showGdprMsg: showGdprMsg,
        singleVehicleText: `${content.vehicles[0].matricula}, ${content.vehicles[0].marca} ${content.vehicles[0].model}`,
        vehicles: {
          matriculas: matricules,
          provisional: vehiclesInfo.provisional,
          exercici: vehiclesInfo.exercici,
          idPersTitular: vehiclesInfo.idPersTitular,
        },
        descripcion: content.motius
          ?.filter((m) => !!m.descripcio)
          .map((m) => m.descripcio)
          .join('<br> '),
        checkbox:
          (content.motius
            ?.filter((m) => !!m.checkbox)
            .map((m) => m.checkbox)[0] as string) || '',
      };
    }
    return undefined;
  }

  setRecursDataResponseInDataObject(
    content: AllegationResum,
    vehiclesInfo: Nullable<VehiclesSelectedInfo>,
    isLoginSimple: boolean,
    showGdprMsg: boolean = true,
  ): RequestSummary | undefined {
    return {
      ...this.setDataResponseInDataObject(
        content,
        vehiclesInfo,
        isLoginSimple,
        showGdprMsg,
      ),
      categoria: undefined,
      co2: undefined,
    };
  }
}
