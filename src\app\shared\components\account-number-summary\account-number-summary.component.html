<se-panel [title]="title | translate">
  <div class="col-12 d-flex flex-md-row flex-column content-spacing mt-2">
    <span class="col-md-3 col-12 fw-bold">{{
      'SE_PADRO_CO2.LABELS.IBAN' | translate
    }}</span>
    <span class="col-md-6 col-12">{{ data?.iban }}</span>
  </div>

  <div
    *ngIf="data?.declaracioNumeroCompte"
    class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
  >
    <form class="mt-2 d-flex flex-column" [formGroup]="recursSummaryForm">
      <se-checkbox
        formControlName="ibanDr"
        [label]="
          'SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_2.BANK_DETAIL_DR' | translate
        "
        [id]="'checkbox-iban-summary'"
        [tooltip]="false"
        [disabled]="true"
      ></se-checkbox>
    </form>
  </div>

  <div
    *ngIf="data?.documentNumeroCompte"
    class="col-12 d-flex flex-md-row flex-column content-spacing mt-2"
  >
    <span class="col-md-3 col-12 fw-bold">{{
      'SE_PADRO_CO2.LABELS.ACCOUNT_CERTIFICATE' | translate
    }}</span>
    <div class="col-md-6 col-12">
      <se-link
        linkTheme="secondary"
        iconName="matFileDownloadOutline"
        (onClick)="downloadDocumentNumeroCompte()"
        [disabled]="false"
      >
        {{ data?.documentNumeroCompte?.filename }}
      </se-link>
    </div>
  </div>
</se-panel>
