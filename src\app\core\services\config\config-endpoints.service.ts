import { Injectable } from '@angular/core';
import { MfSpecificConfiguration } from '@app/core/models';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class ConfigEndpointsService {
  constructor(private httpService: SeHttpService) {}

  getCO2Config(): Observable<SeHttpResponse<MfSpecificConfiguration>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `gestio-configuracio`,
      method: 'get',
      clearExceptions: true,
    };

    return this.httpService.get<SeHttpResponse<MfSpecificConfiguration>>(
      httpRequest,
    );
  }
}
