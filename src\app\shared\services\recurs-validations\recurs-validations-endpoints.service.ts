import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import { map, Observable } from 'rxjs';
import {
  Nullable,
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import {
  StartRecursRequest,
  SelectedVehiclesValidationResponse,
  SelectedVehiclesReasValidationResponse,
} from '@shared/services/recurs-validations/recurs-validations-endpoints.model';

@Injectable({
  providedIn: 'root',
})
export class RecursValidationsEndpointsService {
  constructor(private httpService: SeHttpService) {}

  public setRecurs(
    request: StartRecursRequest,
  ): Observable<Nullable<SelectedVehiclesValidationResponse>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `recurs`,
      method: 'post',
      body: request,
    };
    return this.httpService
      .post<SeHttpResponse<SelectedVehiclesValidationResponse>>(httpRequest)
      .pipe(
        map(
          (response: SeHttpResponse<SelectedVehiclesValidationResponse>) =>
            response.content,
        ),
      );
  }

  public setReas(
    request: StartRecursRequest,
  ): Observable<Nullable<SelectedVehiclesReasValidationResponse>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `reas`,
      method: 'post',
      body: request,
    };
    return this.httpService
      .post<SeHttpResponse<SelectedVehiclesReasValidationResponse>>(httpRequest)
      .pipe(
        map(
          (response: SeHttpResponse<SelectedVehiclesReasValidationResponse>) =>
            response.content,
        ),
      );
  }
}
