import { Component, type <PERSON><PERSON><PERSON><PERSON>, type OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { Subject, takeUntil } from 'rxjs';
import { SeMessageService } from 'se-ui-components-mf-lib';

import { TaxpayerChange, TaxPayer, CreateProcedureRequest } from './models';
import { ParticipantsService } from './participants-endpoints.service';
import { Constant } from '@core/models/constants.enum';
import { HeaderInfoService, StoreService } from '@core/services';
import { AppRoutes } from '@core/models/app-routes.enum';

@Component({
  selector: 'app-participants',
  styleUrls: ['participants.component.scss'],
  template: `
    <mf-tributs-taxpayer
      *axLazyElement
      class="mb-3"
      [description]="
        'SE_DECINF_MF.MODULE_PARTICIPANTS.DESCRIPTION_DATA_TO_ENTER' | translate
      "
      [taxPayerNameLabel]="
        'SE_DECINF_MF.MODULE_PARTICIPANTS.TAXPAYER_NAME_LABEL' | translate
      "
      [taxPayerValidated]="taxPayerValidated"
      [procedureCode]="PROCESS_CODE"
      (onChange)="handleTaxpayerChange($event)"
    ></mf-tributs-taxpayer>

    <section class="d-flex justify-content-end participants__actions">
      <se-button
        [disabled]="!isTaxpayerValid"
        (onClick)="submit()"
        style="right: 0"
      >
        {{ 'UI_COMPONENTS.BUTTONS.NEXT' | translate }}
      </se-button>
    </section>
  `,
})
export class ParticipantsComponent implements OnInit, OnDestroy {
  protected readonly PROCESS_CODE = Constant.PROCESS_CODE;
  protected isResponsibleCompleted = false;
  protected taxPayerValidated: TaxPayer | undefined;

  private declaracioResponsable = {
    visible: false,
    checked: false,
    agreement: false,
  };
  private isScoringValid = false;
  private isPresenter: boolean | undefined = false;
  private taxpayerNIF = '';
  private taxpayerName = '';
  private destroyed$: Subject<void> = new Subject<void>();

  constructor(
    private participantsService: ParticipantsService,
    private router: Router,
    private messageService: SeMessageService,
    private store: StoreService,
    private header: HeaderInfoService,
  ) {
    this.header.resetCompleteSubheader();
  }

  ngOnInit(): void {
    if (this.store.selectedTaxpayer?.nif && this.store.selectedTaxpayer?.name) {
      this.taxPayerValidated = this.store.selectedTaxpayer;
      if (this.taxPayerValidated && this.store.responsibleDeclaration) {
        this.taxPayerValidated.haveAcceptedDeclarationOfResponsibility =
          this.store.responsibleDeclaration;
        this.taxPayerValidated.isPresenter = !this.store.responsibleDeclaration;
        this.declaracioResponsable.checked = this.store.responsibleDeclaration;
        this.declaracioResponsable.visible = true;
      }
    }
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  protected handleTaxpayerChange(event: Event): void {
    const customEvent = event as CustomEvent<TaxpayerChange>;
    const { declaracioResponsable, isScoringValid, name, nif, isPresenter } =
      customEvent.detail;

    const isNifChanged = this.taxpayerNIF !== nif;
    const isNameChanged = this.taxpayerName !== name;
    const isTaxpayerChanged = isNifChanged || isNameChanged;

    if (isTaxpayerChanged) {
      this.messageService.resetMessages();
    }

    this.taxpayerNIF = nif;
    this.taxpayerName = name;
    this.isPresenter = isPresenter;

    this.isScoringValid = isScoringValid;
    this.declaracioResponsable = declaracioResponsable;
  }

  protected get isTaxpayerValid(): boolean {
    const isTaxpayerValid =
      this.isScoringValid &&
      (!this.declaracioResponsable.visible ||
        this.declaracioResponsable.checked ||
        this.declaracioResponsable.agreement);

    return isTaxpayerValid;
  }

  protected submit(): void {
    const isTheSameNIF =
      this.store.selectedTaxpayer?.nif?.toUpperCase() ===
      this.taxpayerNIF.toUpperCase();
    const isTheSameName =
      this.store.selectedTaxpayer?.name?.toUpperCase() ===
      this.taxpayerName.toUpperCase();

    if (this.store.idTramit && isTheSameNIF && isTheSameName) {
      this.navigateToYearDeclarationStep(this.store.idTramit);
    } else {
      this.createProcedure();
    }
  }

  private createProcedure(): void {
    const body: CreateProcedureRequest = {
      declaracioExempcio: false,
      declarants: [
        {
          nom: this.taxpayerName,
          idDocument: this.taxpayerNIF,
          declaracioResponsable: this.declaracioResponsable.checked,
        },
      ],
    };

    this.participantsService
      .createProcedure(body)
      .pipe(takeUntil(this.destroyed$))
      .subscribe({
        next: ({ content }) => {
          if (content) {
            this.navigateToYearDeclarationStep(content);
          }
        },
        error: () => {},
      });
  }

  private navigateToYearDeclarationStep(idTramit: string): void {
    const taxPayer = {
      nif: this.taxpayerNIF,
      name: this.taxpayerName,
      haveAcceptedDeclarationOfResponsibility:
        this.declaracioResponsable.checked,
      isPresenter: this.isPresenter,
      isScoringValid: this.isScoringValid,
    };

    this.store.selectedTaxpayer = taxPayer;
    this.header.taxpayerName = this.taxpayerName;
    this.store.idTramit = idTramit;
    this.store.responsibleDeclaration = this.declaracioResponsable.checked;
    this.router.navigate([AppRoutes.YEAR_DECLARATION]);
  }
}
