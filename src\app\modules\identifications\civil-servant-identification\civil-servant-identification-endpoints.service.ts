import { Injectable } from '@angular/core';
import {
  SeHttpRequest,
  SeHttpResponse,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import { CivilServantResponse } from '../models';
import { environment } from '@environments/environment';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root',
})
export class CivilServantIdentificationEndpointsService {
  constructor(private httpService: SeHttpService) {}

  verifyCivilServantNIF(
    body: string[],
  ): Observable<SeHttpResponse<CivilServantResponse>> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlSeguretat,
      url: `/validar-nifs`,
      method: 'post',
      body,
    };

    return this.httpService.post<SeHttpResponse<CivilServantResponse>>(
      httpRequest,
    );
  }
}
