import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CensusTableFiltersModalComponent } from './census-table-filters/census-table-filters-modal.component';
import { CensusTableFiltersComponent } from './census-table-filters/census-table-filters.component';
import { CensusTableComponent } from './census-table.component';
import { TranslateModule } from '@ngx-translate/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import {
  SeDropdownModule,
  SeModalModule,
  SeInputModule,
  SeRangeFilterModule,
  SeButtonDropdownModule,
  SeButtonModule,
  SeTableModule,
  SeAlertModule,
  SeDropdownFilterModule,
  SeEmptyStateModule,
} from 'se-ui-components-mf-lib';

@NgModule({
  declarations: [
    CensusTableComponent,
    CensusTableFiltersComponent,
    CensusTableFiltersModalComponent,
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    TranslateModule.forChild(),
    SeDropdownModule,
    SeModalModule,
    SeInputModule,
    SeRangeFilterModule,
    SeButtonModule,
    SeButtonDropdownModule,
    SeTableModule,
    SeAlertModule,
    SeDropdownFilterModule,
    SeEmptyStateModule,
  ],
  exports: [
    CensusTableComponent,
    CensusTableFiltersComponent,
    CensusTableFiltersModalComponent,
  ],
})
export class CensusTableModule {}
