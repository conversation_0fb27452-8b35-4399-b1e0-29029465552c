<se-modal
  [data]="data"
  [closableLabel]="'UI_COMPONENT.BUTTONS.CLOSE'"
  (modalOutputEvent)="closeModal()"
  (modalSecondaryButtonEvent)="closeModal()"
>
  <!-- bloque de base imposable -->
  <div class="mt-2">
    <div class="row d-flex align-items-center">
      <div class="col-12 col-md-10 d-flex flex-column">
        <strong>
          {{ 'SE_PADRO_CO2.DETAIL.LABELS.TAX_BASE' | translate }}</strong
        >
        <p
          class="mt-2"
          [innerHTML]="'SE_PADRO_CO2.MODAL.DETAIL.TAX_BASE' | translate"
        ></p>
      </div>
      <div class="col-md-2 col-12">
        <strong>{{ carDetail.co2 | number: '1.0-2' : 'ca-ES' }}g/km</strong>
      </div>
    </div>
  </div>

  <!-- tabla de detalle de base imposable -->
  <div class="border mt-4">
    <se-table
      [selectable]="false"
      [columns]="tableColumns"
      [data]="rows"
      [resizable]="true"
      [cellTemplatePriorityOrder]="'row-column-cell'"
    ></se-table>

    <div class="filter-container justify-content-end">
      <strong class="me-4">
        {{ 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.TOTAL_QUOTA' | translate }}
      </strong>
      <p class="m-0">{{ total | currency: '€' }}</p>
    </div>
  </div>

  <!-- detalle del calculo -->
  <div class="mt-4">
    <strong>{{ 'SE_PADRO_CO2.DETAIL.LABELS.ESTIMATE' | translate }}</strong>
    <div class="border mt-4">
      <div class="grey-back">
        <div
          class="d-flex flex-column flex-md-row justify-content-center gap-1"
        >
          <!-- quota integra -->
          <div class="d-flex flex-column">
            <strong class="text-center">
              {{ 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.TOTAL_QUOTA' | translate }}
            </strong>
            <p class="text-center">{{ total | number: '1.2-2' : 'ca-Es' }}</p>
          </div>

          <div class="d-flex flex-column justify-content-end">
            <p class="text-center text-md-left">x [</p>
          </div>

          <!-- dias de periodo impositivo -->
          <div class="d-flex flex-column">
            <strong class="text-center">{{
              'SE_PADRO_CO2.DETAIL.LABELS.DAYS_TAX_PERIOD' | translate
            }}</strong>
            <p class="text-center">{{ carDetail.totalDies }}</p>
          </div>

          <div class="d-flex flex-column justify-content-end">
            <p class="text-center text-md-left">/</p>
          </div>

          <!-- dias del año -->
          <div class="d-flex flex-column">
            <strong class="text-center">
              {{
                'SE_PADRO_CO2.DETAIL.LABELS.TOTAL_DAYS_TAX_PERIOD' | translate
              }}
            </strong>
            <p class="text-center">{{ yearDays }}</p>
          </div>

          <div class="d-flex flex-column justify-content-end">
            <p class="text-center text-md-left">] x</p>
          </div>

          <!-- titularidad -->
          <div class="d-flex flex-column">
            <strong class="text-center">
              {{ 'SE_PADRO_CO2.DETAIL.LABELS.OWNERSHIP_1' | translate }}
            </strong>
            <p class="text-center">{{ carDetail.percentajeTitular }}%</p>
          </div>
        </div>
      </div>
      <div class="filter-container justify-content-end">
        <strong class="me-4">
          {{ 'SE_PADRO_CO2.DETAIL.LABELS.TAX_QUOTA' | translate }}
        </strong>
        <p class="m-0">{{ totalTaxQuota | currency: '€' }}</p>
      </div>
    </div>
  </div>
</se-modal>
