import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import {
  ReasPresentationResponse,
  ReasResumResponse,
} from '../models/summary-reas.model';

@Injectable({
  providedIn: 'root',
})
export class SummaryReasEndpointService {
  constructor(private httpService: SeHttpService) {}

  getReasResumInfo(idTramit: string): Observable<ReasResumResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `reas/${idTramit}/resum`,
      method: 'get',
    };

    return this.httpService.get<ReasResumResponse>(httpRequest);
  }

  setReasPresentation(idTramit: string): Observable<ReasPresentationResponse> {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `reas/${idTramit}/presentacio`,
      method: 'post',
    };

    return this.httpService.post<ReasPresentationResponse>(httpRequest);
  }
}
