import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import {
  AppRoutes,
  DomiciledProcedureRequest,
  DomiciledProcessTypes,
  DomiciledVehicle,
  FunctionalModuleEnum,
  IdentificationType,
  ProcedureSaved,
  ResourceProcessDocument,
} from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { RequestSummary } from '@shared/components';
import { DomiciliationEndpointService } from '@shared/services';
import { Subject, takeUntil } from 'rxjs';
import { iDocumentPadoct } from 'se-ui-components-mf-lib';
import { ProcessNameToDisplayInPresentationReceipt } from '../../presentation-receipt/presentation-receipt.model';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { VehiclesSelectedInfo } from '@app/shared/models';
import { MaskIbanService } from '@app/core/services/mask-iban';
import { ProceduresHeaderService } from '../../../shared/services/procedures-header';

@Component({
  selector: 'app-direct-debit-summary',
  templateUrl: './direct-debit-summary.component.html',
})
export class DirectDebitSummaryComponent implements OnInit, OnDestroy {
  protected directDebitSummary: RequestSummary | undefined;
  protected loginSimpleUserForm: FormGroup | undefined;
  protected disableContinueButton: boolean = false;
  protected idEntity: string = '';
  protected plates: string[] = [];

  private destroyed$ = new Subject<void>();

  get isLoginSimple(): boolean {
    return this.storage.profileUser === IdentificationType.LOGIN_SIMPLE;
  }

  get isCivilServant(): boolean {
    return this.storage.profileUser === IdentificationType.CIVIL_SERVANT;
  }

  get idFunctionalModule(): FunctionalModuleEnum {
    return FunctionalModuleEnum.DOMICILIACIONS;
  }

  constructor(
    private storage: StorageService,
    private loginSrv: LoginResponseService,
    private domiciliationSrv: DomiciliationEndpointService,
    private specificConfigurationSrv: SpecificConfigurationService,
    private customRouter: CustomRouterService,
    private translate: TranslateService,
    private maskIbanService: MaskIbanService,
    private header: ProceduresHeaderService,
  ) {}

  ngOnInit(): void {
    if (this.isLoginSimple) {
      this.buildLoginSimpleUserForm();
    }
    this.setupPlates();

    this.setDirectDebitSummaryDataInPanel();

    this.saveDirectDebit();

    this.header.setupDomiciliationHeader(this.storage.vehiclesToBeRegistered);
  }

  ngOnDestroy(): void {
    this.destroyed$.next();
    this.destroyed$.complete();
  }

  private setupPlates(): void {
    this.plates =
      this.storage.vehiclesToBeRegistered?.map(
        (vehicle) => vehicle.matricula,
      ) || [];
  }

  protected goBack(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.BANK_DETAILS);
  }

  protected onContinue(): void {
    this.submitDirectDebit();
  }

  protected alertMessageDomiciliationRequest(): string {
    const today = this.specificConfigurationSrv.currentDate;
    const comparativeDay = new Date(
      this.specificConfigurationSrv.limitDomiciliacions,
    );
    today.setHours(0, 0, 0, 0);
    comparativeDay.setHours(0, 0, 0, 0);
    const alert =
      today <= comparativeDay ? 'ALERT_BEFORE_15/07' : 'ALERT_AFTER_15/07';

    return this.translate.instant(
      `SE_PADRO_CO2.DOMICILIATION_MODULE.STEP_3.${alert}`,
    );
  }

  private saveDirectDebit(): void {
    const atesaPhone = this.storage.civilServantVehiclePhone;
    const atesaDate = this.storage.civilServantVehicleDate;
    const atesaHour = this.storage.civilServantVehicleHour;
    const body: DomiciledProcedureRequest = {
      accio: DomiciledProcessTypes.SUBSCRIBE,
      provisional: this.specificConfigurationSrv.isProvisional,
      listMatriculas: this.storage.vehiclesToBeRegistered?.map(
        (vehicle) => vehicle.matricula,
      ),
      idPersTitular: this.loginSrv.user.idPersTitular as string,
      iban: this.storage.ibanDomiciliation,
      exercici: this.specificConfigurationSrv.exerciseToBeDomiciliated,
      tipusAccess: this.loginSrv.user.tipusAccess,
      ...(atesaPhone && atesaDate && atesaHour
        ? {
            trucadaTelefonica: {
              numeroTelefon: atesaPhone,
              data: atesaDate,
              hora: atesaHour,
            },
          }
        : {}),
      nifRepresentant: this.storage.civilServantVehicleNifRepresentant,
    };

    this.domiciliationSrv
      .postDomiciledProcedureInici(body)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((result) => {
        if (result?.content?.id) {
          this.idEntity = result.content.id;
        }
      });
  }

  private submitDirectDebit(): void {
    const body: DomiciledProcedureRequest = {
      listMatriculas: this.plates,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
      tipusAccess: this.loginSrv.user.tipusAccess,
      id: this.idEntity,
      nom: this.loginSimpleUserForm?.get('name')?.value || null,
      cognoms: this.loginSimpleUserForm?.get('surname')?.value || null,
    };

    this.domiciliationSrv
      .postDomiciledProcedureFinalizar(body)
      .pipe(takeUntil(this.destroyed$))
      .subscribe((result) => {
        if (result?.content) {
          this.setDataToShowThemInPresentationReceipt(result.content);
          this.storage.processNameToDisplayPresentationReceipt =
            ProcessNameToDisplayInPresentationReceipt.Domiciliation;
          this.clearStorageData();
          this.customRouter.navigateByBaseUrl(AppRoutes.PRESENTATION_RECEIPT); // pantalla de resumen
        }
      });
  }

  private setDataToShowThemInPresentationReceipt(
    response: ProcedureSaved,
  ): void {
    this.storage.setResourceProcessDocumentData({
      receipt: {
        idPadoct: response.idJustificant,
        nom: response.fileName,
      } as iDocumentPadoct,
      idFunctionalModule:
        ProcessNameToDisplayInPresentationReceipt.Domiciliacions,
    } as ResourceProcessDocument);
  }

  private clearStorageData(): void {
    this.storage.clearIbanDomiciliation();
    this.storage.clearIbanDomiciliationDr();
    this.storage.clearVehiclesToBeRegistered();
  }

  private setDirectDebitSummaryDataInPanel(): void {
    const vehicles = this.storage.vehiclesToBeRegistered;

    const iban = this.maskIbanService.transform(this.storage.ibanDomiciliation);
    const titular = this.loginSrv.user.nombreTitular;

    if (vehicles) {
      this.directDebitSummary = {
        iban: iban,
        vehicles: this.getVehicleListInSummary(vehicles),
        ...(!this.isLoginSimple ? { titular: titular } : {}),
        exercici: this.specificConfigurationSrv.exerciceDom,
        showGdprMsg: true,
        dataProxRebut: this.specificConfigurationSrv.nextReceiptDate,
        ...(vehicles.length === 1 && {
          singleVehicleText: `${vehicles[0].matricula}, ${vehicles[0].marca} ${vehicles[0].model}`,
        }),
      };
    }
  }

  private getVehicleListInSummary(
    vehicles: DomiciledVehicle[],
  ): VehiclesSelectedInfo {
    return {
      provisional: this.specificConfigurationSrv.isProvisional,
      matriculas: vehicles.map((vehicle) => vehicle.matricula),
      exercici: this.specificConfigurationSrv.currentExercise,
      idPersTitular: this.loginSrv.user.idPersTitular as string,
    };
  }

  private buildLoginSimpleUserForm(): void {
    this.loginSimpleUserForm = new FormGroup({
      name: new FormControl('', [Validators.required]),
      surname: new FormControl('', [Validators.required]),
    });
  }

  protected onDisableContinueButton($event: boolean): void {
    this.disableContinueButton = $event;
  }
}
