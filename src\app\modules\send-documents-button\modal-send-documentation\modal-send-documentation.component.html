<div *ngIf="componentForm && data">
  <se-modal [data]="data" (modalOutputEvent)="closeModal($event)">
    <form [formGroup]="componentForm" class="mt-4">
      <se-input formControlName="email" [label]="'SE_DOCUMENTS_MF.SEND_DOCUMENTS_BUTTON.MODAL.EMAIL' | translate" type="text"
        id="email"></se-input>
      <se-input formControlName="confirmEmail"
        [label]="'SE_DOCUMENTS_MF.SEND_DOCUMENTS_BUTTON.MODAL.CONFIRM_EMAIL' | translate" type="text"
        id="confirmEmail"></se-input>
    </form>
    <se-alert *ngIf="showModal" [title]="'SE_DOCUMENTS_MF.SEND_DOCUMENTS_BUTTON.MODAL.INCORRECT_MAIL' | translate" [type]="'warning'"
      [closeButton]="true" (close)="closeAlert()"
      >
    </se-alert>
  </se-modal>
</div>