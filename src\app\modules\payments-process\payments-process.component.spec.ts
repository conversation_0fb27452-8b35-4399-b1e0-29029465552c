import { ComponentFixture, TestBed } from '@angular/core/testing';

import { PaymentsProcessComponent } from './payments-process.component';
import { TranslateModule } from '@ngx-translate/core';
import { SePanelModule, SeTableModule } from 'se-ui-components-mf-lib';
import { CommonModule } from '@angular/common';
import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
import { BrowserAnimationsModule } from '@angular/platform-browser/animations';
import { HeaderInfoService } from '@app/core/services';
import { HttpClientTestingModule } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';

describe('PaymentsProcessComponent', () => {
  let component: PaymentsProcessComponent;
  let fixture: ComponentFixture<PaymentsProcessComponent>;

  beforeEach(() => {
    TestBed.configureTestingModule({
      declarations: [PaymentsProcessComponent],
      imports: [
        CommonModule,
        BrowserAnimationsModule,
        RouterTestingModule,
        HttpClientTestingModule,
        SePanelModule,
        SeTableModule,
        TranslateModule.forRoot(),
      ],
      providers: [HeaderInfoService],
      schemas: [CUSTOM_ELEMENTS_SCHEMA],
    });
    fixture = TestBed.createComponent(PaymentsProcessComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
