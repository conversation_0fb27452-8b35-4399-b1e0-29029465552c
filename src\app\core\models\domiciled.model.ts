import { Vehicle } from '@shared/models';
import { Nullable, SeHttpResponse } from 'se-ui-components-mf-lib';
import { IdentificationType } from './login.model';

export interface DomiciledVehicle extends Vehicle {
  marca: string;
  matricula: string;
  model: string;
  modificat?: boolean;
  nou?: boolean;
  iban?: string;
  exerciciDomiciliacio?: string;
}

export interface DomiciledVehiclesResponse extends SeHttpResponse {
  content?: DomiciledVehicle[];
}

export interface DomiciledVehiclesRequest {
  idPersTitular: string;
  provisional: boolean;
  exercici: string;
  tipusAccess: IdentificationType;
  matricula?: Nullable<string>;
}

export interface DomiciledProcedureResponseInici extends SeHttpResponse {
  content?: {
    id: string;
  };
}

export interface DomiciledProcedureResponseFinalizar extends SeHttpResponse {
  content?: ProcedureSaved;
}

export interface DomiciledProcedureRequest {
  idPersTitular: string;
  id?: string;
  titular?: string;
  iban?: string;
  listMatriculas?: string[];
  accio?: DomiciledProcessTypes;
  provisional?: boolean;
  exercici?: string;
  tipusAccess: IdentificationType;
  nom?: string;
  cognoms?: string;
  trucadaTelefonica?: AtesaPhoneData;
  nifRepresentant?: Nullable<string>;
}
export interface AtesaPhoneData {
  numeroTelefon: string;
  data: string;
  hora: string;
}
export interface ProcedureSaved {
  idJustificant: string;
  fileName: string;
}

export enum DomiciledProcessTypes {
  SUBSCRIBE = 'ALTA',
  UNSUBSCRIBE = 'BAIXA',
  UPDATE = 'MODIFICACIO',
}

export interface FeatureFlag {
  alta: boolean;
  modification: boolean;
}

export interface GetNotificationDataResponse extends SeHttpResponse {
  content: GetNotificationDataResponseContent;
}

export interface GetNotificationDataResponseContent {
  notificacioElectronica: boolean;
  tipo: TipoNotificacio;
}

export enum TipoNotificacio {
  VOLUNTARI = '1',
  OBLIGADO = '2',
  NO_INFO = '3',
}

export interface GetUserInfoResponse extends SeHttpResponse {
  content: UserInfo;
}

export interface UserInfo {
  nif: string;
  nombreCompleto: string;
  nombre: string;
  primerApellido: string;
  segundoApellido: string;
  direccionFiscal: Direccion;
  direccionNotificacion: Direccion;
  notificacionElectronica: DatoContacto;
  notificacionCO2: DatoContacto;
  notificacionNEGeneral: DatoContacto;
  showDireccionNotificacion: boolean;
  showNE: boolean;
}

export interface Direccion {
  idAdress: string;
  viaYPortalYPisoYLetra: string;
  cpYMunicipio: string;
  provincia: string;
  pais: string;
  direNoConsolidat: boolean;
}

export interface DatoContacto {
  telefono: string;
  email: string;
  esMovil: boolean;
}
