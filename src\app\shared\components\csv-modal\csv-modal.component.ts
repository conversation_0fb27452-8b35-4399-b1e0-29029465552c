import { LazyElementsModule } from '@angular-extensions/elements';
import { NgIf, NgTemplateOutlet } from '@angular/common';
import {
  Component,
  CUSTOM_ELEMENTS_SCHEMA,
  EventEmitter,
  Input,
  OnDestroy,
  Output,
} from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { LoginResponseService } from '@app/core/services';
import { environment } from '@environments/environment';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { CsvEndpointService } from '@shared/services/csv-endpoint/csv-endpoint.service';
import { Subject, takeUntil } from 'rxjs';
import {
  SeAlertMessage,
  SeAlertModule,
  SeAlertType,
  SeExceptionViewerModule,
  SeModal,
  SeModalModule,
  SeModalOutputEvents,
  SeSwitchModule,
} from 'se-ui-components-mf-lib';

enum FORM_CONTROL_NAME {
  CSV = 'csv',
  CONTINUE = 'continue',
}

@Component({
  selector: 'app-csv-modal',
  standalone: true,
  imports: [
    NgIf,
    SeModalModule,
    SeSwitchModule,
    SeAlertModule,
    SeExceptionViewerModule,
    LazyElementsModule,
    ReactiveFormsModule,
    NgTemplateOutlet,
    TranslateModule,
  ],
  schemas: [CUSTOM_ELEMENTS_SCHEMA],
  template: `
    <se-modal
      [data]="data"
      (modalOutputEvent)="onContinue($event)"
      (modalSecondaryButtonEvent)="closeModal()"
    >
      <se-exception-viewer></se-exception-viewer>
      <se-alert
        *ngIf="alert"
        [title]="alert.title"
        [type]="alert.type"
        [closeButton]="false"
      >
      </se-alert>
      <ng-template #singleVehicle>
        <div
          [innerHTML]="'SE_PADRO_CO2.MODAL.CSV_MODAL.SINGLE.INFO' | translate"
        ></div>
        <ng-container *ngTemplateOutlet="csvForm"></ng-container>
      </ng-template>
      <ng-container *ngIf="multiple; else singleVehicle">
        <p
          [innerHTML]="'SE_PADRO_CO2.MODAL.CSV_MODAL.MULTIPLE.INFO' | translate"
        ></p>
        <form [formGroup]="form">
          <se-switch
            [id]="FORM_CONTROL_NAME.CSV"
            [label]="
              'SE_PADRO_CO2.MODAL.CSV_MODAL.MULTIPLE.FORM.CSV_LABEL' | translate
            "
            formControlName="csv"
            (onToggle)="onToggleOption(FORM_CONTROL_NAME.CSV, $event)"
          >
          </se-switch>
          <div
            class="csv-form mt-3"
            *ngIf="form.get(FORM_CONTROL_NAME.CSV)?.value"
          >
            <ng-container *ngTemplateOutlet="csvForm"></ng-container>
          </div>
          <hr />
          <se-switch
            [id]="FORM_CONTROL_NAME.CONTINUE"
            [label]="
              'SE_PADRO_CO2.MODAL.CSV_MODAL.MULTIPLE.FORM.CONTINUE_LABEL'
                | translate
            "
            formControlName="continue"
            (onToggle)="onToggleOption(FORM_CONTROL_NAME.CONTINUE, $event)"
          >
          </se-switch>
        </form>
      </ng-container>
    </se-modal>
    <!-- se-gestions -->
    <ng-template #csvForm>
      <mf-gestions-csv-form
        *axLazyElement="mfGestionsURL"
        [csvInfoModalData]="csvInfoModalData"
        [inputPlaceholder]="
          'SE_PADRO_CO2.MODAL.CSV_MODAL.CSV_INPUT_PLACEHOLDER' | translate
        "
        [howToCheckCSVDisplay]="'alert'"
        (callCsvEndpoint)="onCallCsvEndpoint($event)"
      >
      </mf-gestions-csv-form>
    </ng-template>
  `,
  styleUrls: ['./csv-modal.component.scss'],
})
export class CsvModalComponent implements OnDestroy {
  @Input({ required: true }) data!: SeModal;
  @Input({ required: true }) multiple!: boolean;

  @Output() continueOutput: EventEmitter<string> = new EventEmitter<string>();

  form: FormGroup;

  FORM_CONTROL_NAME = FORM_CONTROL_NAME;
  csv: string | undefined;
  alert: SeAlertMessage | undefined;
  SeAlertType = SeAlertType;
  csvInfoModalData!: SeModal;

  readonly mfGestionsURL = environment.mfGestionsURL;

  private readonly unsubscribe: Subject<void> = new Subject();

  constructor(
    private readonly activatedModalService: NgbActiveModal,
    private readonly csvService: CsvEndpointService,
    private readonly loginService: LoginResponseService,
    private readonly fb: FormBuilder,
    private readonly translateService: TranslateService,
  ) {
    this.form = this.fb.group({
      csv: [],
      continue: [],
    });

    this.csvInfoModalData = {
      closable: true,
      title: this.translateService.instant(
        'SE_PADRO_CO2.MODAL.CSV_MODAL.INFO_MODAL.TITLE',
      ),
      titleTextWeight: 'regular',
      subtitle: this.translateService.instant(
        'SE_PADRO_CO2.MODAL.CSV_MODAL.INFO_MODAL.SUBTITLE',
      ),
    };
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  onToggleOption(controlName: string, event: boolean): void {
    if (controlName === FORM_CONTROL_NAME.CSV) {
      this.data.closableDisabled = event;
      this.form.get(FORM_CONTROL_NAME.CONTINUE)?.setValue(!event);
    } else {
      this.data.closableDisabled = !event;
      this.form.get(FORM_CONTROL_NAME.CSV)?.setValue(!event);
    }
  }

  protected onCallCsvEndpoint(event: Event): void {
    const csv = (event as CustomEvent<string>).detail;

    //Call endpoint
    this.csvService
      .checkCSV(csv, this.loginService.getNifTitular())
      .pipe(takeUntil(this.unsubscribe))
      .subscribe((response: boolean) => {
        this.data.closableDisabled = !response;
        this.csv = response ? csv : undefined;
        this.setAlert(!response);
      });
  }

  onContinue(event: string): void {
    if (event === SeModalOutputEvents.MAIN_ACTION) {
      this.continueOutput.emit(this.csv ?? '');
    }

    this.closeModal();
  }

  closeModal(): void {
    this.activatedModalService.close();
  }

  private setAlert(error: boolean): void {
    this.alert = {
      title: error
        ? 'SE_PADRO_CO2.MODAL.CSV_MODAL.CSV_ERROR'
        : 'SE_PADRO_CO2.MODAL.CSV_MODAL.CSV_SUCCESS',
      type: error ? SeAlertType.ERROR : SeAlertType.SUCCESS,
      list: [],
    };
  }
}
