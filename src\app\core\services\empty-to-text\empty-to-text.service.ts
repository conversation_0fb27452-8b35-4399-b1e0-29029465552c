import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Nullable } from 'se-ui-components-mf-lib';

@Injectable({
  providedIn: 'root',
})
export class EmptyToTextService {
  constructor(private translateService: TranslateService) {}

  transform(
    value: Nullable<string>,
    trueText?: Nullable<string>,
    falseText?: Nullable<string>,
  ): string {
    return value
      ? this.translateService.instant(
          trueText || 'UI_COMPONENTS.SELECT_BUTTON_BINARY.YES',
        ) +
          ' - ' +
          value
      : this.translateService.instant(
          falseText || 'UI_COMPONENTS.SELECT_BUTTON_BINARY.NO',
        );
  }
}
