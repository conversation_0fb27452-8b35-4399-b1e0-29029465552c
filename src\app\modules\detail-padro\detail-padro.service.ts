import { formatCurrency } from '@angular/common';
import { Inject, Injectable, LOCALE_ID } from '@angular/core';
import {
  BooleanToTextService,
  EmptyToDashService,
  LoginResponseService,
  SituationsStoreService,
  StorageService,
} from '@core/services';
import { TagColorService } from '@core/services/tag-color';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import {
  Co2EmissionsModalComponent,
  SharesModalComponent,
  TaxPeriodModalComponent,
} from '@shared/components';
import { DetallPadro } from '@shared/models';
import {
  SeAuthService,
  SeModalService,
  SeTagTheme,
} from 'se-ui-components-mf-lib';
import { DetailElement, TagDetailPadro } from './detail-padro.model';
import { EmptyToTextService } from '@app/core/services/empty-to-text';
import { IdentificationType } from '@app/core/models';
import { MaskIbanService } from '@app/core/services/mask-iban';

@Injectable({
  providedIn: 'root',
})
export class DetailPadroService {
  get isLoginSimple(): boolean {
    return this.storageData.profileUser === IdentificationType.LOGIN_SIMPLE;
  }

  constructor(
    private emptyToDashService: EmptyToDashService,
    private maskIbanService: MaskIbanService,
    private translateService: TranslateService,
    private seModalService: SeModalService,
    private booleanToTextService: BooleanToTextService,
    private emptyToTextService: EmptyToTextService,
    private tagColorService: TagColorService,
    private situationsStoreService: SituationsStoreService,
    private authService: SeAuthService,
    private storageData: StorageService,
    private login: LoginResponseService,
    @Inject(LOCALE_ID) private locale: string,
  ) {}

  getOwnerInfo(carDetail: DetallPadro): DetailElement[] {
    return [
      ...(!this.isLoginSimple
        ? [
            {
              title: this.getTitularNameLabel(),
              description: this.emptyToDashService.transform(
                ` ${carDetail.nomTitular} ${carDetail.cognom1Titular} ${carDetail.cognom2Titular}`,
              ),
            },
          ]
        : []),
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.NIF',
        description: this.emptyToDashService.transform(carDetail.nifTitular),
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.OWNER_MUNICIPALITY',
        description: !this.isLoginSimple
          ? this.emptyToDashService.transform(carDetail.municipiTitular)
          : 'SE_PADRO_CO2.DETAIL.LABELS.CATALONIA',
        tooltipText: 'SE_PADRO_CO2.DETAIL.LABELS.OWNER_MUNICIPALITY_TOOLTIP',
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.OWNERSHIP',
        description: `${this.emptyToDashService.transform(
          carDetail.percentajeTitular,
        )} %`,
      },
    ];
  }

  private getTitularNameLabel(): string {
    const isLegalPerson = this.login.isLegalPerson();
    return isLegalPerson
      ? 'SE_PADRO_CO2.DETAIL.LABELS.SOCIAL_REASON'
      : 'SE_PADRO_CO2.DETAIL.LABELS.FULL_NAME';
  }

  getPaymentsInfo(carDetail: DetallPadro): DetailElement[] {
    return [
      {
        title: `${this.translateService.instant('SE_PADRO_CO2.DETAIL.LABELS.TAX_BASE', { year: carDetail.exercici })}:`,
        description: `${this.emptyToDashService.transform(formatCurrency(Number(carDetail.baseImpostable), this.locale, 'g/km'))}`,
        tooltipText: `${this.translateService.instant('SE_PADRO_CO2.DETAIL.LABELS.TAX_BASE_TOOLTIP')}`,
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.TAX_QUOTA',
        description: `${this.emptyToDashService.transform(formatCurrency(carDetail.quota, this.locale, '€', 'EUR', '1.2-2'))}`,
        linkText: 'SE_PADRO_CO2.DETAIL.LABELS.SHOW_CALCULATION',
        linkCommand: () => this.showSharesModal(carDetail),
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.BONIFICATION',
        description: `${this.emptyToDashService.transform(formatCurrency(carDetail.bonificacio, this.locale, '€', 'EUR', '1.2-2'))}`,
        tooltipText: 'SE_PADRO_CO2.DETAIL.LABELS.BONIFICATION_TOOLTIP',
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.TOTAL',
        description: `${this.emptyToDashService.transform(formatCurrency(carDetail.total, this.locale, '€', 'EUR', '1.2-2'))}`,
        bold: true,
      },
    ];
  }

  getDomiciledInfo(carDetail: DetallPadro): DetailElement[] {
    return [
      {
        title: `${this.translateService.instant('SE_PADRO_CO2.DETAIL.LABELS.DOMICILIATED_FOR_EXERCISE', { year: carDetail.exercici })}:`,
        description: `${this.booleanToTextService.transform(!!carDetail.domicilat)}`,
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.ACCOUNT_NUMBER',
        description: `${this.maskIbanService.transform(carDetail.iban)}`,
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.DOMICILIATED_FOR_FUTURE_EXERCISES',
        description: `${this.booleanToTextService.transform(!!carDetail.domicilatSeguents)}`,
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.ACCOUNT_NUMBER_FOR_FUTURE_EXERCISES',
        description: `${this.maskIbanService.transform(carDetail.ibanSeguents)}`,
      },
    ];
  }

  getVehicleInfo(carDetail: DetallPadro): DetailElement[] {
    return [
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.CATEGORY',
        description: this.emptyToDashService.transform(carDetail.categoria),
        tooltipText: 'SE_PADRO_CO2.DETAIL.LABELS.CATEGORY_TOOLTIP',
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.BRAND',
        description: this.emptyToDashService.transform(carDetail.marca),
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.MODEL',
        description: this.emptyToDashService.transform(carDetail.model),
      },
      {
        title: 'SE_PADRO_CO2.LABELS.PLATE',
        description: this.emptyToDashService.transform(carDetail?.matricula),
      },
      ...this.getVehicleMunicipality(carDetail),
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.CO2_EMISSIONS',
        description: `${this.emptyToDashService.transform(formatCurrency(carDetail.co2, this.locale, 'g/km'))}`,
        linkText: 'SE_PADRO_CO2.DETAIL.LABELS.HOW_OBTAINED',
        linkCommand: () => this.showCo2Modal(carDetail),
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.PERIOD',
        description: `${this.emptyToDashService.transform(carDetail.periode)}  ${this.translateService.instant('SE_PADRO_CO2.DETAIL.LABELS.DAYS')}  `,
        tooltipText: 'SE_PADRO_CO2.DETAIL.LABELS.PERIOD_TOOLTIP',
        linkText: 'SE_PADRO_CO2.DETAIL.LABELS.HOW_OBTAINED',
        linkCommand: () => this.showTaxPeriodModal(carDetail),
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.BONIFICATION_TYPE',
        description: `${this.emptyToTextService.transform(carDetail.tipusBonificacio)}`,
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.EXEMPT',
        description: `${this.emptyToTextService.transform(carDetail.tipusExempcio)}`,
      },
      {
        title: 'SE_PADRO_CO2.DETAIL.LABELS.SITUATION',
        tagLabel: this.emptyToDashService.transform(carDetail.situacio),
        tagTheme: this.getSituationTagColor(carDetail.situacio as string),
      },
    ];
  }

  private getVehicleMunicipality(carDetail: DetallPadro): DetailElement[] {
    const isLegalPerson = this.login.isLegalPerson();
    return isLegalPerson
      ? [
          {
            title: 'SE_PADRO_CO2.DETAIL.LABELS.VEHICLE_MUNICIPALITY',
            description: `${this.emptyToDashService.transform(
              carDetail.municipiVehicle,
            )}`,
            tooltipText:
              'SE_PADRO_CO2.DETAIL.LABELS.VEHICLE_MUNICIPALITY_TOOLTIP',
          },
        ]
      : [];
  }

  public showCo2Modal(carDetail: DetallPadro): NgbModalRef {
    const modalRef = this.seModalService.openModal({
      severity: 'info',
      title: this.translateService.instant('SE_PADRO_CO2.MODAL.DETAIL.TITLE'),
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CLOSE',
      keyboard: true,
      centered: true,
      titleTextWeight: 'semi-bold',
      windowClass: 'bg-transparent',
      backdrop: true,
      hideIcon: false,
      size: 'xl',
      component: Co2EmissionsModalComponent,
    });
    modalRef.componentInstance.carDetail = carDetail;
    return modalRef;
  }

  public showTaxPeriodModal(carDetail: DetallPadro): NgbModalRef {
    const modalRef = this.seModalService.openModal({
      severity: 'info',
      title: this.translateService.instant('SE_PADRO_CO2.DETAIL.LABELS.PERIOD'),
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CLOSE',
      keyboard: true,
      centered: true,
      titleTextWeight: 'semi-bold',
      windowClass: 'bg-transparent',
      backdrop: true,
      hideIcon: false,
      component: TaxPeriodModalComponent,
    });
    modalRef.componentInstance.carDetail = carDetail;
    return modalRef;
  }

  private getSituationTagColor(situation: string): SeTagTheme {
    return this.tagColorService.getStatusCodeColor(
      this.situationsStoreService.getSituationCode(situation as string),
    );
  }

  getTags(carDetail: DetallPadro): TagDetailPadro[] {
    return [
      {
        text: `${this.translateService.instant('SE_PADRO_CO2.LABELS.EXERCISE')} ${carDetail.exercici}`,
        color: 'info',
      },
      {
        text: `${this.translateService.instant('SE_PADRO_CO2.DETAIL.LABELS.' + (carDetail.provisional ? 'PROVISIONAL' : 'DEFINITIVE'))}`,
        color: 'info',
      },
    ];
  }

  showSharesModal(carDetail: DetallPadro): NgbModalRef {
    const modalRef = this.seModalService.openModal({
      severity: 'info',
      title: this.translateService.instant(
        'SE_PADRO_CO2.DETAIL.TAX_QUOTA_BREAKDOWN',
      ),
      closable: true,
      closableLabel: 'UI_COMPONENTS.BUTTONS.CLOSE',
      keyboard: true,
      centered: true,
      titleTextWeight: 'semi-bold',
      windowClass: 'bg-transparent',
      backdrop: true,
      hideIcon: false,
      component: SharesModalComponent,
    });
    modalRef.componentInstance.carDetail = carDetail;
    return modalRef;
  }
}
