import { SeHttpResponse } from 'se-ui-components-mf-lib';
import { Vehicle, Motius, DocumentsUpload } from '@shared/models';

export interface ReasResumResponse extends SeHttpResponse {
  content: ReasResum;
}

export interface ReasResum {
  vehicles?: Vehicle[];
  titular?: string;
  quota?: number;
  motius?: Motius[];
  ibanNumeroCompte?: string;
  declaracioNumeroCompte?: boolean;
  documentNumeroCompte?: DocumentsUpload;
  exercici?: number;
}

export interface ReasPresentationResponse extends SeHttpResponse {
  content: ProcedureSavedReas;
}

export interface ProcedureSavedReas {
  idJustificant: string;
  fileName: string;
  idTramit: string;
}
