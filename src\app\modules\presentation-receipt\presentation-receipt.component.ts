import { Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import {
  AppRoutes,
  IdentificationType,
  ResourceProcessDocument,
} from '@core/models';
import {
  CustomRouterService,
  LoginResponseService,
  SpecificConfigurationService,
} from '@core/services';
import { StorageService } from '@core/services/storage';
import { Nullable, SeAuthService } from 'se-ui-components-mf-lib';
import { ProcessNameToDisplayInPresentationReceipt } from './presentation-receipt.model';
import {
  GetPendingPaymentsRequest,
  PaymentsEndpointsService,
} from '@app/shared/services';
import { Subject, takeUntil } from 'rxjs';

const BASE_TRANSLATION = `SE_PADRO_CO2.PRESENTATION_RECEIPT`;

@Component({
  selector: 'app-presentation-receipt',
  templateUrl: './presentation-receipt.component.html',
})
export class PresentationReceiptComponent implements OnInit, OnD<PERSON>roy {
  alertTitle: string = '';
  alertSubtitle: string = '';
  emailLabel: string = '';
  presenterId: Nullable<string>;
  resourceProcessReceiptName: Nullable<string>;
  rebutsPendents: Nullable<boolean>;
  resourceProcessReceiptId: Nullable<string>;
  idAddress?: string;
  csvAddress?: string;
  idContactData?: string;
  csvContactData?: string;
  showMsg2Asterisk: boolean = false;

  get isSelfPerson(): boolean {
    return this.storageData.profileUser === IdentificationType.NOM_PROPI;
  }

  private resourceProcessData: Nullable<ResourceProcessDocument>;

  // ratenow
  private readonly surveyFamily = 'FAM3';
  private readonly procedureDomiciliationId = 'F3CAS30';
  private readonly procedureAllegationId = 'F3CAS4';
  private readonly procedureReaId = 'F3CAS1.2';
  private readonly procedureAppealForReconsiderationId = 'F3CAS1.1';

  get showPanel(): boolean {
    const {
      outOfTimeToBeDomiciledButBeforeDefinitivePadro,
      inTimeToBeDomiciled,
      isProvisional,
    } = this.specificConfigurationService;

    if (
      this.resourceProcessData?.idFunctionalModule ===
        ProcessNameToDisplayInPresentationReceipt.Allegation ||
      this.resourceProcessData?.idFunctionalModule ===
        ProcessNameToDisplayInPresentationReceipt.Rea ||
      this.resourceProcessData?.idFunctionalModule ===
        ProcessNameToDisplayInPresentationReceipt.AppealForReconsideration
    )
      return false;

    if (
      this.resourceProcessData?.idFunctionalModule ===
      ProcessNameToDisplayInPresentationReceipt.Domiciliacions
    ) {
      return (
        outOfTimeToBeDomiciledButBeforeDefinitivePadro ||
        (inTimeToBeDomiciled && !!this.rebutsPendents) ||
        (!isProvisional && !!this.rebutsPendents)
      );
    }

    return !!this.rebutsPendents;
  }

  get panelTitle(): string {
    return 'SE_PADRO_CO2.PRESENTATION_RECEIPT.PENDING_PAYMENT_PANEL.TITLE_IN_PERIOD';
  }

  get panelSubtitle(): string {
    const translateKey =
      'SE_PADRO_CO2.PRESENTATION_RECEIPT.PENDING_PAYMENT_PANEL.';
    if (
      this.resourceProcessData?.idFunctionalModule ===
      ProcessNameToDisplayInPresentationReceipt.Domiciliacions
    ) {
      return this.handleDomiciliationRegistration();
    }

    if (
      this.resourceProcessData?.idFunctionalModule ===
        ProcessNameToDisplayInPresentationReceipt.Domiciliations_modified ||
      this.resourceProcessData?.idFunctionalModule ===
        ProcessNameToDisplayInPresentationReceipt.Domiciliations_unsubscribe
    ) {
      return `${translateKey}DETAIL_UNSUBSCRIBE_MODIFIED`;
    }

    return `${translateKey}DETAIL_TELEMATIC_PAYMENT`;
  }

  private handleDomiciliationRegistration(): string {
    const translateKey =
      'SE_PADRO_CO2.PRESENTATION_RECEIPT.PENDING_PAYMENT_PANEL.';

    if (this.specificConfigurationService.inTimeToBeDomiciled) {
      return `${translateKey}DETAIL_IN_PERIOD`;
    }

    if (
      this.specificConfigurationService
        .outOfTimeToBeDomiciledButBeforeDefinitivePadro
    ) {
      return `${translateKey}DETAIL`;
    }

    return `${translateKey}DETAIL_AFTER_PERIOD`;
  }

  private _unsubscribe: Subject<void> = new Subject();

  constructor(
    public specificConfigurationService: SpecificConfigurationService,
    private storageData: StorageService,
    private customRouter: CustomRouterService,
    private seAuthService: SeAuthService,
    private paymentsEndpointsService: PaymentsEndpointsService,
    private loginService: LoginResponseService,
  ) {}

  ngOnInit(): void {
    this.getPendingPayments();
    this.setupConfirmationFields();
    this.setShowMsg2Asterisk();
    if (!this.resourceProcessData) {
      this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
    }
  }

  setShowMsg2Asterisk(): void {
    this.showMsg2Asterisk =
      this.resourceProcessData?.idFunctionalModule ===
      ProcessNameToDisplayInPresentationReceipt.AppealForReconsideration;
  }

  private getPendingPayments(): void {
    const exerciciActual =
      this.resourceProcessData?.idFunctionalModule ===
      ProcessNameToDisplayInPresentationReceipt.Domiciliacions
        ? false
        : !this.specificConfigurationService.isProvisional;
    const request: GetPendingPaymentsRequest = {
      tipusAccess:
        this.loginService.user.tipusAccess || IdentificationType.NOM_PROPI,
      idPersTitular: this.loginService.user.idPersTitular || '',
      provisional: false,
      exerciciActual: exerciciActual,
      matricula: this.loginService.user.matricula || null,
    };

    this.paymentsEndpointsService
      .getPendingPayments(request)
      .pipe(takeUntil(this._unsubscribe))
      .subscribe((value) => (this.rebutsPendents = value));
  }

  ngOnDestroy(): void {
    this._unsubscribe.next();
    this._unsubscribe.complete();
    this.cleanStore();
  }

  protected goToList(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  private cleanStore(): void {
    this.storageData.clearDirectDebitRegistrationData();
    this.storageData.clearModificationData();
    this.storageData.processNameToDisplayPresentationReceipt = null;
  }

  private setupConfirmationFields(): void {
    const resourceProcessData =
      this.storageData.getResourceProcessDocumentData();

    this.resourceProcessData = resourceProcessData;
    this.alertTitle = `${BASE_TRANSLATION}.MODULE.${resourceProcessData?.idFunctionalModule}`;
    this.alertSubtitle = `${BASE_TRANSLATION}.ALERT_SUBTITLE.${resourceProcessData?.idFunctionalModule}`;
    this.emailLabel = `${BASE_TRANSLATION}.EMAIL_LABEL_BY_MODULE.${resourceProcessData?.idFunctionalModule}`;
    this.presenterId = '';
    this.resourceProcessReceiptName = resourceProcessData?.receipt?.nom;
    this.resourceProcessReceiptId = resourceProcessData?.receipt?.idPadoct;
    this.idAddress = resourceProcessData?.processedAddressResponse?.idDoc;
    this.csvAddress = resourceProcessData?.processedAddressResponse?.csv;
    this.idContactData = resourceProcessData?.contactData?.receiptId;
    this.csvContactData = resourceProcessData?.contactData?.receiptCsv;

    // ratenow
    if (this.storageData.processNameToDisplayPresentationReceipt) {
      this.setupSurvey();
    }
  }

  private getProcedureIdByProcessName(): string {
    const processName =
      this.storageData.processNameToDisplayPresentationReceipt;
    switch (processName) {
      case ProcessNameToDisplayInPresentationReceipt.Allegation:
        return this.procedureAllegationId;
      case ProcessNameToDisplayInPresentationReceipt.Domiciliation:
        return this.procedureDomiciliationId;
      case ProcessNameToDisplayInPresentationReceipt.Rea:
        return this.procedureReaId;
      default:
        return this.procedureAppealForReconsiderationId;
    }
  }

  // no se está usando la encuesta de mf-gestions-receipt porque tiene que aparecer debajo de todo este componente
  private setupSurvey(): void {
    const eventDetail = {
      familia: this.surveyFamily,
      tramite: this.getProcedureIdByProcessName(),
      querySelector: '.presentation-receipt__survey-container',
      produccion:
        this.seAuthService.getSessionStorageUser().environment === 'pro',
    };

    document.dispatchEvent(
      new CustomEvent('showSurveyEvent', { detail: eventDetail }),
    );
  }
}
