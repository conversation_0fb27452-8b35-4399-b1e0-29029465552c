import { Taxpayer } from '@modules/identifications/models';

export interface BankDetailInput {
  panelId?: string;
  ibanId?: string;
  iban?: string;
  ibanLabel?: string;
  isIbanRequired?: boolean;
  isPresenter?: boolean;
  taxpayers: Taxpayer[];
  DRValue?: boolean;
  dRLabel?: string;
  agreementCode?: string;
  disabled?: boolean;
  uploadFilesSynch?: boolean;
  docData?: DocData;
}

export interface DocData {
  entityId: string;
  idFunctionalModule: string;
  lstCodGTATSigedaType: string[];
  subtypes: string[];
}
