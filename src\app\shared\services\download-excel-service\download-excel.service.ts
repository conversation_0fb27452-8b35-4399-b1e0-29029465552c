import { Injectable } from '@angular/core';
import { environment } from '@environments/environment';
import {
  SeDocumentsService,
  SeHttpRequest,
  SeHttpService,
} from 'se-ui-components-mf-lib';
import {
  DownloadExcelRequest,
  DownloadExcelResponse,
} from './download-excel.model';

@Injectable({
  providedIn: 'root',
})
export class DownloadExcelEndpointService {
  constructor(
    private httpService: SeHttpService,
    private seDocumentService: SeDocumentsService,
  ) {}

  downloadExcel(request: DownloadExcelRequest): void {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlCo2,
      url: `llistat-padro/csv`,
      method: 'post',
      headers: request.headers,
      body: request.body,
    };

    this.httpService
      .post(httpRequest)
      .subscribe((response: DownloadExcelResponse) => {
        if (!response.content) return;

        this.seDocumentService.openFile(
          response.content.base64File,
          response.content.mimeType,
          response.content.fileName,
        );
      });
  }
}
