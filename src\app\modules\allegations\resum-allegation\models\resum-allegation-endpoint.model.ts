import { SeHttpResponse } from 'se-ui-components-mf-lib';
import {
  AllegationResum,
  ProcedureSavedAllegation,
} from './resum-allegation.model';

export interface AllegationResumResponse extends SeHttpResponse {
  content: AllegationResum;
}

export interface AllegationPresentationRequest {
  idAllegacio: string;
  nom?: string;
  cognoms?: string;
}
export interface AllegationPresentationResponse extends SeHttpResponse {
  content: ProcedureSavedAllegation;
}
