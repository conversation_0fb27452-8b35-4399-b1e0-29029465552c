<!-- FILTROS -->
<form [formGroup]="filterForm" class="mb-3">
  <!-- 1ra línea de filtros -->
  <div
    *ngIf="showTable && (!deviceService.isMobile() || !isProvisional)"
    class="search-filter px-4 pt-3 flex-nowrap"
  >
    <div class="row">
      <!-- SEARCHBAR -->
      <ng-container *ngTemplateOutlet="searchBarTemplate" />

      <!-- ACTIONS BUTTONS -->
      <ng-container
        *ngTemplateOutlet="
          actionButtonsTemplate;
          context: {
            isCoordinador: isCoordinador,
            isLoginSimple: isLoginSimple,
          }
        "
      />
    </div>
  </div>

  <!-- 2da línea de filtros - DESKTOP  -->
  <app-census-table-filters
    *ngIf="showFilters"
    class="d-md-block d-none"
    [filterForm]="filterForm"
    (handleApplyFilter)="getPadroData()"
  >
  </app-census-table-filters>

  <!-- TEMPLATES -->
  <ng-template
    #actionButtonsTemplate
    let-isCoordinador="isCoordinador"
    let-isLoginSimple="isLoginSimple"
  >
    <aside
      class="d-flex col ms-auto mb-3 flex-nowrap justify-content-md-end align-items-center flex-column flex-md-row"
      *ngIf="!isCoordinador"
    >
      <ng-container *ngIf="!isProvisional">
        <!-- OTHER ACTIONS DROPDOWN -->
        <se-button-dropdown
          *ngIf="!isLoginSimple"
          class="mb-3 mb-md-0 btn-100"
          [items]="dropdownButtonAnotherActions"
          [buttonOptions]="otherActionDropdownButtonOptions"
        >
          {{ 'SE_PADRO_CO2.BUTTONS.OTHER_ACTIONS' | translate }}
        </se-button-dropdown>
        <!-- PAY BUTTON -->
        <se-button
          class="ms-0 ms-md-2 mb-2 mb-md-0 btn-100"
          [type]="'button'"
          [btnTheme]="'primary'"
          [size]="'default'"
          [disabled]="!selectedRows.length"
          (onClick)="onPayButton()"
        >
          {{ 'SE_PADRO_CO2.LABELS.PAY' | translate }}
        </se-button>
      </ng-container>
      <!-- GO TO ALLEGATIONS -->
      <se-button
        *ngIf="showAllegations"
        class="ms-0 ms-md-2 mb-3 mb-md-0 btn-100"
        [type]="'button'"
        [btnTheme]="'secondary'"
        [size]="'default'"
        [disabled]="!selectedRows.length"
        (onClick)="onAllegationsButton()"
      >
        {{ 'SE_PADRO_CO2.BUTTONS.SUBMIT_APPEAL' | translate }}
      </se-button>
      <!-- DOMICILIATION -->
      <se-button
        *ngIf="isProvisional"
        class="ms-0 ms-md-2 mb-3 mb-md-0 btn-100"
        [type]="'button'"
        [btnTheme]="'primary'"
        [size]="'default'"
        (click)="openPaymentTypesModal()"
      >
        {{ 'SE_PADRO_CO2.BUTTONS.PAYMENT_OPTIONS' | translate }}
      </se-button>
    </aside>
  </ng-template>

  <ng-template #searchBarTemplate>
    <aside class="flex-row col-md-3 col-12 d-flex" *ngIf="canSearchByPlate">
      <se-input
        class="w-100 w-md-auto"
        formControlName="plate"
        [ariaLabel]="'SE_PADRO_CO2.LABELS.PLATE' | translate"
        [placeholder]="
          'SE_PADRO_CO2.PREVIOUS_EXERCISES.WRITE_PLATE' | translate
        "
        [type]="'text'"
        [showClear]="true"
      ></se-input>
      <se-button
        class="ms-1"
        type="btn"
        [btnTheme]="'secondary'"
        [icon]="'matSearchOutline'"
        [iconSize]="'1.5em'"
        [ariaLabel]="'SE_PADRO_CO2.BUTTONS.SEARCH' | translate"
        (click)="getPadroData()"
      >
      </se-button>
    </aside>
  </ng-template>
  <!-- TEMPLATES -->
</form>

<!-- BOTON FILTROS - MOBILE -->
<aside *ngIf="showFilters" class="mt-1 mb-4 d-block d-md-none">
  <se-button
    [type]="'button'"
    [btnTheme]="'secondary'"
    [size]="'default'"
    (onClick)="openModalFilter()"
  >
    {{ 'SE_PADRO_CO2.BUTTONS.FILTERS' | translate }}
  </se-button>
</aside>

<se-table
  *ngIf="showTable; else noData"
  [currentPage]="currentPage"
  [selectable]="true"
  [columns]="tableColumns"
  [data]="tableRows"
  [resizable]="true"
  [cellTemplatePriorityOrder]="'cell-row-column'"
  [lazyTotalRecords]="totalRecords"
  [lazy]="true"
  [showSelectAll]="totalRecords > MIN_ELEMENTS_TO_SHOW_FILTERS"
  [showPagination]="totalRecords > MIN_ELEMENTS_TO_SHOW_FILTERS"
  [showEmptyState]="true"
  [showRowsPerPage]="true"
  [clickableRows]="true"
  [rowsPerPageOptions]="rowsPerPageOptions"
  [paginationDownloadButton]="downloadButton"
  (paginationDownloadClick)="openDownloadExcelModal()"
  (onSelectionChange)="onSelectionChange($event)"
  (onPageChange)="handlePageChange($event)"
  (sortByColumn)="sortByColumn($event)"
  (onRowClick)="onRowClick($event)"
></se-table>

<ng-template #noData>
  <se-empty-state [icon]="'info'" [backgroundTheme]="'primary'" />
</ng-template>

<section
  *ngIf="showTable && deviceService.isMobile() && isProvisional"
  class="search-filter mt-3 px-4 pt-3 d-md-none"
>
  <ng-container
    *ngTemplateOutlet="
      actionButtonsTemplate;
      context: {
        isCoordinador: isCoordinador,
      }
    "
  />
</section>
