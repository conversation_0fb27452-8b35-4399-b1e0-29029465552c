import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { SeHttpRequest, SeHttpService } from 'se-ui-components-mf-lib';
import { environment } from 'src/environments/environment';
import {
  GetUserDataByIdPers,
  GetUserInfoResponse,
} from '../models/titular-data-modal.model';

@Injectable({
  providedIn: 'root',
})
export class TitularDataModalEndpointsService {
  constructor(private httpService: SeHttpService) {}

  getUserInfo = (
    request?: GetUserDataByIdPers,
  ): Observable<GetUserInfoResponse> => {
    const httpRequest: SeHttpRequest = {
      baseUrl: environment.baseUrlContribuent,
      url: `/v2/dades-contribuent`,
      method: 'post',
      body: request,
    };

    return this.httpService.post(httpRequest);
  };
}
