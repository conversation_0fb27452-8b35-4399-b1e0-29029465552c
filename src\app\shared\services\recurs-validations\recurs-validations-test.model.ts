import { VehicleRecurs, VehiclesSelectedInfo } from '@app/shared/models';
import {
  SelectedVehiclesReasValidationResponse,
  SelectedVehiclesValidationResponse,
  StartRecursRequest,
} from './recurs-validations-endpoints.model';
import { StatusCodes } from '@app/core/models';
import { NEVER, Subject } from 'rxjs';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { SeModal, SeModalOutputEvents } from 'se-ui-components-mf-lib';
import { CsvModalComponent } from '@app/shared/components/csv-modal/csv-modal.component';

export const DEFAULT_VEHICLES_RECURRIBLES: VehicleRecurs[] = [
  {
    matricula: 'matricula-test-1',
    marca: 'marca-test',
    model: 'model-test',
    exercici: '2025',
    codiSituacio: StatusCodes.Pending,
  },
];

export const DEFAULT_VEHICLES_INFO: VehiclesSelectedInfo = {
  provisional: false,
  idPersTitular: 'id-pers-titular-test',
  exercici: '2025',
  matriculas: ['matricula-test-1'],
  tipusAccess: 'tipus-access-test',
};

export const DEFAULT_VEHICLES_INFO_2_PLATES: VehiclesSelectedInfo = {
  ...DEFAULT_VEHICLES_INFO,
  matriculas: ['matricula-test-1', 'matricula-test-2'],
};

export const DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE: SelectedVehiclesValidationResponse =
  {
    validation: {
      vehiclesRecurribles: [],
      vehiclesAdvertirTermini: false,
      vehiclesForaTermini: false,
      csvNotificacioNecessari: false,
      allCsvNotificacio: false,
      previRea: false,
      previRecurs: false,
    },
    recursId: 'recurs-id-test',
  };

export const TEST_PREVI_REA_RESPONSE: SelectedVehiclesValidationResponse = {
  ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
  validation: {
    ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE.validation,
    previRea: true,
  },
};

export const TEST_PREVI_RECURS_RESPONSE: SelectedVehiclesValidationResponse = {
  ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
  validation: {
    ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE.validation,
    previRecurs: true,
  },
};

export const TEST_VEHICLES_ADVERTIR_TERMINI_RESPONSE: SelectedVehiclesValidationResponse =
  {
    ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
    validation: {
      ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE.validation,
      vehiclesAdvertirTermini: true,
    },
  };

export const TEST_VEHICLES_FORA_TERMINI_RESPONSE: SelectedVehiclesValidationResponse =
  {
    ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE,
    validation: {
      ...DEFAULT_SELECTED_VEHICLES_VALIDATION_RESPONSE.validation,
      vehiclesForaTermini: true,
    },
  };

export const DEFAULT_SELECTED_REAS_VEHICLES_VALIDATION_RESPONSE: SelectedVehiclesReasValidationResponse =
  {
    validation: {
      vehiclesRecurribles: [],
      vehiclesAdvertirTermini: false,
      vehiclesForaTermini: false,
      csvNotificacioNecessari: false,
      allCsvNotificacio: false,
      previRea: false,
      previRecurs: false,
    },
    id: 'recurs-id-test',
  };

export const DEFAULT_MODAL_SECONDARY_BUTTON_EVENT = new Subject<void>();
export const DEFAULT_MODAL_REF_SECONDARY_BUTTON: NgbModalRef = {
  close: () => {},
  componentInstance: {
    modalOutputEvent: NEVER,
    secondaryButtonEvent: DEFAULT_MODAL_SECONDARY_BUTTON_EVENT,
  },
} as NgbModalRef;

export const DEFAULT_MODAL_PRIMARY_BUTTON_EVENT =
  new Subject<SeModalOutputEvents>();
export const DEFAULT_MODAL_REF_PRIMARY_BUTTON = {
  close: () => {},
  componentInstance: {
    modalOutputEvent: DEFAULT_MODAL_PRIMARY_BUTTON_EVENT,
    modalSecondaryButtonEvent: NEVER,
  },
} as NgbModalRef;

export const DEFAULT_MODAL_REF_COMPONENT_INSTANCE = {
  vehicles: undefined,
  message: undefined,
  tramitProcess: undefined,
};

export const DEFAULT_OPEN_CSV_MODAL_REQUEST: SeModal = {
  severity: 'info',
  size: 'xl',
  title: 'SE_PADRO_CO2.REAS_PROCESS.VALIDATIONS_MODALS.MODAL_CSV.SINGLE.TITLE',
  closable: true,
  closableDisabled: true,
  closableLabel: 'SE_PADRO_CO2.BUTTONS.CONTINUE',
  secondaryButton: true,
  secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
  component: CsvModalComponent,
};

export const DEFAULT_WARNIG_MODAL_REQUEST: SeModal = {
  severity: 'warning',
  closable: true,
  closableLabel: 'SE_PADRO_CO2.BUTTONS.CONTINUE',
  secondaryButton: true,
  secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
};

export const DEFAULT_MODAL_CANNOT_PROCESS_REQUEST: SeModal = {
  severity: 'error',
  title:
    'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_CANNOT_PROCESS.TITLE',
  subtitle:
    'SE_PADRO_CO2.RECURS_PROCESS.VALIDATIONS_MODALS.MODAL_CANNOT_PROCESS.SINGLE.INFO',
  closable: true,
  closableLabel: 'SE_PADRO_CO2.BUTTONS.CLOSE',
};

export const DEFAULT_INIT_PROCEDURE_REQUEST = {
  advertimentTerminiAcceptat: undefined,
  nomesVehiclesValids: undefined,
  csvNotificacio: undefined,
};

export const DEFAULT_START_RECURS_REQUEST = {
  idPerssCens: DEFAULT_VEHICLES_INFO_2_PLATES.idPersTitular,
  exercici: Number(DEFAULT_VEHICLES_INFO_2_PLATES.exercici),
  matricules: DEFAULT_VEHICLES_INFO_2_PLATES.matriculas,
  tipusAcces: DEFAULT_VEHICLES_INFO_2_PLATES.tipusAccess,
  advertimentTerminiAcceptat: false,
  nomesVehiclesValids: false,
  csvNotificacio: undefined,
  nifRepresentant: undefined,
} as StartRecursRequest;
