import { Component, OnDestroy, OnInit } from '@angular/core';
import { Params, Router } from '@angular/router';
import { TranslateService } from '@ngx-translate/core';
import { Subject, takeUntil } from 'rxjs';

import { FeatureFlagService } from '@app/core/services/feature-flag';
import { AppRoutes, IdentificationType, StatusCodes } from '@core/models';
import {
  CustomRouterService,
  HeaderInfoService,
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
} from '@core/services';
import { environment } from '@environments/environment';
import {
  DetallPadro,
  DetallPadroResponse,
  VehiclesSelectedInfo,
} from '@shared/models';
import { PaymentsService } from '@shared/services';
import {
  Nullable,
  SeButton,
  SeMfConfigurationService,
} from 'se-ui-components-mf-lib';
import { ReceiptsUtilsService } from '../receipts/receipts-utils.service';
import { DetailPadroEndpointService } from './detail-padro-endpoint.service';
import {
  DetailAlertsEnum,
  DetailElement,
  TagDetailPadro,
} from './detail-padro.model';
import { DetailPadroService } from './detail-padro.service';

@Component({
  selector: 'app-detail-padro',
  templateUrl: './detail-padro.component.html',
  styleUrls: ['./detail-padro.component.scss'],
})
export class DetailPadroComponent implements OnInit, OnDestroy {
  detailAlertsEnum = DetailAlertsEnum;

  carDetail: Nullable<DetallPadro>;

  IMAGES = {
    CAR: 'green-car',
    BIKE: 'green-bike',
    HAND: 'green-hand',
    LIGTH: 'green-ligth',
    FARM: 'green-farm',
  };

  tags: TagDetailPadro[] = [];

  get panelDetallActionButton(): SeButton | undefined {
    return this.specificConfigurationService.inTimeToBeAlleged &&
      !this.isCoordinador &&
      this.carDetail?.provisional
      ? {
          label: 'SE_PADRO_CO2.BUTTONS.REQUEST_MODIFICATION',
        }
      : undefined;
  }

  get panelDomiciledActionButton(): SeButton | undefined {
    if (!this.carDetail) return;
    const { domicilat, domicilatSeguents } = this.carDetail as DetallPadro;
    return (domicilat || domicilatSeguents) &&
      //this.featureFlagSrv.modifyDomiciliation &&
      !this.isCoordinador &&
      this.carDetail.exercici === this.currentExercise
      ? {
          label: 'SE_PADRO_CO2.BUTTONS.MODIFY_DOMICILIATE',
        }
      : undefined;
  }

  panelCalculationActionButton: SeButton | undefined = {
    label: 'SE_PADRO_CO2.BUTTONS.REQUEST_MODIFICATION',
  };

  get isProvisional(): boolean {
    return this.specificConfigurationService?.isProvisional;
  }

  get currentExercise(): string {
    return this.specificConfigurationService.currentExercise;
  }

  get domiciled(): boolean {
    return this.carDetail?.domicilat || false;
  }

  get isLoginSimple(): boolean {
    return this.storageData.profileUser === IdentificationType.LOGIN_SIMPLE;
  }

  get isConveniat(): boolean {
    return this.storageData.profileUser === IdentificationType.CONVENIAT;
  }

  get isRepresentant(): boolean {
    return this.storageData.profileUser === IdentificationType.REPRESENTATIVE;
  }

  get isCivilServant(): boolean {
    return this.storageData.profileUser === IdentificationType.CIVIL_SERVANT;
  }

  get isSelfPerson(): boolean {
    return this.storageData.profileUser === IdentificationType.NOM_PROPI;
  }

  get isCoordinador(): boolean {
    return this.storageData.profileUser === IdentificationType.COORDINADOR;
  }

  get showExecutiveLink(): boolean {
    return (
      this.storageData.profileUser === IdentificationType.NOM_PROPI &&
      !this.mfConfigurationService.getIsMobileApp()
    );
  }

  get executiveLinkText(): string {
    return (
      'SE_PADRO_CO2.DETAIL.' +
      (this.showExecutiveLink
        ? 'EXECUTIVE_TEXT_WITH_LINK'
        : 'EXECUTIVE_TEXT_WITHOUT_LINK')
    );
  }

  get executiveLink(): string {
    const origin = window.location.origin;
    return `${origin}/${this.translateService.currentLang}/secured/el-meu-espai-atc/deutes`;
  }

  get showRegisterDomiciliation(): boolean {
    return this.featureFlagSrv.registerDomiciliation;
  }

  vehicleInfo: DetailElement[] = [];
  ownerInfo: DetailElement[] = [];
  domiciledInfo: DetailElement[] = [];
  paymentsInfo: DetailElement[] = [];

  statusCode: Nullable<StatusCodes>;

  StatusCodes = StatusCodes;

  private unsubscribe: Subject<void> = new Subject();

  constructor(
    public specificConfigurationService: SpecificConfigurationService,
    private router: Router,
    private translateService: TranslateService,
    private detailPadroEndpointService: DetailPadroEndpointService,
    private header: HeaderInfoService,
    private loginService: LoginResponseService,
    private detailPadroService: DetailPadroService,
    private paymentService: PaymentsService,
    private customRouter: CustomRouterService,
    private storageData: StorageService,
    private receiptsUtilsService: ReceiptsUtilsService,
    private headerInfoService: HeaderInfoService,
    private featureFlagSrv: FeatureFlagService,
    private readonly mfConfigurationService: SeMfConfigurationService,
  ) {
    // Intencionadamente vacío
  }

  ngOnInit(): void {
    const queryParams = this.router.routerState.snapshot.root.queryParams;
    this.statusCode = queryParams['codiSituacio'];

    this.setUpHeader();

    this.initPadroDetail(queryParams);
  }

  ngOnDestroy(): void {
    this.unsubscribe.next();
    this.unsubscribe.complete();
  }

  private setUpHeader(): void {
    this.header.reset();
    const title = this.translateService.instant('SE_PADRO_CO2.APP_TITLE');
    const titleMobile = this.translateService.instant(
      'SE_PADRO_CO2.APP_TITLE_MOBILE',
    );

    // Show header and set common title if any role flag is true
    if (
      this.isLoginSimple ||
      this.isCivilServant ||
      this.isRepresentant ||
      this.isConveniat ||
      this.isCoordinador
    ) {
      this.header.show();
      this.header.setTitleHeader(title, titleMobile);
    }

    if (this.isCivilServant) {
      this.header.setCivilServantHeader(true);
    }
    if (this.isRepresentant) {
      this.header.setRepresentantHeader(true);
    }
    if (this.isConveniat) {
      this.header.setConveniantHeader(true);
    }
    if (this.isCoordinador) {
      this.header.setCoordinatorHeader(
        this.storageData.coordinadorNifContribuent,
        true,
      );
    }
  }

  initPadroDetail(queryParams: Params): void {
    const {
      idPersTitular,
      tipusAccess,
      matricula,
      exercici,
      situacio,
      codiSituacio,
    } = queryParams;
    if (idPersTitular && matricula && exercici) {
      this.detailPadroEndpointService
        .getPadroDetail({
          idPersTitular,
          matricula,
          exercici,
          provisional: this.getIsProvisional(exercici),
          situacio,
          tipusAccess,
          codiSituacio,
        })
        .pipe(takeUntil(this.unsubscribe))
        .subscribe((data: DetallPadroResponse) => {
          if (data.content) {
            this.carDetail = data.content;
            this.vehicleInfo = this.detailPadroService.getVehicleInfo(
              this.carDetail,
            );
            this.ownerInfo = this.detailPadroService.getOwnerInfo(
              this.carDetail,
            );
            this.paymentsInfo = this.detailPadroService.getPaymentsInfo(
              this.carDetail,
            );
            this.domiciledInfo = this.detailPadroService.getDomiciledInfo(
              this.carDetail,
            );
            this.tags = this.detailPadroService.getTags(
              this.carDetail as DetallPadro,
            );
          } else {
            this.goBack();
          }
        });
    } else {
      this.goBack();
    }
  }

  private getIsProvisional(exercici: string): boolean {
    return Number(exercici) < Number(this.currentExercise)
      ? false
      : this.isProvisional;
  }

  goBack(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  getSrc(value: string): string {
    return `${environment.baseUrlCommons}/images/${value}.png`;
  }

  protected navigateToIdCat(): void {
    window.open('https://idcatmobil.cat/', '_blank');
  }

  protected navigateToUpdateDomiciled(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.MODIFY_DOMICILED);
  }
  protected navigateToAllegations(): void {
    const plates: string[] = [this.carDetail!.matricula];

    const vehiclesInfo: VehiclesSelectedInfo =
      this.receiptsUtilsService.getVehiclesSelectedInfoByPlates(
        plates,
        this.isProvisional,
        this.specificConfigurationService.currentExercise,
      );
    this.storageData.setVehiclesSelected(vehiclesInfo);
    this.storageData.clearIdTramit();
    this.customRouter.navigateByBaseUrl(AppRoutes.ALLEGATIONS);
  }

  protected navigateToCreateDomiciliation(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.DOMICILIATION);
  }

  getButtonLabel(): string {
    const {
      PROVISIONAL_NO_DOMICILED_IN_TIME_TO_BE_DOMICILED,
      PENDING_PAYMENT,
    } = DetailAlertsEnum;
    const condition = this.getCondition();
    let response: string = '';
    if (condition === PROVISIONAL_NO_DOMICILED_IN_TIME_TO_BE_DOMICILED)
      response = 'SE_PADRO_CO2.BUTTONS.DOMICILIAR_PAYMENTS';
    else if (condition === PENDING_PAYMENT)
      response = 'SE_PADRO_CO2.BUTTONS.PAY';
    return response;
  }

  getAlertList(): string[] {
    let response: string[] = [];
    const currentLang = this.translateService.store.currentLang;
    const { publicacioPadroDefinitiu } = this.specificConfigurationService;
    const condition: DetailAlertsEnum = this.getCondition();
    const currentYear = new Date(publicacioPadroDefinitiu).getFullYear();
    const pastYear = currentYear - 1;
    const day = new Date(publicacioPadroDefinitiu).getDate();
    const month = new Date(publicacioPadroDefinitiu).toLocaleString(
      currentLang,
      {
        month: 'long',
      },
    );
    const { fiPeriodeVoluntariPagament } = this.specificConfigurationService;
    const startDayFiPeriodeVoluntariPagament = new Date(
      fiPeriodeVoluntariPagament,
    ).getDate();
    const startMonthFiPeriodeVoluntariPagament = new Date(
      fiPeriodeVoluntariPagament,
    ).toLocaleString(currentLang, {
      month: 'long',
    });

    const PAYMENT_PERIOD_YEAR: string = this.translateService.instant(
      'SE_PADRO_CO2.DETAIL.PAYMENT_PERIOD_YEAR',
      {
        startDay: day,
        startMonth: month,
        startDayPayementPeriode: startDayFiPeriodeVoluntariPagament,
        startMonthPayementPeriode: startMonthFiPeriodeVoluntariPagament,
      },
    );

    switch (condition) {
      case DetailAlertsEnum.PROVISIONAL_DOMICILED:
        response = [
          this.translateService.instant('SE_PADRO_CO2.DETAIL.DOMICILED', {
            startDayPayementPeriode: startDayFiPeriodeVoluntariPagament,
            startMonthPayementPeriode: startMonthFiPeriodeVoluntariPagament,
          }),
        ];
        break;

      case DetailAlertsEnum.PROVISIONAL_NO_DOMICILED_IN_TIME_TO_BE_DOMICILED:
        response = [
          this.translateService.instant(
            'SE_PADRO_CO2.DETAIL.PAYMENT_PERIOD_YEAR_TO_BE_DOMICILIATED',
            { pastYear, day, month, currentYear },
          ),
        ];
        break;
      case DetailAlertsEnum.PROVISIONAL_NO_DOMICILED_OUT_OF_TIME_TO_DOMICILED:
        response = [PAYMENT_PERIOD_YEAR];
        break;
      case DetailAlertsEnum.DOMICILED:
        response = [
          this.translateService.instant('SE_PADRO_CO2.DETAIL.DOMICILED', {
            startDayPayementPeriode: startDayFiPeriodeVoluntariPagament,
            startMonthPayementPeriode: startMonthFiPeriodeVoluntariPagament,
          }),
        ];
        break;

      case DetailAlertsEnum.CANCELED:
        response = [
          this.translateService.instant('SE_PADRO_CO2.DETAIL.CANCELED'),
        ];
        break;
      default:
        response = [];
    }
    return response;
  }

  getCondition(): DetailAlertsEnum {
    const { isProvisional, domiciled, carDetail } = this;

    if (
      (carDetail?.codiSituacio === StatusCodes.In_review ||
        carDetail?.codiSituacio === StatusCodes.Provisional) &&
      isProvisional
    ) {
      return this.getInReviewOrProvisionalCondition(domiciled, carDetail);
    }

    if (carDetail?.codiSituacio === StatusCodes.Pending) {
      return DetailAlertsEnum.PENDING_PAYMENT;
    }

    if (
      carDetail?.codiSituacio === StatusCodes.Domiciled &&
      (domiciled || carDetail?.domicilatSeguents)
    ) {
      return DetailAlertsEnum.DOMICILED;
    }

    if (carDetail?.codiSituacio === StatusCodes.Executive) {
      return DetailAlertsEnum.EXECUTIVE;
    }

    if (carDetail?.codiSituacio === StatusCodes.Canceled) {
      return DetailAlertsEnum.CANCELED;
    }

    return '' as DetailAlertsEnum;
  }

  private getInReviewOrProvisionalCondition(
    domiciled: boolean,
    carDetail: Nullable<DetallPadro>,
  ): DetailAlertsEnum {
    if (domiciled || carDetail?.domicilatSeguents) {
      return DetailAlertsEnum.PROVISIONAL_DOMICILED;
    }

    if (
      !domiciled &&
      !carDetail?.domicilatSeguents &&
      this.specificConfigurationService.inTimeToBeDomiciled
    ) {
      return DetailAlertsEnum.PROVISIONAL_NO_DOMICILED_IN_TIME_TO_BE_DOMICILED;
    }

    if (
      !domiciled &&
      !carDetail?.domicilatSeguents &&
      this.specificConfigurationService.outOfTimeToBeDomiciled
    ) {
      return DetailAlertsEnum.PROVISIONAL_NO_DOMICILED_OUT_OF_TIME_TO_DOMICILED;
    }

    return '' as DetailAlertsEnum;
  }

  onButtonClick(): void {
    const {
      PROVISIONAL_NO_DOMICILED_IN_TIME_TO_BE_DOMICILED,
      PENDING_PAYMENT,
    } = DetailAlertsEnum;
    const condition = this.getCondition();
    if (condition === PROVISIONAL_NO_DOMICILED_IN_TIME_TO_BE_DOMICILED)
      this.navigateToCreateDomiciliation();
    else if (condition === PENDING_PAYMENT) {
      const user = this.loginService.user;
      const vehiclesInfo: VehiclesSelectedInfo = {
        provisional: this.isProvisional,
        exercici: this.carDetail!.exercici,
        vehicles: [
          {
            matricula: this.carDetail!.matricula,
            exercici: this.carDetail!.exercici,
          },
        ],
        nifTitular: user?.nifTitular as string,
        idPersTitular: user.idPersTitular as string,
      };

      this.paymentService.initPayment(vehiclesInfo);
    }
  }

  showHelpModal(): void {
    this.headerInfoService.showHelpModal();
  }
}
