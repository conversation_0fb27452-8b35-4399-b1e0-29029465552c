<se-modal
  [data]="data"
  (modalOutputEvent)="onSubmit($event)"
  (modalSecondaryButtonEvent)="closeModal('cancel')"
>
  <ng-container *ngTemplateOutlet="modal"></ng-container>
</se-modal>

<ng-template #modal>
  <div class="d-flex flex-column">
    <se-alert
      *ngIf="infoModalAlert"
      [title]="infoModalAlert.title"
      [type]="infoModalAlert.type"
      [list]="infoModalAlert.list"
      [closeButton]="true"
    >
      <div
        *ngIf="infoModalAlert.content"
        [innerHTML]="infoModalAlert.content"
      ></div>
    </se-alert>
  </div>
  <se-upload-files
    [tableColumns]="tableColumns"
    [modalTableColumns]="modalTableColumns"
    [hasActions]="hasActions"
    [hasModal]="hasModal"
    [showInput]="true"
    [info]="info"
    [subtitle]="true"
    [subtitleText]="subtitleText"
    [accept]="accept"
    [multiple]="multiple"
    [sizeLimitPerFile]="sizeLimitPerFile"
    [groupSizeLimit]="groupSizeLimit"
    [dropAreaTitlePreLinkText]="dropAreaTitlePreLinkText"
    [dropAreaTitleLinkText]="dropAreaTitleLinkText"
    [dropAreaTitlePostLinkText]="dropAreaTitlePostLinkText"
    [fileFormatSeparation]="fileFormatSeparation"
    [maxFiles]="maxFiles"
    [disabled]="disabled"
    [files]="uploadedFiles"
    [required]="required"
    [customTrackById]="customTrackById"
    (filesLoaded)="handleFileLoaded($event)"
    (editFile)="onEditFile($event)"
    (deleteFile)="onDeleteFile($event)"
  ></se-upload-files>
</ng-template>
