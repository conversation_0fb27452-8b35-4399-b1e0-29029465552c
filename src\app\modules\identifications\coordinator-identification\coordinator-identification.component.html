<form class="mt-2 d-flex flex-column" [formGroup]="coordinatorForm">
  <se-panel [title]="'SE_PADRO_CO2.PUBLIC_SERVANT.TITLE' | translate">
    <!-- TODO condition ngif when endpoint response -->
    <se-alert
      *ngIf="false"
      [title]="'SE_PADRO_CO2.ERRORS.NIF_NOT_FOUND' | translate"
      [type]="'error'"
      [closeButton]="false"
    >
    </se-alert>
    <div class="row align-items-center">
      <se-input
        class="col col-sm-auto"
        formControlName="nifTitular"
        [label]="'UI_COMPONENTS.PASSIVE_SUBJECT.NIF' | translate"
        [disabled]="false"
        [type]="'text'"
        [id]="'nif-id'"
      ></se-input>
    </div>
  </se-panel>
  <div class="d-flex flex-column flex-sm-row justify-content-sm-end mt-4">
    <se-button
      type="btn"
      [btnTheme]="'primary'"
      [disabled]="!coordinatorForm.valid"
      (click)="continue()"
    >
      <!-- TODO condition disabled -->
      {{ 'UI_COMPONENTS.BUTTONS.CONTINUE' | translate }}
    </se-button>
  </div>
</form>
