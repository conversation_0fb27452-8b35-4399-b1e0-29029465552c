import { Injectable } from '@angular/core';
import { AppRoutes, IdentificationType } from '@app/core/models';
import {
  SpecificConfigurationService,
  CustomRouterService,
  LoginResponseService,
  StorageService,
} from '@app/core/services';
import { VehiclesSelectedInfo } from '@app/shared/models';
import {
  DeviceService,
  Nullable,
  SeLoginService,
  SeModal,
  SeModalService,
} from 'se-ui-components-mf-lib';
import {
  CalculateDeferralPaymentRequest,
  CalculateDeferralPaymentResponse,
} from './deferral-payments-endpoints.model';
import { DeferralPaymentsEndpointsService } from './deferral-payments-endpoints.service';
import { take } from 'rxjs';
import { TranslateService } from '@ngx-translate/core';
import { BaseUrlService } from '@app/core/services/url-atc/url-atc.service';
import { NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { CsvModalComponent } from '@app/shared/components/csv-modal/csv-modal.component';

const TRANSLATE_KEY = 'SE_PADRO_CO2.DEFERRAL_PAYMENTS_PROCESS';

@Injectable({
  providedIn: 'root',
})
export class DeferralPaymentsService {
  constructor(
    private readonly storageService: StorageService,
    private readonly customRouter: CustomRouterService,
    private readonly loginService: LoginResponseService,
    private readonly endpointsService: DeferralPaymentsEndpointsService,
    private readonly modalService: SeModalService,
    private readonly configurationService: SpecificConfigurationService,
    private readonly translateService: TranslateService,
    private readonly seLoginService: SeLoginService,
    private readonly baseUrlService: BaseUrlService,
    private readonly deviceSrv: DeviceService,
  ) {}

  // AJORNAMENT FRACCIONAMENT
  public initDeferralPayment(
    vehiclesInfo: Nullable<VehiclesSelectedInfo>,
    continuarAmbValids?: boolean,
    csv?: string,
  ): void {
    this.storageService.setVehiclesSelected(vehiclesInfo);

    if (!vehiclesInfo) {
      return;
    }

    const totalAmount =
      vehiclesInfo.vehicles?.reduce((sum, v) => sum + (v.quota ?? 0), 0) ?? 0;
    const importMin =
      this.configurationService.importMinAjornamentFraccionament;

    if (totalAmount < importMin) {
      this.openImportErrorModal(importMin);
      return;
    }

    const request: CalculateDeferralPaymentRequest = this.getRequestBody(
      vehiclesInfo,
      continuarAmbValids,
      csv,
    );

    this.endpointsService
      .calculateDeferral(request)
      .pipe(take(1))
      .subscribe((response: Nullable<CalculateDeferralPaymentResponse>) => {
        if (response) {
          const { identificadorMui, referenciaMui, importeMui } = response;

          if (identificadorMui && referenciaMui && importeMui) {
            this.goToDeferralPaymentProcess(response);
          } else {
            this.openCommonModals(response, importMin, continuarAmbValids);
          }
        }
      });
  }

  private getRequestBody(
    vehiclesInfo: VehiclesSelectedInfo,
    continuarAmbValids?: boolean,
    csv?: string,
  ): CalculateDeferralPaymentRequest {
    const atesaPhone = this.storageService.civilServantVehiclePhone;
    const atesaDate = this.storageService.civilServantVehicleDate;
    const atesaHour = this.storageService.civilServantVehicleHour;

    return {
      idPersTitular: vehiclesInfo.idPersTitular,
      vehicles: vehiclesInfo.vehicles ?? [],
      provisional: vehiclesInfo.provisional,
      exercici: vehiclesInfo?.exercici,
      csv,
      continuarAmbValids,
      tipusAccess:
        this.loginService.user.tipusAccess || IdentificationType.NOM_PROPI,
      ...(atesaPhone && atesaDate && atesaHour
        ? {
            trucadaTelefonica: {
              numeroTelefon: atesaPhone,
              data: atesaDate,
              hora: atesaHour,
            },
          }
        : {}),
    };
  }

  private openCommonModals(
    response: CalculateDeferralPaymentResponse,
    importMin: number,
    continuarAmbValids = false,
  ): void {
    const {
      importError,
      pagamentExecutivaError,
      pagamentRecarregaError,
      pendentNotificacio,
      pagamentAltresError,
    } = response;

    if (importError) {
      this.openImportErrorModal(importMin);
    } else if (pagamentExecutivaError) {
      this.openExecutivaAlertModal();
    } else if (pagamentRecarregaError) {
      this.openRecarregaAlertModal();
    } else if (pagamentAltresError) {
      this.openAltresErrorModal();
    } else if (pendentNotificacio) {
      // If has an error or state --> Open modal
      this.openCSVModal(!response.allPendentNotificacio, continuarAmbValids);
    }
  }

  private openImportErrorModal(importMin: number): void {
    // Logic to open a modal for import error
    const modalRef = this.openModalError(
      TRANSLATE_KEY + '.MODALS.ERROR_IMPORT.TITLE',
      this.translateService.instant(
        TRANSLATE_KEY + '.MODALS.ERROR_IMPORT.MESSAGE',
        {
          importMin: importMin.toFixed(2),
        },
      ),
    );

    modalRef.componentInstance.modalOutputEvent.pipe(take(1)).subscribe(() => {
      modalRef.close();
    });
  }

  private openExecutivaAlertModal(): void {
    const canGoToElMeuEspai =
      this.storageService.profileUser === IdentificationType.NOM_PROPI &&
      !this.deviceSrv.isMobile();
    // Logic to open a modal for executiva error
    const modalRef = this.openModalError(
      TRANSLATE_KEY + '.MODALS.ERROR_EXECUTIVA.TITLE',
      TRANSLATE_KEY + '.MODALS.ERROR_EXECUTIVA.MESSAGE',
      canGoToElMeuEspai
        ? {
            closableLabel:
              TRANSLATE_KEY + '.MODALS.ERROR_EXECUTIVA.CLOSE_BUTTON',
            secondaryButton: true,
            secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
          }
        : {
            closableLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
          },
    );

    modalRef.componentInstance.modalOutputEvent.pipe(take(1)).subscribe(() => {
      canGoToElMeuEspai && this.baseUrlService.goToElMeuEspai('deutes');
      modalRef.close();
    });

    modalRef.componentInstance.modalSecondaryButtonEvent
      .pipe(take(1))
      .subscribe(() => {
        modalRef.close();
      });
  }

  private openRecarregaAlertModal(): void {
    // Logic to open a modal for recarrega error
    const modalRef = this.openModalError(
      TRANSLATE_KEY + '.MODALS.ERROR_RECARREGA.TITLE',
      TRANSLATE_KEY + '.MODALS.ERROR_RECARREGA.MESSAGE',
    );

    modalRef.componentInstance.modalOutputEvent.pipe(take(1)).subscribe(() => {
      modalRef.close();
    });
  }

  private openAltresErrorModal(): void {
    // Logic to open a modal for altres error
    const modalRef = this.openModalError(
      TRANSLATE_KEY + '.MODALS.ERROR_ALTRES.TITLE',
      TRANSLATE_KEY + '.MODALS.ERROR_ALTRES.MESSAGE',
    );

    modalRef.componentInstance.modalOutputEvent.pipe(take(1)).subscribe(() => {
      modalRef.close();
    });
  }

  private goToDeferralPaymentProcess(
    response: CalculateDeferralPaymentResponse,
  ): void {
    this.storageService.setDeferralPaymentVehicles(response);
    this.customRouter.navigateByBaseUrl(AppRoutes.DEFERRAL_PAYMENTS);
  }

  async getURLFormSEU(
    response: CalculateDeferralPaymentResponse,
  ): Promise<string> {
    const seu = (await this.seLoginService.getSeuUrlByEnv())?.content;
    const urlBase =
      seu +
      '/' +
      this.translateService.currentLang +
      '/OficinaVirtual/Pagines/TRAjornaments.aspx';
    return `${urlBase}?Tipustramit=L&TipusImpost=820&Referencia=${response.referenciaMui}&Identificacio=${response.identificadorMui}&Import=${response.importeMui}&Idioma=${this.translateService.currentLang.toUpperCase()}&URLRetorn=${response.urlRetorn}&Origen=SRC`;
  }

  private openModalError(
    title: string,
    subtitle: string,
    modalData?: SeModal,
  ): NgbModalRef {
    return this.modalService.openModal({
      severity: 'error',
      size: 'lg',
      title,
      subtitle,
      closable: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.CLOSE',
      ...modalData,
    });
  }

  private openCSVModal(multiple: boolean, continuarAmbValids?: boolean): void {
    const modalRef = this.modalService.openModal({
      severity: 'info',
      size: 'xl',
      title:
        'SE_PADRO_CO2.MODAL.CSV_MODAL.' +
        (multiple ? 'MULTIPLE' : 'SINGLE') +
        '.TITLE',
      closable: true,
      closableDisabled: true,
      closableLabel: 'SE_PADRO_CO2.BUTTONS.CONTINUE',
      secondaryButton: true,
      secondaryButtonLabel: 'SE_PADRO_CO2.BUTTONS.CANCEL',
      component: CsvModalComponent,
    });

    modalRef.componentInstance.multiple = multiple;

    modalRef.componentInstance.continueOutput
      .pipe(take(1))
      .subscribe((csv: string) => {
        this.initDeferralPayment(
          this.storageService.getVehiclesSelected(),
          continuarAmbValids,
          csv,
        );
      });
  }
}
