import { Component, Input } from '@angular/core';
import { SpecificConfigurationService } from '@app/core/services';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { TranslateService } from '@ngx-translate/core';
import { CodiCategory, DetallPadro } from '@shared/models';
import {
  Column,
  DeviceService,
  Nullable,
  Row,
  SeModal,
} from 'se-ui-components-mf-lib';

interface Ranges {
  '0-160': number;
  '0-120': number;
  '120-140': number;
  '140-160': number;
  '160-200': number;
  more_than_160: number;
  more_than_200: number;
}

@Component({
  selector: 'app-shares-modal',
  templateUrl: './shares-modal.component.html',
  styleUrls: ['./shares-modal.component.scss'],
})
export class SharesModalComponent {
  private _carDetail: Nullable<DetallPadro>;
  private ranges: Nullable<Ranges>;

  @Input() data: SeModal | undefined;

  @Input() set carDetail(carDetail: DetallPadro) {
    this._carDetail = carDetail;
    this.setRows();
    this.setTotal();
    this.setTotalTaxQuota();
  }

  get carDetail(): DetallPadro {
    return this._carDetail as DetallPadro;
  }

  rows: Row[] = [];

  tableColumns: Column[] = [
    {
      header: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.CO2_EMISSION_BANDS',
      tooltip: false,
      key: 'band',
      size: 35,
      cellComponentName: 'defaultCellComponent',
      resizable: true,
      cellConfig: {},
    },
    {
      header: '€/gr',
      tooltip: false,
      key: 'price',
      size: 10,
      cellComponentName: 'defaultCellComponent',
      resizable: true,
      cellConfig: {
        ngStyle: {
          'text-align': this.deviceSrv.isMobile() ? 'left' : 'right',
        },
      },
    },
    {
      header: ' ',
      tooltip: false,
      key: 'x',
      size: '10px',
      cellComponentName: 'defaultCellComponent',
      resizable: true,
    },
    {
      header: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.EMISSIONS_BAND',
      tooltip: false,
      key: 'emissions',
      cellComponentName: 'currencyCellComponent',
      resizable: true,
      cellConfig: {
        currencyCode: 'g/km',
      },
    },
    {
      header: 'SE_PADRO_CO2.MODAL.DETAIL.TABLE.IMPORT_TRAM',
      tooltip: false,
      key: 'finalPrice',
      cellComponentName: 'currencyCellComponent',
      resizable: true,
      cellConfig: {
        align: this.deviceSrv.isMobile() ? 'left' : 'right',
      },
    },
  ];

  total: number = 0;
  totalTaxQuota: number = 0;

  get yearDays(): number {
    const year =
      this._carDetail?.exercici ||
      this.specificConfigurationService.currentDate.getFullYear();
    const date = new Date(Number(year), 1, 29);

    return date.getMonth() === 1 ? 366 : 365;
  }

  constructor(
    private activatedModalService: NgbActiveModal,
    private translateService: TranslateService,
    private deviceSrv: DeviceService,
    public specificConfigurationService: SpecificConfigurationService,
  ) {}

  closeModal(): void {
    this.rows = [];
    this.activatedModalService.close();
  }

  private setRanges(): void {
    const baseImpostable = Number(this._carDetail?.baseImpostable);

    this.ranges = {
      '0-120': baseImpostable > 120 ? 120 : baseImpostable,
      '0-160': baseImpostable > 160 ? 160 : baseImpostable,
      '120-140':
        baseImpostable > 140
          ? 20
          : baseImpostable < 120
            ? 0
            : baseImpostable - 120,
      '140-160':
        baseImpostable > 160
          ? 20
          : baseImpostable < 140
            ? 0
            : baseImpostable - 140,
      '160-200':
        baseImpostable > 200
          ? 40
          : baseImpostable < 160
            ? 0
            : baseImpostable - 160,
      more_than_160: baseImpostable > 160 ? baseImpostable - 160 : 0,
      more_than_200: baseImpostable > 200 ? baseImpostable - 200 : 0,
    };
  }

  private setTotal(): void {
    if (this.rows) {
      let total = 0;
      this.rows.forEach((row) => {
        total += Number(
          String(row.data['finalPrice'].value)
            .replace('€', '')
            .replace(',', '.'),
        );
      });
      this.total = total;
    }
  }

  private setTotalTaxQuota(): void {
    if (this._carDetail?.totalDies && this._carDetail?.percentajeTitular) {
      this.totalTaxQuota =
        ((Math.round(this.total * 100) / 100) *
          (this._carDetail?.totalDies / this.yearDays) *
          this._carDetail?.percentajeTitular) /
        100;
    }
  }

  private setRows(): void {
    if (!this._carDetail) return;
    this.setRanges();
    if (this.carDetail.categoria === CodiCategory.N1) {
      this.setRowsN1();
    } else {
      this.setRestRows();
    }
  }

  private setRowsN1(): void {
    if (!this.ranges) return;

    this.rows = [
      {
        data: {
          band: {
            value: `${this.translateService.instant('SE_PADRO_CO2.LABELS.TO')} 160`,
          },
          price: {
            value: '0,00 €',
          },
          x: { value: 'X' },
          emissions: { value: this.ranges['0-160'] },
          finalPrice: { value: 0 },
        },
      },
      {
        data: {
          band: {
            value: `${this.translateService.instant('SE_PADRO_CO2.MODAL.DETAIL.TABLE.MORE_THAN', { from: 160 })}`,
          },
          price: {
            value: '0,30 €',
          },
          x: { value: 'X' },
          emissions: { value: this.ranges.more_than_160 },
          finalPrice: {
            value: this.ranges.more_than_160 * 0.3,
          },
        },
      },
    ];
  }

  private setRestRows(): void {
    if (!this.ranges) return;

    this.rows = [
      {
        data: {
          band: {
            value: `${this.translateService.instant('SE_PADRO_CO2.LABELS.TO')} 120`,
          },
          price: {
            value: '0,00 €',
          },
          x: { value: 'X' },
          emissions: { value: this.ranges['0-120'] },
          finalPrice: { value: 0 },
        },
      },
      {
        data: {
          band: {
            value: `${this.translateService.instant('SE_PADRO_CO2.MODAL.DETAIL.TABLE.FROM_TO', { from: 120, to: 140 })}`,
          },
          price: {
            value: '0,55 €',
          },
          x: { value: 'X' },
          emissions: { value: this.ranges['120-140'] },
          finalPrice: {
            value: this.ranges['120-140'] * 0.55,
          },
        },
      },
      {
        data: {
          band: {
            value: `${this.translateService.instant('SE_PADRO_CO2.MODAL.DETAIL.TABLE.FROM_TO', { from: 140, to: 160 })}`,
          },
          price: {
            value: '0,65 €',
          },
          x: { value: 'X' },
          emissions: { value: this.ranges['140-160'] },
          finalPrice: {
            value: this.ranges['140-160'] * 0.65,
          },
        },
      },
      {
        data: {
          band: {
            value: `${this.translateService.instant('SE_PADRO_CO2.MODAL.DETAIL.TABLE.FROM_TO', { from: 160, to: 200 })}`,
          },
          price: {
            value: '0,80 €',
          },
          x: { value: 'X' },
          emissions: { value: this.ranges['160-200'] },
          finalPrice: {
            value: this.ranges['160-200'] * 0.8,
          },
        },
      },
      {
        data: {
          band: {
            value: `${this.translateService.instant('SE_PADRO_CO2.MODAL.DETAIL.TABLE.MORE_THAN', { from: 200 })}`,
          },
          price: {
            value: '1,10 €',
          },
          x: { value: 'X' },
          emissions: { value: this.ranges.more_than_200 },
          finalPrice: {
            value: this.ranges.more_than_200 * 1.1,
          },
        },
      },
    ];
  }
}
