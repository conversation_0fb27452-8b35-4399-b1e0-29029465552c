::ng-deep {
  .captcha-container {
    justify-content: flex-start;
  }

  .simple-login {
    &__success-alert {
      display: flex;

      .alert {
        position: fixed !important;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 250px !important;
        height: auto;
        margin: 30px 0;
        box-shadow: -3px 5px 15px var(--gray-400);
        animation: success-alert 4s;
        z-index: 100;
      }
    }
  }
}

@keyframes success-alert {
  0% {
    opacity: 0;
  }

  25% {
    opacity: 1;
  }

  50% {
    opacity: 1;
  }

  75% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}
