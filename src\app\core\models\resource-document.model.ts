import { iDocumentPadoct } from 'se-ui-components-mf-lib';
import { Co2User } from './login.model';
import { AddressTramit, ContactDataOutput } from '@app/shared/models';

export interface ResourceProcessDocument {
  rebutsPendents?: boolean;
  id: string;
  idFunctionalModule: string;
  technicalKey: string;
  passiveSubject: Co2User;
  presentator: Co2User;
  state: string;
  statementOfLegalRepresentation: boolean;
  statementOfAccountNumber: boolean;
  accountNumber: string;
  receipt: iDocumentPadoct;
  processedAddressResponse?: AddressTramit;
  contactData?: ContactDataOutput;
}
