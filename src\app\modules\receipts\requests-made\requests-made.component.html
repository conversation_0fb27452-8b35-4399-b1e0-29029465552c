<div class="row">
  <div class="col-3">
    <form [formGroup]="filterForm">
      <se-datepicker
        formControlName="from"
        [placeholder]="'dd/mm/aaaa'"
        [id]="'date-from'"
        [label]="'SE_PADRO_CO2.LABELS.FROM' | translate"
        [maxDate]="today"
        [showTime]="false"
        [showIcon]="true"
        [inline]="false"
        [disabled]="false"
        [selectionMode]="'single'"
        [tooltip]="false"
      ></se-datepicker>
    </form>
  </div>
  <div class="col-3">
    <form [formGroup]="filterForm">
      <se-datepicker
        formControlName="to"
        [placeholder]="'dd/mm/aaaa'"
        [id]="'date-to'"
        [label]="'SE_PADRO_CO2.LABELS.TO' | translate"
        [minDate]="today"
        [showTime]="false"
        [showIcon]="true"
        [inline]="false"
        [disabled]="false"
        [selectionMode]="'single'"
        [tooltip]="false"
      ></se-datepicker>
    </form>
  </div>
</div>
<p class="mt-3">
  {{ 'SE_PADRO_CO2.REQUESTS_MADE.ORDERED_BY_DATE' | translate }}
</p>
<div class="card-container mt-3" *ngFor="let request of requests">
  <div class="row">
    <div class="col-6 d-flex justify-content-start">
      <span>{{ request.dataHora | date: 'dd/MM/yyyy' }}</span>
      <span>{{ request.tipusGestio }}</span>
      <se-tag [tagTheme]="theme" [closable]="false">
        {{ request.estat }}
      </se-tag>
    </div>
    <div class="col-6 d-flex justify-content-end">
      <se-button
        class="button-align"
        [btnTheme]="'secondary'"
        [icon]="'matFileDownloadOutline'"
        [iconPosition]="'right'"
        >{{ 'SE_PADRO_CO2.REQUESTS_MADE.DOWNLOAD_RECEIPT' | translate }}
      </se-button>
    </div>
  </div>
  <div class="row mt-3">
    <div class="col-2 d-flex flex-column">
      <span>{{ 'SE_PADRO_CO2.LABELS.EXERCISE' | translate }}</span>
      <span>{{ getExercise(request.dataHora) }}</span>
    </div>
    <div class="col-2 d-flex flex-column">
      <span>{{ 'SE_PADRO_CO2.LABELS.PLATE' | translate }}</span>
      <!-- TODO agregar el . -->
      <span>{{ request }}</span>
    </div>
  </div>
</div>
<se-empty-state
  *ngIf="!requests?.length"
  [backgroundTheme]="'default'"
  [message]="'SE_PADRO_CO2.REQUESTS_MADE.NO_VALUES_FOUND' | translate"
  [icon]="'search'"
>
</se-empty-state>
