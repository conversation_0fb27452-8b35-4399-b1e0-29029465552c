import { Component, Input } from '@angular/core';

import { IdentificationType } from '@app/core/models';
import {
  LoginResponseService,
  SpecificConfigurationService,
  StorageService,
  HeaderInfoService,
} from '@app/core/services';
import { HelpModalOptionEnum } from '@app/shared/components';
import type { DetallPadro, VehiclesSelectedInfo } from '@app/shared/models';
import { RecursValidationsService } from '@app/shared/services/recurs-validations';
import {
  SeButtonSizeEnum,
  SeButtonThemeEnum,
  type Nullable,
} from 'se-ui-components-mf-lib';

/*
# 5.3.5 Bloc recursos i reclamacions

Es mostrarà sota del bloc de domiciliació, només quan ens trobem en període de
padró definitiu i exercici en curs i condicionat al perfil de l’usuari:

- En nom propi: només es mostrarà l’enllaç per presentar recurs de reposició.
  L’enllaç per presentar REA no es mostrarà.
- Login simple i coordinador ATC: no es mostrarà el bloc.
- Representant, conveniat, funcionari habilitat: es mostrarà el bloc amb
  els 2 enllaços.

El botó Ajuda obrirà l’ajuda contextual lateral amb l’apartat de recursos
seleccionat, mostrant informació sobre en quins casos es pot presentar un
recurs o una REA.

Els enllaços porten, respectivament, a les accions de recurs i reclamació
disponibles desde la visualització del llistat en padró definitiu.
*/
@Component({
  selector: 'app-recursos-i-reclamacions',
  template: `
    <div
      *ngIf="isComponentVisible"
      class="app-recursos-i-reclamacions-container"
    >
      <div class="row">
        <p class="col">
          {{
            'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.QUESTION' | translate
          }}
        </p>
        <se-button
          class="col-auto"
          [size]="SeButtonSizeEnum.SMALL"
          [btnTheme]="SeButtonThemeEnum.SECONDARY"
          (onClick)="onHelpButtonClick()"
        >
          {{
            'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.HELP_BUTTON'
              | translate
          }}
        </se-button>
        <se-button
          [btnTheme]="SeButtonThemeEnum.LINK"
          (onClick)="onPresentarRecursButtonClick()"
        >
          {{
            'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.PRESENTAR_RECURS_LINK'
              | translate
          }}
        </se-button>

        <se-button
          [btnTheme]="SeButtonThemeEnum.LINK"
          *ngIf="isReaLinkVisible"
          (onClick)="onPresentarReaButtonClick()"
        >
          {{
            'SE_PADRO_CO2.DETAIL.RECURSOS_I_RECLAMACIONS.PRESENTAR_REA_LINK'
              | translate
          }}
        </se-button>
      </div>
    </div>
  `,
  styleUrls: ['./recursos-i-reclamacions.component.scss'],
})
export class RecursosIReclamacionsComponent {
  readonly SeButtonSizeEnum = SeButtonSizeEnum;
  readonly SeButtonThemeEnum = SeButtonThemeEnum;

  @Input() vehicleDetail: Nullable<DetallPadro>;

  constructor(
    private readonly config: SpecificConfigurationService,
    private readonly storage: StorageService,
    private readonly loginService: LoginResponseService,
    private readonly recursValidationService: RecursValidationsService,
    private readonly headerInfoService: HeaderInfoService,
  ) {
    // Intencionadament buit
  }

  get isComponentVisible(): boolean {
    return (
      this.config.isProvisional === false &&
      this.vehicleDetail?.exercici === this.config.currentExercise &&
      this.storage.profileUser !== IdentificationType.LOGIN_SIMPLE &&
      this.storage.profileUser !== IdentificationType.COORDINADOR
    );
  }

  get isReaLinkVisible(): boolean {
    return (
      this.storage.profileUser === IdentificationType.CONVENIAT ||
      this.storage.profileUser === IdentificationType.REPRESENTATIVE
    );
  }

  onHelpButtonClick(): void {
    this.headerInfoService.showHelpModal(HelpModalOptionEnum.RECURS_Y_REA);
  }

  onPresentarRecursButtonClick(): void {
    const vehiclesInfo: VehiclesSelectedInfo = this.getVehiclesInfo();

    this.recursValidationService.initRecurs({ vehiclesInfo });
    this.storage.setReasonsSelected(null);
  }

  onPresentarReaButtonClick(): void {
    const vehiclesInfo: VehiclesSelectedInfo = this.getVehiclesInfo();

    this.recursValidationService.initReas({ vehiclesInfo });
    this.storage.setReasonsSelected(null);
  }

  private getVehiclesInfo(): VehiclesSelectedInfo {
    return {
      provisional: this.config.isProvisional,
      matriculas: [this.vehicleDetail!.matricula],
      exercici: this.vehicleDetail!.exercici,
      nifTitular: this.vehicleDetail!.nifTitular,
      idPersTitular: this.loginService.user.idPersTitular!,
      tipusAccess: this.loginService.user.tipusAccess,
    };
  }
}
