import { Component, OnInit } from '@angular/core';
import { Location } from '@angular/common';
import {
  FormIbanValue,
  PaymentData,
  PaymentType,
} from './models/payments.model';
import {
  CustomRouterService,
  StorageService,
  HeaderInfoService,
} from '@core/services';
import { VehiclePayment } from '@shared/models';
import { Column, Row } from 'se-ui-components-mf-lib';
import { TranslateService } from '@ngx-translate/core';
import {
  AppRoutes,
  FunctionalModuleEnum,
  IdentificationType,
} from '@core/models';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';

export const BASE_TRANSLATE = 'SE_PADRO_CO2.PAYMENTS_PROCESS';

@Component({
  selector: 'app-payments-process',
  templateUrl: './payments-process.component.html',
  styleUrls: ['./payments-process.component.scss'],
})
export class PaymentsProcessComponent implements OnInit {
  paymentType?: PaymentType;
  ibanValue: string = '';
  ibanFormValid: boolean = false;

  paymentData: PaymentData | undefined;
  totalAmount: number = 0;

  vehiclesColumns: Column[] = [];
  vehiclesRows: Row[] = [];

  showTable = true;

  plates: string[] = [];

  get isLoginSimple(): boolean {
    return this.storageService.profileUser === IdentificationType.LOGIN_SIMPLE;
  }

  get isConveniat(): boolean {
    return this.storageService.profileUser === IdentificationType.CONVENIAT;
  }

  get isRepresentant(): boolean {
    return (
      this.storageService.profileUser === IdentificationType.REPRESENTATIVE
    );
  }

  get isCivilServant(): boolean {
    return this.storageService.profileUser === IdentificationType.CIVIL_SERVANT;
  }

  get isSelfPerson(): boolean {
    return this.storageService.profileUser === IdentificationType.NOM_PROPI;
  }

  get idFunctionalModule(): FunctionalModuleEnum {
    return FunctionalModuleEnum.PAGAMENT_LIQUIDACIONS;
  }

  protected disableContinueButton: boolean = false;

  constructor(
    private location: Location,
    private storageService: StorageService,
    private translateService: TranslateService,
    private customRouter: CustomRouterService,
    private headerInfoService: HeaderInfoService,
    private proceduresHeaderService: ProceduresHeaderService,
  ) {
    // this.headerInfoService.setFullscreenMode(true);
  }

  ngOnInit(): void {
    if (this.storageService.getPaymentVehicles()) {
      const { total, vehicles, idBBDD, refPago, idPagament } =
        this.storageService.getPaymentVehicles();

      this.proceduresHeaderService.setupPaymentHeader(vehicles);

      this.totalAmount = total;
      this.vehiclesColumns = this.getVehiclesColumns();
      this.vehiclesRows = this.getVehiclesRows(vehicles);
      this.plates = vehicles?.map((vehicle) => vehicle.matricula) || [];
      this.paymentData = {
        idBBDD,
        refPago,
        idPagament,
      };
      this.storageService.paymentData = this.paymentData;
    } else {
      //TODO
      this.headerInfoService.setFullscreenMode(false);
      this.location.back();
    }
  }

  handlePayButton(event: Event): void {
    const ev = event as CustomEvent<PaymentType>;
    if (ev.detail === PaymentType.CUENTA) {
      this.customRouter.navigateByBaseUrl(AppRoutes.PAYMENTS_CONFIRMATION);
    } else {
      this.showTable = false;
    }
  }

  // boton de volver al listado
  goToReceipts(): void {
    this.headerInfoService.setFullscreenMode(false);
    this.customRouter.navigateByBaseUrl(AppRoutes.RECEIPTS);
  }

  private getVehiclesRows(vehicles: VehiclePayment[]): Row[] {
    return (vehicles ?? []).map((vehicle) => {
      return {
        data: {
          vehicle: {
            value: `${vehicle.marca} - ${vehicle.matricula}`,
          },
          exercici: {
            value: vehicle.exercici,
          },
          quota: {
            value: vehicle.quota,
          },
          recarrec: {
            value: vehicle.recarrega,
          },
          total: {
            value: vehicle.total,
          },
        },
      };
    }) as Row[];
  }

  private getVehiclesColumns(): Column[] {
    return [
      {
        key: 'vehicle',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.VEHICLE`,
        ),
        size: 10,
      },
      {
        key: 'exercici',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.EXERCICI`,
        ),
        cellConfig: {
          align: 'right',
        },
        size: 8,
      },
      {
        key: 'quota',
        header: this.translateService.instant(`${BASE_TRANSLATE}.TABLE.QUOTA`),
        size: 8,
        cellConfig: {
          align: 'right',
        },
        cellComponentName: 'currencyCellComponent',
      },
      {
        key: 'recarrec',
        header: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.RECARREC`,
        ),
        size: 8,
        cellConfig: {
          align: 'right',
        },
        cellComponentName: 'currencyCellComponent',
        tooltip: true,
        tooltipText: this.translateService.instant(
          `${BASE_TRANSLATE}.TABLE.RECARREC_TOOLTIP`,
        ),
      },
      {
        key: 'total',
        header: this.translateService.instant(`${BASE_TRANSLATE}.TABLE.TOTAL`),
        size: 10,
        cellConfig: {
          align: 'right',
        },
        cellComponentName: 'currencyCellComponent',
      },
    ];
  }

  protected onDisableContinueButton($event: boolean): void {
    this.disableContinueButton = $event;
  }

  protected onActiveContainerChanged($event: Event): void {
    this.paymentType = ($event as CustomEvent<PaymentType>).detail;
  }

  protected onFormChanged($event: Event): void {
    const formValues = ($event as CustomEvent<FormIbanValue>).detail;

    this.ibanValue = formValues.iban;
    this.ibanFormValid = formValues.valid;
  }
}
