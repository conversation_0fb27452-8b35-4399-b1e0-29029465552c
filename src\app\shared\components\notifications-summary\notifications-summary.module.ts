import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { TranslateModule } from '@ngx-translate/core';
import { SePanelModule } from 'se-ui-components-mf-lib';

import { NotificationsSummaryComponent } from './notifications-summary.component';

@NgModule({
  declarations: [NotificationsSummaryComponent],
  exports: [NotificationsSummaryComponent],
  imports: [CommonModule, TranslateModule.forChild(), SePanelModule],
})
export class NotificationsSummaryModule {}
