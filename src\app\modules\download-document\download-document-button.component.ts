import { Component, EventEmitter, Input, Output } from '@angular/core';
import { MenuItem } from 'primeng/api';
import { SeButton } from 'se-ui-components-mf-lib';
import { DocsService } from 'src/app/core/services/docs.service';

@Component({
  selector: 'app-download-document-button',
  template: `
    <se-button
      *ngIf="!showDownloadMenu"
      [size]="size"
      [btnTheme]="btnTheme"
      [disabled]="disabled"
      [icon]="icon"
      [iconPosition]="iconPosition"
      [type]="type"
      (onClick)="downloadDocument()"
    >
      {{ buttonLabel ?? 'SE_DOCUMENTS_MF.DOWNLOAD_BUTTON_LABEL' | translate }}
    </se-button>
    <se-button-dropdown
      *ngIf="showDownloadMenu"
      [items]="items"
      [buttonOptions]="buttonOptions"
    >
      {{  'SE_DOCUMENTS_MF.DOWNLOAD_MENU.LABEL' | translate }}
    </se-button-dropdown>
  `,
})
export class DownloadDocumentButtonComponent {
  @Input() size!: 'large' | 'default' | 'small';
  @Input() btnTheme!: 'primary' | 'secondary' | 'onlyText' | 'danger';
  @Input() disabled = false;
  @Input() icon = '';
  @Input() iconPosition!: 'right' | 'left';
  @Input() type!: string;
  @Input() idDocument!: string[];
  @Input() idJustificant!: string;
  @Input() buttonLabel?: string;
  @Input() showDownloadMenu = false
  @Input() emitDownloadDocument = false;
  @Input() items: MenuItem[] = []
  @Output() downloadDocumentEvent: EventEmitter<boolean> = new EventEmitter()

  buttonOptions: SeButton | undefined = {
    size: 'small',
    icon: "matExpandMoreOutline",
    iconPosition: 'right',
  };

  constructor(
    private docsService: DocsService,
  ) {
    console.log(
      'Webcomponent: Documents > DownloadDocumentButtonComponent > constructor'
    );
  }

  protected downloadDocument(): void {
    if (this.emitDownloadDocument) {
      this.downloadDocumentEvent.emit()
    } else {
      this.docsService.downloadDocument(this.idDocument, this.idJustificant);
    }
  }
}
