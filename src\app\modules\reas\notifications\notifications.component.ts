import { Component, OnInit } from '@angular/core';
import {
  Nullable,
  SeAuthService,
  SeUser,
  WebComponentsService,
} from 'se-ui-components-mf-lib';
import { environment } from '@environments/environment';
import {
  StorageService,
  PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA,
  CustomRouterService,
  LoginResponseService,
} from '@core/services';
import { AppRoutes, IdentificationType } from '@core/models';
import { ProceduresHeaderService } from '@app/shared/services/procedures-header';
import { ReasService } from '../services/reas.service';
import { TranslateService } from '@ngx-translate/core';
import { NotificationsEndpointsService } from './services/notifications-endpoints.service';
import {
  ContribuentInputData,
  ContribuentOutputData,
  DestinationOrgansT,
  OrigenEnum,
} from '@app/shared/models';

@Component({
  selector: 'app-notifications',
  templateUrl: './notifications.component.html',
  styleUrls: ['./notifications.component.scss'],
})
export class NotificationsComponent implements OnInit {
  receivingAgency: Nullable<string>;
  receivingAgencyCode: Nullable<DestinationOrgansT>;
  receivingAgencyLink!: string;

  updateAddressDuringProcedureOnly: boolean = false;

  // Contact Data
  contribuentWcUrlCss = environment.wcUrlContribuentCss;
  contribuentWcInput!: ContribuentInputData;

  // Constant
  PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA =
    PROCESSING_NOTIFICATION_ADDRESS_STORAGE_DATA;
  OrigenEnum = OrigenEnum;

  private user: SeUser | undefined;

  constructor(
    private seAuthService: SeAuthService,
    private wcDataService: StorageService,
    private wcService: WebComponentsService,
    private customRouter: CustomRouterService,
    private translateService: TranslateService,
    private procedureHeaderService: ProceduresHeaderService,
    private appelForReconsiderationService: ReasService,
    private loginService: LoginResponseService,
    private notificationsEndpointsService: NotificationsEndpointsService,
  ) {
    this.wcService.setWebComponentStyle(
      this.contribuentWcUrlCss,
      'contribuent',
    );
  }

  ngOnInit(): void {
    this.user = this.seAuthService.getSessionStorageUser();

    this.receivingAgencyLink = `https://economia.gencat.cat/${this.translateService.currentLang}/ambits-actuacio/via-economicoadministrativa/junta-tributs/`;

    this.checkNotificationElectronic();

    this.setContactDataInput();

    this.setupHeader();
  }

  private checkNotificationElectronic(): void {
    this.notificationsEndpointsService.getNotificationData().subscribe({
      next: (response) => {
        this.getIdentificationData(response?.content?.notificacioElectronica);
      },
    });
  }

  private getIdentificationData(
    isNotificationElectronic: boolean = false,
  ): void {
    const profileUser = this.wcDataService.profileUser;

    const isThirdPerson = profileUser !== IdentificationType.NOM_PROPI;
    const isLegalPerson =
      profileUser === IdentificationType.NOM_PROPI &&
      this.loginService.isLegalPerson();
    this.updateAddressDuringProcedureOnly =
      isThirdPerson || isLegalPerson || isNotificationElectronic;
  }

  private setContactDataInput(): void {
    this.contribuentWcInput = {
      isThirdPerson:
        this.wcDataService.profileUser !== IdentificationType.NOM_PROPI,
      nifPassiveSubject: this.user?.nif || '',
    };
  }

  private setupHeader(): void {
    if (!this.user?.nif) return;

    const vehiclesInfo =
      this.appelForReconsiderationService.getVehiclesSelectedInfo(
        this.user.nif,
      );

    this.procedureHeaderService.setupReasHeader(vehiclesInfo?.matriculas);
  }

  onContribuentOutput($event: Event): void {
    const contribuentData = ($event as CustomEvent)
      .detail as ContribuentOutputData;

    this.wcDataService.setNotificationsData({
      ...contribuentData,
      receivingAgency: this.receivingAgency,
      receivingAgencyCode: this.receivingAgencyCode,
    });
  }

  onGoBackButtonClick(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.REAS);
  }
  onContinueButtonClick(): void {
    this.customRouter.navigateByBaseUrl(AppRoutes.REAS_RESUM);
  }
}
